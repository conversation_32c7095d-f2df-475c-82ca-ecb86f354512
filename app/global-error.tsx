'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global Error:', error);
  }, [error]);

  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
      <body className="bg-background text-foreground antialiased">
        <div className="flex items-center justify-center min-h-screen p-4">
          <div className="w-full max-w-lg rounded-lg border border-border bg-card p-8 shadow-lg">
            <div className="flex justify-center mb-6">
              <div className="rounded-full bg-destructive/10 p-4">
                <AlertTriangle className="h-12 w-12 text-destructive" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-center mb-3">Critical System Error</h1>
            <p className="text-center text-muted-foreground mb-6 text-base">
              A critical error occurred while loading the application. This is usually a temporary issue.
            </p>

            <div className="bg-muted/50 p-4 rounded-lg mb-6">
              <p className="text-sm text-muted-foreground text-center">
                <strong>Error Details:</strong><br />
                {error.message || 'Unknown error occurred'}
              </p>
              {error.digest && (
                <p className="text-xs text-muted-foreground text-center mt-2">
                  Error ID: {error.digest}
                </p>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={reset} className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Try Again
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/'}
                className="flex items-center gap-2"
              >
                Go to Homepage
              </Button>
            </div>

            <p className="text-xs text-muted-foreground text-center mt-6">
              If this problem persists, please contact support with the error ID above.
            </p>
          </div>
        </div>
      </body>
    </html>
  );
}
