"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { XCircle, ArrowLeft, RefreshCw, CreditCard, Package, AlertTriangle } from "lucide-react";
import Link from "next/link";

export default function PaymentCancelPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [paymentType, setPaymentType] = useState<string>('');
  const [nodeId, setNodeId] = useState<string>('');
  const [reason, setReason] = useState<string>('');

  useEffect(() => {
    // Get payment details from URL params
    const type = searchParams.get('type') || 'node';
    const id = searchParams.get('nodeId') || '';
    const cancelReason = searchParams.get('reason') || 'user_cancelled';
    
    setPaymentType(type);
    setNodeId(id);
    setReason(cancelReason);
  }, [searchParams]);

  const getCancelMessage = () => {
    if (paymentType === 'subscription') {
      return {
        title: "Subscription Payment Cancelled",
        description: "Your subscription payment was cancelled. You can try again anytime or continue with the free plan.",
        icon: <CreditCard className="h-16 w-16 text-orange-500" />,
        badge: "Subscription",
        retryText: "Try Again",
        retryLink: "/marketplace?tab=subscription"
      };
    } else {
      return {
        title: "Node Purchase Cancelled",
        description: "Your node purchase was cancelled. The node is still available for purchase if you'd like to try again.",
        icon: <Package className="h-16 w-16 text-orange-500" />,
        badge: "Node Purchase",
        retryText: "Try Again",
        retryLink: nodeId ? `/marketplace/node/${nodeId}` : "/marketplace"
      };
    }
  };

  const getReason = () => {
    switch (reason) {
      case 'timeout':
        return 'Payment session expired';
      case 'insufficient_funds':
        return 'Insufficient funds';
      case 'card_declined':
        return 'Card was declined';
      case 'user_cancelled':
      default:
        return 'Payment was cancelled';
    }
  };

  const cancelInfo = getCancelMessage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="pb-4">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <XCircle className="h-20 w-20 text-orange-500" />
              <div className="absolute -top-2 -right-2">
                {cancelInfo.icon}
              </div>
            </div>
          </div>
          
          <Badge variant="secondary" className="mx-auto mb-2 w-fit bg-orange-100 text-orange-700 dark:bg-orange-950 dark:text-orange-400">
            {cancelInfo.badge}
          </Badge>
          
          <CardTitle className="text-2xl text-orange-700 dark:text-orange-400">
            {cancelInfo.title}
          </CardTitle>
          
          <CardDescription className="text-base mt-2">
            {cancelInfo.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="bg-orange-50 dark:bg-orange-950/30 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
            <div className="flex items-center justify-center gap-2 text-orange-700 dark:text-orange-400">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-medium">{getReason()}</span>
            </div>
            <p className="text-sm text-orange-600 dark:text-orange-500 mt-1">
              No charges were made to your account.
            </p>
          </div>

          <div className="space-y-3">
            <Button 
              asChild 
              className="w-full"
              size="lg"
            >
              <Link href={cancelInfo.retryLink}>
                <RefreshCw className="h-4 w-4 mr-2" />
                {cancelInfo.retryText}
              </Link>
            </Button>

            <div className="flex gap-2">
              <Button variant="outline" asChild className="flex-1">
                <Link href="/marketplace">
                  <Package className="h-4 w-4 mr-2" />
                  Marketplace
                </Link>
              </Button>
              
              <Button variant="outline" asChild className="flex-1">
                <Link href="/profile">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Profile
                </Link>
              </Button>
            </div>

            <Button variant="ghost" asChild className="w-full">
              <Link href="/dashboard">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Link>
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            Need help? Contact our support team.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
