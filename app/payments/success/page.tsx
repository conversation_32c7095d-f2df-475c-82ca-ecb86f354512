"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, ArrowRight, Download, CreditCard, Package } from "lucide-react";
import Link from "next/link";

export default function PaymentSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [paymentType, setPaymentType] = useState<string>('');
  const [nodeId, setNodeId] = useState<string>('');
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    // Get payment details from URL params
    const type = searchParams.get('type') || 'node';
    const id = searchParams.get('nodeId') || '';
    
    setPaymentType(type);
    setNodeId(id);

    // Auto-redirect countdown
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // Redirect based on payment type
          if (type === 'subscription') {
            router.push('/profile');
          } else if (id) {
            router.push(`/marketplace/node/${id}`);
          } else {
            router.push('/marketplace');
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [searchParams, router]);

  const getSuccessMessage = () => {
    if (paymentType === 'subscription') {
      return {
        title: "Subscription Activated!",
        description: "Your subscription has been successfully activated and you now have access to premium features.",
        icon: <CreditCard className="h-16 w-16 text-green-500" />,
        badge: "Subscription",
        redirectText: "Go to Profile"
      };
    } else {
      return {
        title: "Node Purchased Successfully!",
        description: "Your node has been purchased and is now available in your library. You can start using it in your workflows.",
        icon: <Package className="h-16 w-16 text-green-500" />,
        badge: "Node Purchase",
        redirectText: nodeId ? "View Node" : "Go to Marketplace"
      };
    }
  };

  const successInfo = getSuccessMessage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="pb-4">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <CheckCircle className="h-20 w-20 text-green-500" />
              <div className="absolute -top-2 -right-2">
                {successInfo.icon}
              </div>
            </div>
          </div>
          
          <Badge variant="secondary" className="mx-auto mb-2 w-fit">
            {successInfo.badge}
          </Badge>
          
          <CardTitle className="text-2xl text-green-700 dark:text-green-400">
            {successInfo.title}
          </CardTitle>
          
          <CardDescription className="text-base mt-2">
            {successInfo.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-center justify-center gap-2 text-green-700 dark:text-green-400">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">Payment Confirmed</span>
            </div>
            <p className="text-sm text-green-600 dark:text-green-500 mt-1">
              You will receive a confirmation email shortly.
            </p>
          </div>

          <div className="space-y-3">
            <Button 
              asChild 
              className="w-full"
              size="lg"
            >
              <Link href={
                paymentType === 'subscription' 
                  ? '/profile' 
                  : nodeId 
                    ? `/marketplace/node/${nodeId}` 
                    : '/marketplace'
              }>
                {successInfo.redirectText}
                <ArrowRight className="h-4 w-4 ml-2" />
              </Link>
            </Button>

            <div className="flex gap-2">
              <Button variant="outline" asChild className="flex-1">
                <Link href="/marketplace">
                  <Package className="h-4 w-4 mr-2" />
                  Marketplace
                </Link>
              </Button>
              
              <Button variant="outline" asChild className="flex-1">
                <Link href="/workflow-manager">
                  <Download className="h-4 w-4 mr-2" />
                  Workflows
                </Link>
              </Button>
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            Redirecting in {countdown} seconds...
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
