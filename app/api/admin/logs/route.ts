import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requirePermission, logActivity } from '@/lib/user-management/middleware';
import { ACTIVITY_ACTIONS } from '@/lib/user-management/permissions';

// GET /api/admin/logs - Get user activity logs
export async function GET(request: NextRequest) {
  return requirePermission('logs', 'read')(request, async (req, user) => {
    try {
      const { searchParams } = new URL(req.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '50');
      const userId = searchParams.get('userId');
      const action = searchParams.get('action');
      const resource = searchParams.get('resource');
      const startDate = searchParams.get('startDate');
      const endDate = searchParams.get('endDate');

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (userId) {
        where.userId = userId;
      }

      if (action) {
        where.action = { contains: action };
      }

      if (resource) {
        where.resource = resource;
      }

      if (startDate || endDate) {
        where.timestamp = {};
        if (startDate) {
          where.timestamp.gte = new Date(startDate);
        }
        if (endDate) {
          where.timestamp.lte = new Date(endDate);
        }
      }

      // Get logs with pagination
      const [logs, total] = await Promise.all([
        prisma.userLog.findMany({
          where,
          skip,
          take: limit,
          orderBy: { timestamp: 'desc' },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
              },
            },
          },
        }),
        prisma.userLog.count({ where }),
      ]);

      // Format response
      const formattedLogs = logs.map(log => ({
        id: log.id,
        action: log.action,
        resource: log.resource,
        resourceId: log.resourceId,
        details: log.details ? JSON.parse(log.details) : null,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        timestamp: log.timestamp,
        user: log.user,
      }));

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.API_CALLED,
        'logs',
        undefined,
        {
          action: 'list',
          filters: { userId, action, resource, startDate, endDate },
        },
        req
      );

      return NextResponse.json({
        logs: formattedLogs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      console.error('Error fetching logs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch logs' },
        { status: 500 }
      );
    }
  });
}

// GET /api/admin/logs/stats - Get log statistics
export async function POST(request: NextRequest) {
  return requirePermission('logs', 'read')(request, async (req, user) => {
    try {
      const body = await req.json();
      const { startDate, endDate, groupBy = 'day' } = body;

      const where: any = {};
      if (startDate || endDate) {
        where.timestamp = {};
        if (startDate) {
          where.timestamp.gte = new Date(startDate);
        }
        if (endDate) {
          where.timestamp.lte = new Date(endDate);
        }
      }

      // Get activity statistics
      const [
        totalLogs,
        uniqueUsers,
        topActions,
        topResources,
        recentActivity,
      ] = await Promise.all([
        // Total logs count
        prisma.userLog.count({ where }),

        // Unique users count
        prisma.userLog.findMany({
          where,
          select: { userId: true },
          distinct: ['userId'],
        }).then(users => users.length),

        // Top actions
        prisma.userLog.groupBy({
          by: ['action'],
          where,
          _count: { action: true },
          orderBy: { _count: { action: 'desc' } },
          take: 10,
        }),

        // Top resources
        prisma.userLog.groupBy({
          by: ['resource'],
          where: { ...where, resource: { not: null } },
          _count: { resource: true },
          orderBy: { _count: { resource: 'desc' } },
          take: 10,
        }),

        // Recent activity (last 24 hours)
        prisma.userLog.count({
          where: {
            ...where,
            timestamp: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
            },
          },
        }),
      ]);

      // Get activity timeline based on groupBy
      let timelineQuery = '';
      if (groupBy === 'hour') {
        timelineQuery = `
          SELECT
            strftime('%Y-%m-%d %H:00:00', timestamp) as period,
            COUNT(*) as count
          FROM UserLog
          WHERE timestamp >= ? AND timestamp <= ?
          GROUP BY strftime('%Y-%m-%d %H:00:00', timestamp)
          ORDER BY period
        `;
      } else {
        timelineQuery = `
          SELECT
            strftime('%Y-%m-%d', timestamp) as period,
            COUNT(*) as count
          FROM UserLog
          WHERE timestamp >= ? AND timestamp <= ?
          GROUP BY strftime('%Y-%m-%d', timestamp)
          ORDER BY period
        `;
      }

      const timelineStart = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const timelineEnd = endDate || new Date();

      const timeline = await prisma.$queryRawUnsafe(
        timelineQuery,
        timelineStart,
        timelineEnd
      ) as { period: string; count: number }[];

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.API_CALLED,
        'logs',
        undefined,
        { action: 'stats', groupBy, startDate, endDate },
        req
      );

      return NextResponse.json({
        stats: {
          totalLogs,
          uniqueUsers,
          recentActivity,
          topActions: topActions.map(item => ({
            action: item.action,
            count: item._count.action,
          })),
          topResources: topResources.map(item => ({
            resource: item.resource,
            count: item._count.resource,
          })),
          timeline,
        },
      });
    } catch (error) {
      console.error('Error fetching log stats:', error);
      return NextResponse.json(
        { error: 'Failed to fetch log statistics' },
        { status: 500 }
      );
    }
  });
}
