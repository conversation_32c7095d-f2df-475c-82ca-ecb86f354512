import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';
import { SYSTEM_PERMISSIONS, SYSTEM_ROLES } from '@/lib/user-management/permissions';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user?.isAdmin && !user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    console.log('🌱 Seeding developer permissions...');

    // 1. Create all system permissions
    const createdPermissions = new Map<string, string>();

    for (const perm of SYSTEM_PERMISSIONS) {
      const permission = await prisma.permission.upsert({
        where: {
          name: `${perm.resource}:${perm.action}`,
        },
        update: {
          displayName: perm.displayName,
          description: perm.description,
        },
        create: {
          name: `${perm.resource}:${perm.action}`,
          displayName: perm.displayName,
          description: perm.description,
          resource: perm.resource,
          action: perm.action,
          isSystem: true,
        },
      });

      createdPermissions.set(`${perm.resource}:${perm.action}`, permission.id);
    }

    console.log(`✅ Created/updated ${createdPermissions.size} permissions`);

    // 2. Create all system roles
    const createdRoles = new Map<string, string>();

    for (const roleData of SYSTEM_ROLES) {
      const role = await prisma.role.upsert({
        where: {
          name: roleData.name,
        },
        update: {
          displayName: roleData.displayName,
          description: roleData.description,
        },
        create: {
          name: roleData.name,
          displayName: roleData.displayName,
          description: roleData.description,
          isSystem: true,
        },
      });

      createdRoles.set(roleData.name, role.id);

      // Clear existing permissions for this role
      await prisma.rolePermission.deleteMany({
        where: { roleId: role.id },
      });

      // Assign permissions to role
      for (const permissionName of roleData.permissions) {
        const permissionId = createdPermissions.get(permissionName);
        if (permissionId) {
          await prisma.rolePermission.create({
            data: {
              roleId: role.id,
              permissionId,
            },
          });
        }
      }
    }

    console.log(`✅ Created/updated ${createdRoles.size} roles`);

    // 3. Get current role counts
    const roleStats = await Promise.all(
      SYSTEM_ROLES.map(async (roleData) => {
        const role = await prisma.role.findUnique({
          where: { name: roleData.name },
          include: {
            permissions: true,
            userRoles: {
              include: {
                user: {
                  select: { id: true, name: true, email: true }
                }
              }
            }
          }
        });

        return {
          name: roleData.name,
          displayName: roleData.displayName,
          permissionCount: role?.permissions.length || 0,
          userCount: role?.userRoles.length || 0,
          users: role?.userRoles.map(ur => ur.user) || []
        };
      })
    );

    return NextResponse.json({
      message: 'Developer permissions seeded successfully',
      stats: {
        permissionsCreated: createdPermissions.size,
        rolesCreated: createdRoles.size,
        roles: roleStats
      }
    });

  } catch (error) {
    console.error('Error seeding developer permissions:', error);
    return NextResponse.json(
      {
        error: 'Failed to seed developer permissions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
