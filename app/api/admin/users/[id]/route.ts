import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requirePermission, logActivity, validateRequestData } from '@/lib/user-management/middleware';
import { ACTIVITY_ACTIONS } from '@/lib/user-management/permissions';
import bcrypt from 'bcryptjs';

// GET /api/admin/users/[id] - Get user details
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  return requirePermission('users', 'read')(request, async (req, user) => {
    try {
      const targetUser = await prisma.user.findUnique({
        where: { id: params.id },
        include: {
          userRoles: {
            include: {
              role: {
                include: {
                  permissions: {
                    include: {
                      permission: true,
                    },
                  },
                },
              },
              assignedByUser: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              workflows: true,
              userLogs: true,
              loginHistory: true,
            },
          },
        },
      });

      if (!targetUser) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      // Format response
      const formattedUser = {
        id: targetUser.id,
        email: targetUser.email,
        username: targetUser.username,
        name: targetUser.name,
        bio: targetUser.bio,
        avatar: targetUser.avatar,
        status: targetUser.status,
        isAdmin: targetUser.isAdmin,
        isSuperAdmin: targetUser.isSuperAdmin,
        emailVerified: targetUser.emailVerified,
        lastLoginAt: targetUser.lastLoginAt,
        lastActiveAt: targetUser.lastActiveAt,
        loginCount: targetUser.loginCount,
        createdAt: targetUser.createdAt,
        updatedAt: targetUser.updatedAt,
        createdBy: targetUser.createdBy,
        roles: targetUser.userRoles.map(ur => ({
          id: ur.role.id,
          name: ur.role.name,
          displayName: ur.role.displayName,
          description: ur.role.description,
          assignedAt: ur.assignedAt,
          assignedBy: ur.assignedByUser,
          expiresAt: ur.expiresAt,
          permissions: ur.role.permissions.map(rp => rp.permission),
        })),
        stats: {
          workflowCount: targetUser._count.workflows,
          activityCount: targetUser._count.userLogs,
          loginCount: targetUser._count.loginHistory,
        },
      };

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.API_CALLED,
        'users',
        params.id,
        { action: 'view' },
        req
      );

      return NextResponse.json({ user: formattedUser });
    } catch (error) {
      console.error('Error fetching user:', error);
      return NextResponse.json(
        { error: 'Failed to fetch user' },
        { status: 500 }
      );
    }
  });
}

// PUT /api/admin/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  return requirePermission('users', 'update')(request, async (req, user) => {
    try {
      const body = await req.json();

      const validation = validateRequestData(
        body,
        [],
        ['name', 'bio', 'avatar', 'status', 'isAdmin', 'password', 'roles']
      );

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { name, bio, avatar, status, isAdmin, password, roles } = validation.validatedData!;

      // Check if target user exists
      const targetUser = await prisma.user.findUnique({
        where: { id: params.id },
      });

      if (!targetUser) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      // Prevent non-super-admin from modifying super-admin users
      if (targetUser.isSuperAdmin && !user.isSuperAdmin) {
        return NextResponse.json(
          { error: 'Cannot modify super admin user' },
          { status: 403 }
        );
      }

      // Prepare update data
      const updateData: any = {};

      if (name !== undefined) updateData.name = name;
      if (bio !== undefined) updateData.bio = bio;
      if (avatar !== undefined) updateData.avatar = avatar;
      if (status !== undefined) updateData.status = status;
      if (isAdmin !== undefined && user.isSuperAdmin) updateData.isAdmin = isAdmin;

      // Hash new password if provided
      if (password) {
        updateData.password = await bcrypt.hash(password as string, 12);
      }

      // Update user
      const updatedUser = await prisma.user.update({
        where: { id: params.id },
        data: updateData,
      });

      // Update roles if provided
      if (roles && Array.isArray(roles)) {
        // Remove existing roles
        await prisma.userRole.deleteMany({
          where: { userId: params.id },
        });

        // Add new roles
        for (const roleId of roles) {
          await prisma.userRole.create({
            data: {
              userId: params.id,
              roleId: roleId as string,
              assignedBy: user.id,
            },
          });
        }
      }

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.USER_UPDATED,
        'users',
        params.id,
        {
          changes: updateData,
          roles: roles || 'unchanged',
        },
        req
      );

      return NextResponse.json({
        message: 'User updated successfully',
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          username: updatedUser.username,
          name: updatedUser.name,
          status: updatedUser.status,
          isAdmin: updatedUser.isAdmin,
          updatedAt: updatedUser.updatedAt,
        },
      });
    } catch (error) {
      console.error('Error updating user:', error);
      return NextResponse.json(
        { error: 'Failed to update user' },
        { status: 500 }
      );
    }
  });
}

// DELETE /api/admin/users/[id] - Delete user
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  return requirePermission('users', 'delete')(request, async (req, user) => {
    try {
      // Check if target user exists
      const targetUser = await prisma.user.findUnique({
        where: { id: params.id },
      });

      if (!targetUser) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      // Prevent deletion of super admin users
      if (targetUser.isSuperAdmin) {
        return NextResponse.json(
          { error: 'Cannot delete super admin user' },
          { status: 403 }
        );
      }

      // Prevent users from deleting themselves
      if (targetUser.id === user.id) {
        return NextResponse.json(
          { error: 'Cannot delete your own account' },
          { status: 403 }
        );
      }

      // Delete user (cascade will handle related records)
      await prisma.user.delete({
        where: { id: params.id },
      });

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.USER_DELETED,
        'users',
        params.id,
        {
          deletedUser: {
            email: targetUser.email,
            name: targetUser.name,
          },
        },
        req
      );

      return NextResponse.json({
        message: 'User deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      return NextResponse.json(
        { error: 'Failed to delete user' },
        { status: 500 }
      );
    }
  });
}
