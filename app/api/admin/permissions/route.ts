import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requirePermission, logActivity } from '@/lib/user-management/middleware';
import { ACTIVITY_ACTIONS } from '@/lib/user-management/permissions';

// GET /api/admin/permissions - List all permissions
export async function GET(request: NextRequest) {
  return requirePermission('roles', 'read')(request, async (req, user) => {
    try {
      const { searchParams } = new URL(req.url);
      const resource = searchParams.get('resource');

      const where = resource ? { resource } : {};

      const permissions = await prisma.permission.findMany({
        where,
        include: {
          _count: {
            select: {
              rolePermissions: true,
            },
          },
        },
        orderBy: [
          { resource: 'asc' },
          { action: 'asc' },
        ],
      });

      const formattedPermissions = permissions.map(permission => ({
        id: permission.id,
        name: permission.name,
        displayName: permission.displayName,
        description: permission.description,
        resource: permission.resource,
        action: permission.action,
        isSystem: permission.isSystem,
        roleCount: permission._count.rolePermissions,
      }));

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.API_CALLED,
        'permissions',
        undefined,
        { action: 'list', resource },
        req
      );

      return NextResponse.json({
        permissions: formattedPermissions,
        total: permissions.length,
      });
    } catch (error) {
      console.error('Error fetching permissions:', error);
      return NextResponse.json(
        { error: 'Failed to fetch permissions' },
        { status: 500 }
      );
    }
  });
}
