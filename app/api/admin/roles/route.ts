import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requirePermission, logActivity, validateRequestData } from '@/lib/user-management/middleware';
import { ACTIVITY_ACTIONS } from '@/lib/user-management/permissions';

// GET /api/admin/roles - List all roles
export async function GET(request: NextRequest) {
  return requirePermission('roles', 'read')(request, async (req, user) => {
    try {
      const roles = await prisma.role.findMany({
        include: {
          permissions: {
            include: {
              permission: true,
            },
          },
          _count: {
            select: {
              userRoles: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
      });

      const formattedRoles = roles.map(role => ({
        id: role.id,
        name: role.name,
        displayName: role.displayName,
        description: role.description,
        isSystem: role.isSystem,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
        permissions: role.permissions.map(rp => rp.permission),
        userCount: role._count.userRoles,
      }));

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.API_CALLED,
        'roles',
        undefined,
        { action: 'list' },
        req
      );

      return NextResponse.json({ roles: formattedRoles });
    } catch (error) {
      console.error('Error fetching roles:', error);
      return NextResponse.json(
        { error: 'Failed to fetch roles' },
        { status: 500 }
      );
    }
  });
}

// POST /api/admin/roles - Create new role
export async function POST(request: NextRequest) {
  return requirePermission('roles', 'create')(request, async (req, user) => {
    try {
      const body = await req.json();
      
      const validation = validateRequestData(
        body,
        ['name', 'displayName'],
        ['description', 'permissions']
      );

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { name, displayName, description, permissions } = validation.validatedData!;

      // Check if role already exists
      const existingRole = await prisma.role.findUnique({
        where: { name: name as string },
      });

      if (existingRole) {
        return NextResponse.json(
          { error: 'Role with this name already exists' },
          { status: 400 }
        );
      }

      // Create role
      const newRole = await prisma.role.create({
        data: {
          name: name as string,
          displayName: displayName as string,
          description: description as string,
          isSystem: false,
        },
      });

      // Assign permissions if provided
      if (permissions && Array.isArray(permissions)) {
        for (const permissionId of permissions) {
          await prisma.rolePermission.create({
            data: {
              roleId: newRole.id,
              permissionId: permissionId as string,
            },
          });
        }
      }

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.ROLE_CREATED,
        'roles',
        newRole.id,
        { 
          name: newRole.name,
          displayName: newRole.displayName,
          permissions: permissions || [],
        },
        req
      );

      return NextResponse.json({
        message: 'Role created successfully',
        role: {
          id: newRole.id,
          name: newRole.name,
          displayName: newRole.displayName,
          description: newRole.description,
          createdAt: newRole.createdAt,
        },
      });
    } catch (error) {
      console.error('Error creating role:', error);
      return NextResponse.json(
        { error: 'Failed to create role' },
        { status: 500 }
      );
    }
  });
}
