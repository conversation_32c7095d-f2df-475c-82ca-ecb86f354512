import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user?.isAdmin && !user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { userEmail } = await request.json();

    if (!userEmail) {
      return NextResponse.json({ error: 'User email is required' }, { status: 400 });
    }

    // Find the user to assign the role to
    const targetUser = await prisma.user.findUnique({
      where: { email: userEmail },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Find the developer role
    const developerRole = await prisma.role.findUnique({
      where: { name: 'developer' }
    });

    if (!developerRole) {
      return NextResponse.json({ error: 'Developer role not found. Please seed permissions first.' }, { status: 404 });
    }

    // Check if user already has the developer role
    const hasRole = targetUser.userRoles.some(ur => ur.role.name === 'developer');

    if (hasRole) {
      return NextResponse.json({
        message: 'User already has developer role',
        user: {
          name: targetUser.name,
          email: targetUser.email,
          roles: targetUser.userRoles.map(ur => ur.role.displayName)
        }
      });
    }

    // Assign the developer role
    await prisma.userRole.create({
      data: {
        userId: targetUser.id,
        roleId: developerRole.id
      }
    });

    // Get updated user with roles
    const updatedUser = await prisma.user.findUnique({
      where: { id: targetUser.id },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Developer role assigned successfully',
      user: {
        name: updatedUser?.name,
        email: updatedUser?.email,
        roles: updatedUser?.userRoles.map(ur => ur.role.displayName) || []
      }
    });

  } catch (error) {
    console.error('Error assigning developer role:', error);
    return NextResponse.json(
      {
        error: 'Failed to assign developer role',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
