import { NextRequest, NextResponse } from 'next/server';
import { seedUserManagementSystem, resetUserManagementSystem, checkUserManagementHealth } from '@/lib/user-management/seed';

// POST /api/admin/system/init - Initialize User Management System
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action = 'seed' } = body;

    let result;

    switch (action) {
      case 'seed':
        result = await seedUserManagementSystem();
        break;
      case 'reset':
        await resetUserManagementSystem();
        result = await seedUserManagementSystem();
        break;
      case 'health':
        result = await checkUserManagementHealth();
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: seed, reset, or health' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message: `User Management System ${action} completed successfully`,
      result,
    });
  } catch (error) {
    console.error(`Error during system ${request.json().then(b => b.action || 'seed')}:`, error);
    return NextResponse.json(
      { 
        error: 'Failed to initialize User Management System',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/system/init - Check system health
export async function GET() {
  try {
    const health = await checkUserManagementHealth();
    return NextResponse.json(health);
  } catch (error) {
    console.error('Error checking system health:', error);
    return NextResponse.json(
      { 
        healthy: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
