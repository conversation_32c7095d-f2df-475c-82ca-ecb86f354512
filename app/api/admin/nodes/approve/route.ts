import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    const userId = (session?.user as any)?.id;
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has node approval permissions
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        isAdmin: true,
        isSuperAdmin: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions (simplified check for now)
    if (!user.isAdmin && !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Admin access required to approve nodes' },
        { status: 403 }
      );
    }

    const { nodeId, action, rejectionReason } = await request.json();

    if (!nodeId || !action) {
      return NextResponse.json(
        { error: 'Node ID and action are required' },
        { status: 400 }
      );
    }

    if (!['approve', 'reject', 'pending'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be "approve", "reject", or "pending"' },
        { status: 400 }
      );
    }

    if (action === 'reject' && !rejectionReason) {
      return NextResponse.json(
        { error: 'Rejection reason is required when rejecting a node' },
        { status: 400 }
      );
    }

    // Find the node
    const node = await prisma.nodePlugin.findUnique({
      where: { id: nodeId },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!node) {
      return NextResponse.json(
        { error: 'Node not found' },
        { status: 404 }
      );
    }

    // Validate status transitions
    if (action === 'pending') {
      // Allow changing from approved or rejected back to pending
      if (!['approved', 'rejected'].includes(node.approvalStatus)) {
        return NextResponse.json(
          { error: `Cannot change status from ${node.approvalStatus} to pending` },
          { status: 400 }
        );
      }
    } else {
      // For approve/reject actions, node must be pending
      if (node.approvalStatus !== 'pending') {
        return NextResponse.json(
          { error: `Node is already ${node.approvalStatus}. Only pending nodes can be approved or rejected.` },
          { status: 400 }
        );
      }
    }

    // Update node approval status
    const updatedNode = await prisma.nodePlugin.update({
      where: { id: nodeId },
      data: {
        approvalStatus: action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'pending',
        approvedAt: action === 'approve' ? new Date() : null,
        approvedById: action === 'approve' ? userId : null,
        rejectionReason: action === 'reject' ? rejectionReason : action === 'pending' ? null : undefined
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Log the approval action
    await prisma.userLog.create({
      data: {
        userId: userId,
        action: action === 'approve' ? 'approve_node' : action === 'reject' ? 'reject_node' : 'recall_node_to_pending',
        resource: 'marketplace',
        resourceId: nodeId,
        details: JSON.stringify({
          nodeName: node.name,
          nodeVersion: node.version,
          authorId: node.authorId,
          authorName: node.author.name,
          previousStatus: node.approvalStatus,
          newStatus: action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'pending',
          rejectionReason: action === 'reject' ? rejectionReason : undefined
        })
      }
    });

    // TODO: Send notification email to node author
    // This would be implemented with your email service
    console.log(`Node ${action}d:`, {
      nodeId,
      nodeName: node.name,
      authorEmail: node.author.email,
      action,
      rejectionReason: action === 'reject' ? rejectionReason : undefined
    });

    return NextResponse.json({
      success: true,
      message: action === 'pending' ? 'Node recalled to pending successfully' : `Node ${action}d successfully`,
      node: {
        id: updatedNode.id,
        name: updatedNode.name,
        version: updatedNode.version,
        approvalStatus: updatedNode.approvalStatus,
        approvedAt: updatedNode.approvedAt,
        approvedBy: updatedNode.approvedBy,
        rejectionReason: updatedNode.rejectionReason,
        author: updatedNode.author
      }
    });

  } catch (error) {
    console.error('Node approval error:', error);
    return NextResponse.json(
      { error: 'Failed to process node approval' },
      { status: 500 }
    );
  }
}
