import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has node review permissions
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        isAdmin: true,
        isSuperAdmin: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions (simplified check for now)
    if (!user.isAdmin && !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Admin access required to review nodes' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'pending';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Validate status
    if (!['pending', 'approved', 'rejected', 'all'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be: pending, approved, rejected, or all' },
        { status: 400 }
      );
    }

    // Build where clause
    const whereClause: any = {};
    if (status !== 'all') {
      whereClause.approvalStatus = status;
    }

    // Get nodes with pagination
    const [nodes, totalCount] = await Promise.all([
      prisma.nodePlugin.findMany({
        where: whereClause,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true
            }
          },
          approvedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          nodeCodes: {
            select: {
              id: true,
              version: true,
              createdAt: true
            }
          },
          _count: {
            select: {
              installedNodes: true,
              reviews: true
            }
          }
        },
        orderBy: [
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.nodePlugin.count({
        where: whereClause
      })
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Get summary statistics
    const stats = await prisma.nodePlugin.groupBy({
      by: ['approvalStatus'],
      _count: {
        id: true
      }
    });

    const statusCounts = {
      pending: 0,
      approved: 0,
      rejected: 0
    };

    stats.forEach(stat => {
      if (stat.approvalStatus in statusCounts) {
        statusCounts[stat.approvalStatus as keyof typeof statusCounts] = stat._count.id;
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        nodes: nodes.map(node => ({
          id: node.id,
          name: node.name,
          version: node.version,
          description: node.description,
          category: node.category,
          tier: node.tier,
          price: node.price,
          icon: node.icon,
          tags: JSON.parse(node.tags || '[]'),
          approvalStatus: node.approvalStatus,
          approvedAt: node.approvedAt,
          rejectionReason: node.rejectionReason,
          createdAt: node.createdAt,
          lastUpdated: node.lastUpdated,
          author: node.author,
          approvedBy: node.approvedBy,
          hasCode: node.nodeCodes.length > 0,
          installCount: node._count.installedNodes,
          reviewCount: node._count.reviews,
          rating: node.rating
        })),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage,
          hasPrevPage
        },
        stats: {
          ...statusCounts,
          total: totalCount
        }
      }
    });

  } catch (error) {
    console.error('Get pending nodes error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pending nodes' },
      { status: 500 }
    );
  }
}
