/**
 * Payment Cancel API Route
 * Handles cancelled payment redirects from Doku
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');
    const invoiceNumber = searchParams.get('invoice');
    const reason = searchParams.get('reason') || 'user_cancelled';

    // Log the cancelled payment redirect
    console.log('Payment cancel redirect:', {
      sessionId,
      invoiceNumber,
      reason
    });

    // Find payment record if we have invoice number
    if (invoiceNumber) {
      const payment = await prisma.payment.findFirst({
        where: {
          invoiceNumber: invoiceNumber
        }
      });

      if (payment) {
        // Update payment status to cancelled
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status: 'cancelled',
            updatedAt: new Date(),
            metadata: JSON.stringify({
              ...JSON.parse(payment.metadata as string || '{}'),
              cancellationReason: reason,
              cancelledAt: new Date().toISOString()
            })
          }
        });

        // Determine redirect URL based on payment type
        const metadata = payment.metadata ? JSON.parse(payment.metadata as string) : {};
        
        if (metadata.type === 'subscription') {
          // Redirect to marketplace subscription tab for subscription payments
          return NextResponse.redirect(new URL('/marketplace?tab=subscription&payment=cancelled', request.url));
        } else if (metadata.nodeId) {
          // Redirect to node detail page for node purchases
          return NextResponse.redirect(new URL(`/marketplace/node/${metadata.nodeId}?payment=cancelled`, request.url));
        }
      }
    }

    // Default redirect to marketplace with cancelled message
    return NextResponse.redirect(new URL('/marketplace?payment=cancelled', request.url));

  } catch (error: any) {
    console.error('Error handling payment cancellation:', error);
    
    // Redirect to marketplace with error message
    return NextResponse.redirect(new URL('/marketplace?payment=error', request.url));
  }
}

// Handle POST requests (some payment gateways use POST for redirects)
export async function POST(request: NextRequest) {
  return GET(request);
}
