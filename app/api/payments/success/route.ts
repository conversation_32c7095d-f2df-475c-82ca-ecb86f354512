/**
 * Payment Success API Route
 * Handles successful payment redirects from Doku
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');
    const invoiceNumber = searchParams.get('invoice');
    const transactionId = searchParams.get('transaction_id');

    // Log the successful payment redirect
    console.log('Payment success redirect:', {
      sessionId,
      invoiceNumber,
      transactionId
    });

    // Find payment record if we have invoice number
    if (invoiceNumber) {
      const payment = await prisma.payment.findFirst({
        where: {
          invoiceNumber: invoiceNumber
        }
      });

      if (payment) {
        // Update payment status to processing (webhook will set to completed)
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status: 'processing',
            updatedAt: new Date()
          }
        });

        // Determine redirect URL based on payment type
        const metadata = payment.metadata ? JSON.parse(payment.metadata as string) : {};
        
        if (metadata.type === 'subscription') {
          // Redirect to profile for subscription payments
          return NextResponse.redirect(new URL('/profile?payment=success&type=subscription', request.url));
        } else if (metadata.nodeId) {
          // Redirect to node detail page for node purchases
          return NextResponse.redirect(new URL(`/marketplace/node/${metadata.nodeId}?payment=success`, request.url));
        }
      }
    }

    // Default redirect to marketplace with success message
    return NextResponse.redirect(new URL('/marketplace?payment=success', request.url));

  } catch (error: any) {
    console.error('Error handling payment success:', error);
    
    // Redirect to marketplace with error message
    return NextResponse.redirect(new URL('/marketplace?payment=error', request.url));
  }
}

// Handle POST requests (some payment gateways use POST for redirects)
export async function POST(request: NextRequest) {
  return GET(request);
}
