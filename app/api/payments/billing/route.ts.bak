/**
 * Billing Dashboard API Route
 * Provides billing information, invoices, and payment methods
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { getStripe, validateStripeConfig } from '@/lib/stripe/config';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        stripeCustomerId: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get current subscription
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: { in: ['active', 'trialing', 'past_due'] }
      },
      orderBy: { createdAt: 'desc' }
    });

    let billingData: any = {
      subscription: subscription ? {
        id: subscription.id,
        planId: subscription.planId,
        status: subscription.status,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd
      } : null,
      invoices: [],
      paymentMethods: [],
      upcomingInvoice: null
    };

    // If user has Stripe customer ID and it's not a free plan, get Stripe data
    if (user.stripeCustomerId && subscription?.planId !== 'free') {
      const configValidation = validateStripeConfig();
      if (configValidation.isValid) {
        const stripe = getStripe();

        try {
          // Get invoices
          const invoices = await stripe.invoices.list({
            customer: user.stripeCustomerId,
            limit: 10
          });

          billingData.invoices = invoices.data.map(invoice => ({
            id: invoice.id,
            number: invoice.number,
            status: invoice.status,
            amount: invoice.amount_paid,
            currency: invoice.currency,
            created: invoice.created,
            hostedInvoiceUrl: invoice.hosted_invoice_url,
            invoicePdf: invoice.invoice_pdf,
            description: invoice.lines.data[0]?.description || 'Subscription'
          }));

          // Get payment methods
          const paymentMethods = await stripe.paymentMethods.list({
            customer: user.stripeCustomerId,
            type: 'card'
          });

          // Get default payment method
          const customer = await stripe.customers.retrieve(user.stripeCustomerId);
          const defaultSource = typeof customer === 'object' && 'default_source' in customer
            ? customer.default_source
            : null;

          billingData.paymentMethods = paymentMethods.data.map(pm => ({
            id: pm.id,
            type: pm.type,
            card: pm.card ? {
              brand: pm.card.brand,
              last4: pm.card.last4,
              expMonth: pm.card.exp_month,
              expYear: pm.card.exp_year
            } : null,
            isDefault: pm.id === defaultSource
          }));

          // TODO: Fix Stripe API method for retrieving upcoming invoices
          // Commented out until we can resolve the correct Stripe API method

        } catch (error) {
          console.error('Error fetching Stripe billing data:', error);
          // Continue with local data only
        }
      }
    }

    return NextResponse.json(billingData);

  } catch (error: any) {
    console.error('Error fetching billing data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch billing data' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
