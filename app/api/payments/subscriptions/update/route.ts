/**
 * Subscription Update API Route
 * Handles subscription plan changes (upgrade/downgrade)
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { PaymentService } from '@/lib/doku/payment-service';
import { subscriptionPlans, validateDokuConfig } from '@/lib/doku/config';
import { prisma } from '@/lib/prisma';

export async function PUT(request: NextRequest) {
  try {
    // Validate Doku configuration
    const configValidation = validateDokuConfig();
    if (!configValidation.isValid) {
      return NextResponse.json(
        {
          error: 'Payment system not configured',
          details: configValidation.errors
        },
        { status: 500 }
      );
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    const { newPlanId, prorationBehavior = 'create_prorations' } = body;

    // Validate new plan ID
    if (!newPlanId || !subscriptionPlans[newPlanId as keyof typeof subscriptionPlans]) {
      return NextResponse.json(
        { error: 'Invalid plan ID' },
        { status: 400 }
      );
    }

    // Get user's current subscription
    const currentSubscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: { in: ['active', 'trialing'] }
      },
      orderBy: { createdAt: 'desc' }
    });

    if (!currentSubscription) {
      return NextResponse.json(
        { error: 'No active subscription found' },
        { status: 404 }
      );
    }

    // Check if it's the same plan
    if (currentSubscription.planId === newPlanId) {
      return NextResponse.json(
        { error: 'Already subscribed to this plan' },
        { status: 400 }
      );
    }

    // Handle free plan downgrade
    if (newPlanId === 'free') {
      // Cancel current subscription and create free plan
      const paymentService = new PaymentService();

      // Cancel current subscription at period end (only if it has a Doku subscription ID)
      if (currentSubscription.dokuSubscriptionId) {
        await paymentService.cancelSubscription({
          subscriptionId: currentSubscription.dokuSubscriptionId,
          immediately: false,
          cancellationReason: 'Downgraded to free plan'
        });
      }

      // Create free plan subscription to start after current period ends
      const freeSubscription = await prisma.subscription.create({
        data: {
          id: `free_${user.id}_${Date.now()}`,
          userId: user.id,
          dokuSubscriptionId: `free_${user.id}_${Date.now()}`,
          dokuCustomerId: currentSubscription.dokuCustomerId,
          status: 'active',
          planId: 'free',
          currentPeriodStart: currentSubscription.currentPeriodEnd,
          currentPeriodEnd: new Date(currentSubscription.currentPeriodEnd.getTime() + 365 * 24 * 60 * 60 * 1000),
          cancelAtPeriodEnd: false,
          metadata: JSON.stringify({
            planId: 'free',
            type: 'downgrade',
            previousPlanId: currentSubscription.planId
          })
        }
      });

      return NextResponse.json({
        subscription: {
          id: freeSubscription.id,
          status: 'scheduled',
          planId: freeSubscription.planId,
          currentPeriodStart: Math.floor(freeSubscription.currentPeriodStart.getTime() / 1000),
          currentPeriodEnd: Math.floor(freeSubscription.currentPeriodEnd.getTime() / 1000),
          cancelAtPeriodEnd: false,
          message: 'Subscription will downgrade to free plan at the end of current billing period'
        }
      });
    }

    // Handle paid plan upgrade/downgrade
    const paymentService = new PaymentService();

    if (!currentSubscription.dokuSubscriptionId) {
      return NextResponse.json(
        { error: 'No active subscription found to update' },
        { status: 400 }
      );
    }

    const updatedSubscription = await paymentService.updateSubscription({
      subscriptionId: currentSubscription.dokuSubscriptionId,
      newPlanId,
      prorationBehavior
    });

    return NextResponse.json({
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        planId: newPlanId,
        message: 'Subscription updated successfully'
      }
    });

  } catch (error: any) {
    console.error('Error updating subscription:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update subscription' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
