/**
 * Doku Webhooks API Route
 * Handles Doku webhook notifications for payment processing
 */

import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { PaymentService } from '@/lib/doku/payment-service';
import { validateWebhookSignature, validateQrisSignature } from '@/lib/doku/signature';
import { DokuNotification } from '@/lib/doku/types';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headersList = await headers();

    // Check if this is a QRIS notification (different format)
    const contentType = headersList.get('content-type');
    if (contentType?.includes('application/x-www-form-urlencoded')) {
      return await handleQrisNotification(request, body);
    }

    // Handle standard Doku JSON notification
    const timestamp = headersList.get('request-timestamp') || headersList.get('Request-Timestamp');
    const signature = headersList.get('signature') || headersList.get('Signature');

    if (!timestamp || !signature) {
      console.error('Missing required headers for Doku notification');
      return NextResponse.json(
        { error: 'Missing required headers' },
        { status: 400 }
      );
    }

    // Verify webhook signature
    const isValidSignature = validateWebhookSignature(body, timestamp, signature);
    if (!isValidSignature) {
      console.error('Invalid webhook signature');
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Parse notification data
    let notification: DokuNotification;
    try {
      notification = JSON.parse(body);
    } catch (err) {
      console.error('Invalid JSON in webhook body:', err);
      return NextResponse.json(
        { error: 'Invalid JSON' },
        { status: 400 }
      );
    }

    console.log(`Received Doku notification for invoice: ${notification.order.invoice_number}`);

    // Handle the notification
    const paymentService = new PaymentService();
    await paymentService.handleNotification(notification);

    return NextResponse.json({ received: true });

  } catch (error: any) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

/**
 * Handle QRIS notification (different format from standard Doku notifications)
 */
async function handleQrisNotification(request: NextRequest, body: string) {
  try {
    // Parse form data
    const formData = new URLSearchParams(body);
    const qrisData = {
      TXNDATE: formData.get('TXNDATE') || '',
      TXNSTATUS: formData.get('TXNSTATUS') || '',
      ACQUIRER: formData.get('ACQUIRER') || '',
      MERCHANTPAN: formData.get('MERCHANTPAN') || '',
      ISSUERNAME: formData.get('ISSUERNAME') || '',
      ISSUERID: formData.get('ISSUERID') || '',
      CONVENIENCEFEE: formData.get('CONVENIENCEFEE') || '',
      INVOICE: formData.get('INVOICE') || '',
      AMOUNT: formData.get('AMOUNT') || '',
      TRANSACTIONID: formData.get('TRANSACTIONID') || '',
      WORDS: formData.get('WORDS') || '',
      CUSTOMERPAN: formData.get('CUSTOMERPAN') || '',
      TERMINALID: formData.get('TERMINALID') || '',
      ORIGIN: formData.get('ORIGIN') || '',
      REFERENCEID: formData.get('REFERENCEID') || ''
    };

    // Validate QRIS signature
    const sharedKey = process.env.DOKU_SECRET_KEY || '';
    const isValidSignature = validateQrisSignature(
      qrisData.ISSUERID,
      qrisData.TXNDATE,
      qrisData.MERCHANTPAN,
      qrisData.INVOICE,
      qrisData.WORDS,
      sharedKey
    );

    if (!isValidSignature) {
      console.error('Invalid QRIS signature');
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Convert QRIS data to standard notification format
    const notification: DokuNotification = {
      order: {
        amount: qrisData.AMOUNT,
        invoice_number: qrisData.INVOICE,
        currency: 'IDR'
      },
      transaction: {
        id: qrisData.TRANSACTIONID,
        status: qrisData.TXNSTATUS === 'S' ? 'SUCCESS' : 'FAILED',
        date: qrisData.TXNDATE,
        reference_number: qrisData.REFERENCEID
      },
      signature: qrisData.WORDS
    };

    console.log(`Received QRIS notification for invoice: ${notification.order.invoice_number}`);

    // Handle the notification
    const paymentService = new PaymentService();
    await paymentService.handleNotification(notification);

    return NextResponse.json({ received: true });

  } catch (error: any) {
    console.error('QRIS webhook error:', error);
    return NextResponse.json(
      { error: 'QRIS webhook processing failed' },
      { status: 500 }
    );
  }
}


