/**
 * Create Payment Intent API Route
 * Handles creation of Doku checkout sessions for node purchases
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { PaymentService } from '@/lib/doku/payment-service';
import { validateDokuConfig } from '@/lib/doku/config';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Validate Doku configuration
    const configValidation = validateDokuConfig();
    if (!configValidation.isValid) {
      return NextResponse.json(
        {
          error: 'Payment system not configured',
          details: configValidation.errors
        },
        { status: 500 }
      );
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    const { nodeId, amount, currency = 'idr', metadata = {} } = body;

    // Validate required fields
    if (!nodeId || !amount) {
      return NextResponse.json(
        { error: 'Missing required fields: nodeId, amount' },
        { status: 400 }
      );
    }

    // Validate amount
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      );
    }

    // Create checkout session
    const paymentService = new PaymentService();
    const checkoutSession = await paymentService.createCheckoutSession({
      nodeId,
      userId: user.id,
      amount: Math.round(amount), // Amount in IDR (no conversion needed)
      currency,
      metadata: {
        ...metadata,
        userId: user.id,
        userEmail: session.user.email || ''
      }
    });

    return NextResponse.json({
      checkoutUrl: checkoutSession.response.payment.url,
      tokenId: checkoutSession.response.payment.token_id,
      sessionId: checkoutSession.response.order.session_id,
      amount: checkoutSession.response.order.amount,
      currency: checkoutSession.response.order.currency,
      expiredDate: checkoutSession.response.payment.expired_date
    });

  } catch (error: any) {
    console.error('Error creating checkout session:', error);

    // Handle specific error types
    if (error.message === 'Node not found') {
      return NextResponse.json(
        { error: 'Node not found' },
        { status: 404 }
      );
    }

    if (error.message === 'Node already purchased') {
      return NextResponse.json(
        { error: 'Node already purchased' },
        { status: 409 }
      );
    }

    if (error.message.includes('amount')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
