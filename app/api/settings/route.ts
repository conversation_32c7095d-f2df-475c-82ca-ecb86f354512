import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { SettingsService } from '@/lib/settings/settings-service';
import { AppSettings } from '@/lib/settings/types';
import { prisma } from '@/lib/prisma';

const settingsService = SettingsService.getInstance();

// GET /api/settings - Get all settings or specific category
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category') as keyof AppSettings | null;

    if (category) {
      const categorySettings = await settingsService.getCategorySettings(category);
      return NextResponse.json({
        category,
        settings: categorySettings
      });
    } else {
      const allSettings = await settingsService.getAllSettings();
      return NextResponse.json(allSettings);
    }
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// PUT /api/settings - Update settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // For now, allow any authenticated user to update settings
    // In production, you might want to add admin role checking
    const userId = user.id;

    const body = await request.json();
    const { category, settings } = body;

    if (!category || !settings) {
      return NextResponse.json(
        { error: 'Category and settings are required' },
        { status: 400 }
      );
    }

    // Validate settings
    const validation = settingsService.validateSettings(category, settings);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: 'Invalid settings',
          details: validation.errors,
          warnings: validation.warnings
        },
        { status: 400 }
      );
    }

    // Update settings
    await settingsService.updateCategorySettings(category, settings, userId);

    // Return updated settings
    const updatedSettings = await settingsService.getCategorySettings(category);

    return NextResponse.json({
      success: true,
      category,
      settings: updatedSettings,
      warnings: validation.warnings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
}

// POST /api/settings/reset - Reset settings to defaults
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { categories } = body;

    await settingsService.resetToDefaults(categories);

    return NextResponse.json({
      success: true,
      message: categories
        ? `Reset ${categories.join(', ')} settings to defaults`
        : 'Reset all settings to defaults'
    });
  } catch (error) {
    console.error('Error resetting settings:', error);
    return NextResponse.json(
      { error: 'Failed to reset settings' },
      { status: 500 }
    );
  }
}
