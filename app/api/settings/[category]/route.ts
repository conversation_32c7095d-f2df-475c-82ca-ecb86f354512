import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { SettingsService } from '@/lib/settings/settings-service';
import { AppSettings } from '@/lib/settings/types';
import { prisma } from '@/lib/prisma';

const settingsService = SettingsService.getInstance();

// GET /api/settings/[category] - Get settings for specific category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ category: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { category } = await params;

    if (!category) {
      return NextResponse.json(
        { error: 'Category is required' },
        { status: 400 }
      );
    }

    const categorySettings = await settingsService.getCategorySettings(
      category as keyof AppSettings
    );

    return NextResponse.json({
      category,
      settings: categorySettings
    });
  } catch (error) {
    console.error(`Error fetching ${(await params).category} settings:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch category settings' },
      { status: 500 }
    );
  }
}

// PUT /api/settings/[category] - Update settings for specific category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ category: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { category } = await params;
    const userId = user.id;
    const settings = await request.json();

    if (!category) {
      return NextResponse.json(
        { error: 'Category is required' },
        { status: 400 }
      );
    }

    // Validate settings
    const validation = settingsService.validateSettings(
      category as keyof AppSettings,
      settings
    );

    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: 'Invalid settings',
          details: validation.errors,
          warnings: validation.warnings
        },
        { status: 400 }
      );
    }

    // Update settings
    await settingsService.updateCategorySettings(
      category as keyof AppSettings,
      settings,
      userId
    );

    // Return updated settings
    const updatedSettings = await settingsService.getCategorySettings(
      category as keyof AppSettings
    );

    return NextResponse.json({
      success: true,
      category,
      settings: updatedSettings,
      warnings: validation.warnings
    });
  } catch (error) {
    console.error(`Error updating ${(await params).category} settings:`, error);
    return NextResponse.json(
      { error: 'Failed to update category settings' },
      { status: 500 }
    );
  }
}
