import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { ExecutionResultsManager } from "@/lib/workflow/execution-results";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ executionId: string }> }
) {
  try {
    const { executionId } = await params;
    const body = await request.json();
    const { 
      format = 'json',
      includeMetadata = true,
      includeLogs = false,
      includeErrors = true,
      filename,
      dateFormat = 'ISO',
      timezone = 'UTC'
    } = body;

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    console.log(`[Export API] Exporting execution ${executionId} in ${format} format`);

    const resultsManager = ExecutionResultsManager.getInstance();

    // Get execution summary
    const summary = await resultsManager.getExecutionSummary(executionId);
    if (!summary) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Prepare export options
    const exportOptions = {
      format,
      includeMetadata,
      includeLogs,
      includeErrors,
      dateFormat,
      timezone
    };

    try {
      // Export the results
      const exportResult = await resultsManager.exportResults(executionId, exportOptions);
      
      // Set appropriate headers for file download
      const headers = new Headers();
      headers.set('Content-Type', exportResult.mimeType);
      headers.set('Content-Disposition', `attachment; filename="${exportResult.filename}"`);
      
      // For JSON and CSV, return the data directly
      if (format === 'json') {
        return new NextResponse(JSON.stringify(exportResult.data, null, 2), {
          status: 200,
          headers
        });
      } else if (format === 'csv') {
        return new NextResponse(exportResult.data, {
          status: 200,
          headers
        });
      } else {
        // For other formats, return as binary data
        return new NextResponse(exportResult.data, {
          status: 200,
          headers
        });
      }

    } catch (exportError) {
      console.error('[Export API] Export failed:', exportError);
      return NextResponse.json(
        { 
          error: 'Export failed',
          details: exportError instanceof Error ? exportError.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('[Export API] Request failed:', error);
    return NextResponse.json(
      { 
        error: 'Export request failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ executionId: string }> }
) {
  try {
    const { executionId } = await params;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'json';

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    console.log(`[Export API] Quick export for execution ${executionId} in ${format} format`);

    const resultsManager = ExecutionResultsManager.getInstance();

    // Get execution summary
    const summary = await resultsManager.getExecutionSummary(executionId);
    if (!summary) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Quick export with default options
    const exportOptions = {
      format,
      includeMetadata: true,
      includeLogs: false,
      includeErrors: true,
      dateFormat: 'ISO',
      timezone: 'UTC'
    };

    try {
      const exportResult = await resultsManager.exportResults(executionId, exportOptions);
      
      // Set appropriate headers
      const headers = new Headers();
      headers.set('Content-Type', exportResult.mimeType);
      headers.set('Content-Disposition', `attachment; filename="${exportResult.filename}"`);
      
      if (format === 'json') {
        return new NextResponse(JSON.stringify(exportResult.data, null, 2), {
          status: 200,
          headers
        });
      } else if (format === 'csv') {
        return new NextResponse(exportResult.data, {
          status: 200,
          headers
        });
      } else {
        return new NextResponse(exportResult.data, {
          status: 200,
          headers
        });
      }

    } catch (exportError) {
      console.error('[Export API] Quick export failed:', exportError);
      return NextResponse.json(
        { 
          error: 'Export failed',
          details: exportError instanceof Error ? exportError.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('[Export API] Quick export request failed:', error);
    return NextResponse.json(
      { 
        error: 'Export request failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// OPTIONS method for CORS support
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
