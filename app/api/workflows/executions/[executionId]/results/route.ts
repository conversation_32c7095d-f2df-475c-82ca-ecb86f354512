import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { ExecutionResultsManager } from '@/lib/workflow/execution-results';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ executionId: string }> }
) {
  try {
    const { executionId } = await params;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'summary';
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '50');

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    console.log(`[Execution Results] Getting results for execution ${executionId}, format: ${format}`);

    const resultsManager = ExecutionResultsManager.getInstance();

    switch (format) {
      case 'summary':
        const summary = await resultsManager.getExecutionSummary(executionId);
        if (!summary) {
          return NextResponse.json(
            { error: 'Execution not found' },
            { status: 404 }
          );
        }
        return NextResponse.json(summary);

      case 'table':
        const tableData = await resultsManager.getTablePreview(executionId, page, pageSize);
        if (!tableData) {
          return NextResponse.json(
            { error: 'Execution not found' },
            { status: 404 }
          );
        }
        return NextResponse.json(tableData);

      default:
        return NextResponse.json(
          { error: 'Invalid format. Use "summary" or "table"' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('[Execution Results] Error getting results:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get execution results',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ executionId: string }> }
) {
  try {
    const { executionId } = await params;
    const body = await request.json();
    const { 
      format = 'json',
      includeMetadata = true,
      includeLogs = false,
      includeErrors = true,
      dateFormat = 'ISO',
      timezone = 'UTC'
    } = body;

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    console.log(`[Execution Results] Exporting results for execution ${executionId}, format: ${format}`);

    const resultsManager = ExecutionResultsManager.getInstance();

    const exportOptions = {
      format,
      includeMetadata,
      includeLogs,
      includeErrors,
      dateFormat,
      timezone
    };

    const exportResult = await resultsManager.exportResults(executionId, exportOptions);

    // Set appropriate headers for file download
    const headers = new Headers();
    headers.set('Content-Type', exportResult.mimeType);
    headers.set('Content-Disposition', `attachment; filename="${exportResult.filename}"`);

    return new NextResponse(exportResult.data, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('[Execution Results] Error exporting results:', error);
    return NextResponse.json(
      { 
        error: 'Failed to export execution results',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
