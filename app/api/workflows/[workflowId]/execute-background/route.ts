import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { PrismaClient } from '@prisma/client';
import { WorkflowExecutionQueue } from '@/lib/workflow/execution-queue';
import { ExecutionContext } from '@/lib/workflow/execution-engine';

const prisma = new PrismaClient();

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const { workflowId } = await params;
    const body = await request.json();
    const { 
      executionOptions = {}, 
      variables = {}, 
      priority = 5,
      scheduledAt = null
    } = body;

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    console.log(`[Background Execute] Starting background execution for workflow ${workflowId}`);

    // Get workflow data
    const workflow = await prisma.workflow.findUnique({
      where: { id: workflowId },
      select: {
        id: true,
        name: true,
        nodes: true,
        edges: true,
        userId: true
      }
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Parse workflow data
    let nodes, edges;
    try {
      nodes = JSON.parse(workflow.nodes);
      edges = JSON.parse(workflow.edges);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid workflow data format' },
        { status: 400 }
      );
    }

    if (!nodes || nodes.length === 0) {
      return NextResponse.json(
        { error: 'Workflow has no nodes to execute' },
        { status: 400 }
      );
    }

    // Create execution context
    const executionContext: ExecutionContext = {
      workflowId: workflow.id,
      userId: userId,
      variables: variables || {},
      secrets: {}, // TODO: Implement secrets management
      settings: {}, // TODO: Implement settings management
      log: (message: string, level: string = 'info') => {
        console.log(`[Workflow ${workflowId}] ${level.toUpperCase()}: ${message}`);
      }
    };

    // Get execution queue instance
    const executionQueue = WorkflowExecutionQueue.getInstance();

    // Parse scheduled date if provided
    const scheduledDate = scheduledAt ? new Date(scheduledAt) : undefined;

    // Add to execution queue
    const executionId = await executionQueue.enqueue(
      workflowId,
      userId,
      nodes,
      edges,
      executionContext,
      {
        mode: 'sequential', // Force sequential for background execution
        timeout: 300000, // 5 minutes timeout
        retryAttempts: 3,
        continueOnError: false,
        debugMode: false,
        maxConcurrentNodes: 1,
        backgroundExecution: true,
        saveResults: true,
        notifyOnComplete: true,
        ...executionOptions
      },
      priority,
      scheduledDate
    );

    console.log(`[Background Execute] Queued execution ${executionId} for workflow ${workflowId}`);

    // Get queue stats
    const queueStats = executionQueue.getStats();

    return NextResponse.json({
      success: true,
      executionId,
      message: scheduledDate 
        ? `Workflow scheduled for execution at ${scheduledDate.toISOString()}`
        : 'Workflow added to execution queue',
      queuePosition: queueStats.totalQueued,
      estimatedStartTime: scheduledDate || new Date(Date.now() + (queueStats.totalQueued * 30000)), // Rough estimate
      queueStats
    });

  } catch (error) {
    console.error('[Background Execute] Error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to queue workflow execution',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const { workflowId } = await params;
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    const executionQueue = WorkflowExecutionQueue.getInstance();

    if (executionId) {
      // Get specific execution status
      const execution = executionQueue.getExecutionStatus(executionId);
      
      if (!execution) {
        // Check database for completed executions
        const dbExecution = await prisma.workflowExecution.findUnique({
          where: { id: executionId },
          select: {
            id: true,
            status: true,
            startTime: true,
            endTime: true,
            progress: true,
            error: true,
            results: true,
            logs: true
          }
        });

        if (!dbExecution) {
          return NextResponse.json(
            { error: 'Execution not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          executionId: dbExecution.id,
          status: dbExecution.status,
          startTime: dbExecution.startTime,
          endTime: dbExecution.endTime,
          progress: dbExecution.progress,
          error: dbExecution.error,
          hasResults: !!dbExecution.results,
          hasLogs: !!dbExecution.logs,
          isBackground: true
        });
      }

      return NextResponse.json({
        executionId: execution.id,
        status: execution.attempts > 0 ? 'running' : 'queued',
        queuePosition: executionQueue.getStats().totalQueued,
        attempts: execution.attempts,
        maxAttempts: execution.maxAttempts,
        lastError: execution.lastError,
        scheduledAt: execution.scheduledAt,
        createdAt: execution.createdAt,
        isBackground: true
      });
    } else {
      // Get queue stats for this workflow
      const queueStats = executionQueue.getStats();
      
      // Get recent executions from database
      const recentExecutions = await prisma.workflowExecution.findMany({
        where: { 
          workflowId,
          userId: userId !== 'anonymous' ? userId : undefined
        },
        select: {
          id: true,
          status: true,
          startTime: true,
          endTime: true,
          progress: true,
          error: true
        },
        orderBy: { startTime: 'desc' },
        take: 10
      });

      return NextResponse.json({
        queueStats,
        recentExecutions,
        workflowId
      });
    }

  } catch (error) {
    console.error('[Background Execute] Error getting status:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get execution status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');

    if (!executionId) {
      return NextResponse.json(
        { error: 'Execution ID is required' },
        { status: 400 }
      );
    }

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    const executionQueue = WorkflowExecutionQueue.getInstance();

    // Try to cancel the execution
    const cancelled = await executionQueue.cancelExecution(executionId);

    if (cancelled) {
      return NextResponse.json({
        success: true,
        message: 'Execution cancelled successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Execution not found or cannot be cancelled' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('[Background Execute] Error cancelling execution:', error);
    return NextResponse.json(
      { 
        error: 'Failed to cancel execution',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
