import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { WorkflowExecutionEngine } from "@/lib/workflow/execution-engine";

// Helper function to extract output data from execution results
function extractOutputDataFromResults(results: Record<string, any>): any[] {
  const outputData: any[] = [];

  Object.entries(results).forEach(([nodeId, result]) => {
    if (result.success && result.outputs) {
      Object.entries(result.outputs).forEach(([outputKey, outputValue]) => {
        if (Array.isArray(outputValue)) {
          outputValue.forEach((item, index) => {
            outputData.push({
              nodeId,
              outputKey,
              index,
              data: item,
              type: typeof item,
              timestamp: new Date()
            });
          });
        } else if (typeof outputValue === 'object' && outputValue !== null) {
          outputData.push({
            nodeId,
            outputKey,
            data: outputValue,
            type: 'object',
            timestamp: new Date()
          });
        } else {
          outputData.push({
            nodeId,
            outputKey,
            data: outputValue,
            type: typeof outputValue,
            timestamp: new Date()
          });
        }
      });
    }
  });

  return outputData;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    // Await params to fix Next.js 15 requirement
    const { workflowId } = await params;

    // Get workflow (no authentication required)
    const workflow = await prisma.workflow.findFirst({
      where: {
        id: workflowId
      }
    });

    if (!workflow) {
      return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    const {
      executionOptions = {},
      variables = {},
      triggerNodeId = null
    } = body;

    // Check if this is a forced new execution (for re-execute functionality)
    const forceNewExecution = executionOptions.forceNewExecution || false;

    // Check for existing execution for this workflow and user
    const userId = workflow.userId || 'anonymous';
    let execution;

    if (forceNewExecution) {
      // Always create new execution for re-execute functionality
      execution = await prisma.workflowExecution.create({
        data: {
          id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          workflowId: workflowId,
          userId: userId,
          status: 'running',
          startTime: new Date(),
          variables: JSON.stringify(variables),
          options: JSON.stringify(executionOptions),
          triggerNodeId,
          logs: JSON.stringify([{
            timestamp: new Date(),
            level: 'info',
            message: 'Re-execution started'
          }])
        }
      });
    } else {
      // Normal execution - check for existing and update, or create new
      execution = await prisma.workflowExecution.findFirst({
        where: {
          workflowId: workflowId,
          userId: userId
        },
        orderBy: { startTime: 'desc' }
      });

      if (execution) {
        // Update existing execution
        execution = await prisma.workflowExecution.update({
          where: { id: execution.id },
          data: {
            status: 'running',
            startTime: new Date(),
            endTime: null,
            variables: JSON.stringify(variables),
            options: JSON.stringify(executionOptions),
            triggerNodeId,
            results: null,
            error: null,
            progress: 0,
            logs: JSON.stringify([{
              timestamp: new Date(),
              level: 'info',
              message: 'Execution restarted'
            }])
          }
        });
      } else {
        // Create new execution record
        execution = await prisma.workflowExecution.create({
          data: {
            id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            workflowId: workflowId,
            userId: userId,
            status: 'running',
            startTime: new Date(),
            variables: JSON.stringify(variables),
            options: JSON.stringify(executionOptions),
            triggerNodeId,
            logs: JSON.stringify([{
              timestamp: new Date(),
              level: 'info',
              message: 'Execution started'
            }])
          }
        });
      }
    }

    // Use the real execution engine
    try {
      // Parse workflow nodes and edges (handle double-encoded JSON)
      let nodes, edges;
      try {
        // First parse to get the JSON string, then parse again to get the actual array
        const nodesStr = JSON.parse(workflow.nodes || '[]');
        const edgesStr = JSON.parse(workflow.edges || '[]');

        // If the result is a string, parse it again (double-encoded)
        nodes = typeof nodesStr === 'string' ? JSON.parse(nodesStr) : nodesStr;
        edges = typeof edgesStr === 'string' ? JSON.parse(edgesStr) : edgesStr;

        // Ensure they are arrays
        if (!Array.isArray(nodes)) nodes = [];
        if (!Array.isArray(edges)) edges = [];

      } catch (parseError) {
        console.error('[Quick Execute] Error parsing workflow data:', parseError);
        nodes = [];
        edges = [];
      }

      console.log(`[Quick Execute] Starting execution for workflow ${workflowId} with ${nodes.length} nodes`);

      // Create execution context
      const executionContext = {
        workflowId: workflowId,
        userId: workflow.userId || 'anonymous',
        variables,
        secrets: {}, // TODO: Implement secrets management
        settings: {}, // TODO: Implement settings management
        log: (message: string) => console.log(`[Workflow ${workflowId}]: ${message}`)
      };

      // Get execution engine instance
      const executionEngine = WorkflowExecutionEngine.getInstance();

      // Execute the workflow using the real engine (continueOnError: true by default)
      const workflowStatus = await executionEngine.executeWorkflow(
        nodes,
        edges,
        executionContext,
        {
          mode: 'sequence',
          timeout: 30000,
          retryAttempts: 3,
          continueOnError: true,
          debugMode: false,
          maxConcurrentNodes: 3,
          ...executionOptions
        }
      );

      console.log(`[Quick Execute] Workflow execution completed with status: ${workflowStatus.status}`);

      // Extract results and logs from the execution status
      const results = workflowStatus.results || {};
      const logs = workflowStatus.logs || [];

      // Determine final status
      const finalStatus = workflowStatus.status;

      // Prepare detailed logs that preserve execution results
      const detailedLogs = [
        ...JSON.parse(execution.logs || '[]'),
        ...logs.map(log => {
          if (typeof log === 'string') {
            return {
              timestamp: new Date(),
              level: 'info',
              message: log
            };
          } else {
            return {
              timestamp: log.timestamp || new Date(),
              level: log.level || 'info',
              message: log.message,
              nodeId: log.nodeId
            };
          }
        }),
        {
          timestamp: new Date(),
          level: 'info',
          message: `Execution completed with status: ${finalStatus}`,
          executionSummary: {
            totalNodes: Object.keys(results).length,
            successfulNodes: Object.keys(results).filter(id => results[id].success).length,
            failedNodes: Object.keys(results).filter(id => !results[id].success).length,
            outputData: extractOutputDataFromResults(results)
          }
        }
      ];

      // Update execution record
      await prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          status: finalStatus,
          endTime: workflowStatus.endTime || new Date(),
          results: JSON.stringify(results),
          logs: JSON.stringify(detailedLogs),
          error: finalStatus === 'failed' ? 'Workflow execution failed' : null,
          progress: workflowStatus.progress || 100
        }
      });

      console.log(`[Quick Execute] Database updated with execution results`);

      return NextResponse.json({
        executionId: execution.id,
        status: finalStatus,
        results,
        logs,
        startTime: execution.startTime,
        endTime: workflowStatus.endTime || new Date(),
        progress: workflowStatus.progress,
        completedNodes: workflowStatus.completedNodes,
        failedNodes: workflowStatus.failedNodes
      });

    } catch (executionError) {
      console.error('[Quick Execute] Execution error:', executionError);

      // Update execution record with error
      await prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          status: 'failed',
          endTime: new Date(),
          error: executionError instanceof Error ? executionError.message : 'Execution failed',
          logs: JSON.stringify([
            ...JSON.parse(execution.logs || '[]'),
            {
              timestamp: new Date(),
              level: 'error',
              message: executionError instanceof Error ? executionError.message : 'Execution failed'
            }
          ])
        }
      });

      throw executionError;
    }

  } catch (error) {
    console.error('Workflow execution error:', error);
    return NextResponse.json({
      error: 'Execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    // Await params to fix Next.js 15 requirement
    const { workflowId } = await params;

    // Get execution history for this workflow (no authentication required)
    const executions = await prisma.workflowExecution.findMany({
      where: {
        workflowId: workflowId
      },
      orderBy: { startTime: 'desc' },
      take: 20 // Limit to last 20 executions
    });

    // Parse JSON strings in the results
    const parsedExecutions = executions.map(execution => ({
      ...execution,
      variables: execution.variables ? JSON.parse(execution.variables) : null,
      options: execution.options ? JSON.parse(execution.options) : null,
      results: execution.results ? JSON.parse(execution.results) : null,
      logs: execution.logs ? JSON.parse(execution.logs) : [],
      duration: execution.endTime && execution.startTime
        ? execution.endTime.getTime() - execution.startTime.getTime()
        : null
    }));

    return NextResponse.json({
      executions: parsedExecutions.map(exec => ({
        id: exec.id,
        status: exec.status,
        startTime: exec.startTime.toISOString(),
        endTime: exec.endTime?.toISOString(),
        duration: exec.duration,
        variables: exec.variables,
        options: exec.options,
        results: exec.results,
        logs: exec.logs,
        error: exec.error,
        triggerNodeId: exec.triggerNodeId
      }))
    });

  } catch (error) {
    console.error('Execution history fetch error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
