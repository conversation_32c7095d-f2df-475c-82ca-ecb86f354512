import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get withdrawal requests
    const withdrawalRequests = await prisma.withdrawalRequest.findMany({
      where: { userId: user.id },
      include: {
        bankAccount: {
          select: {
            bankName: true,
            accountNumber: true,
            accountHolderName: true
          }
        }
      },
      orderBy: { requestedAt: 'desc' }
    });

    return NextResponse.json(withdrawalRequests);

  } catch (error) {
    console.error('Error fetching withdrawal requests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    const { amount, bankAccountId } = body;

    // Validate required fields
    if (!amount || !bankAccountId) {
      return NextResponse.json(
        { error: 'Missing required fields: amount, bankAccountId' },
        { status: 400 }
      );
    }

    // Validate amount
    if (typeof amount !== 'number' || amount < 50000 || amount > ********) {
      return NextResponse.json(
        { error: 'Amount must be between IDR 50,000 and IDR 10,000,000' },
        { status: 400 }
      );
    }

    // Verify bank account belongs to user
    const bankAccount = await prisma.bankAccount.findFirst({
      where: {
        id: bankAccountId,
        userId: user.id
      }
    });

    if (!bankAccount) {
      return NextResponse.json(
        { error: 'Bank account not found' },
        { status: 404 }
      );
    }

    // Calculate available balance
    const revenueStats = await prisma.revenue.aggregate({
      where: { userId: user.id },
      _sum: {
        commission: true
      }
    });

    const withdrawnStats = await prisma.withdrawalRequest.aggregate({
      where: {
        userId: user.id,
        status: 'completed'
      },
      _sum: {
        amount: true
      }
    });

    const pendingStats = await prisma.withdrawalRequest.aggregate({
      where: {
        userId: user.id,
        status: {
          in: ['pending', 'processing']
        }
      },
      _sum: {
        amount: true
      }
    });

    const totalEarnings = revenueStats._sum.commission || 0;
    const totalWithdrawn = withdrawnStats._sum.amount || 0;
    const pendingWithdrawals = pendingStats._sum.amount || 0;
    const availableBalance = totalEarnings - totalWithdrawn - pendingWithdrawals;

    // Check if user has sufficient balance
    if (amount > availableBalance) {
      return NextResponse.json(
        { error: 'Insufficient balance for withdrawal' },
        { status: 400 }
      );
    }

    // Check for existing pending withdrawal requests (limit to 1 pending at a time)
    const existingPendingRequest = await prisma.withdrawalRequest.findFirst({
      where: {
        userId: user.id,
        status: {
          in: ['pending', 'processing']
        }
      }
    });

    if (existingPendingRequest) {
      return NextResponse.json(
        { error: 'You already have a pending withdrawal request. Please wait for it to be processed.' },
        { status: 400 }
      );
    }

    // Create withdrawal request
    const withdrawalRequest = await prisma.withdrawalRequest.create({
      data: {
        userId: user.id,
        bankAccountId,
        amount,
        currency: 'IDR',
        status: 'pending',
        metadata: JSON.stringify({
          userEmail: session.user.email,
          requestedFrom: 'developer_dashboard'
        })
      },
      include: {
        bankAccount: {
          select: {
            bankName: true,
            accountNumber: true,
            accountHolderName: true
          }
        }
      }
    });

    // Update user balance
    await prisma.user.update({
      where: { id: user.id },
      data: {
        availableBalance: availableBalance - amount,
        totalEarnings,
        totalWithdrawn
      }
    });

    return NextResponse.json({
      message: 'Withdrawal request submitted successfully',
      withdrawalRequest: {
        id: withdrawalRequest.id,
        amount: withdrawalRequest.amount,
        currency: withdrawalRequest.currency,
        status: withdrawalRequest.status,
        requestedAt: withdrawalRequest.requestedAt.toISOString(),
        bankAccount: withdrawalRequest.bankAccount
      }
    });

  } catch (error) {
    console.error('Error creating withdrawal request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
