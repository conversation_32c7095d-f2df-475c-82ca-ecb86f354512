import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { randomUUID } from "crypto";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get developer's nodes
    const nodes = await prisma.nodePlugin.findMany({
      where: { authorId: user.id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        },
        _count: {
          select: {
            reviews: true,
            purchases: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Transform nodes to include status (for now, all approved)
    const transformedNodes = nodes.map(node => ({
      ...node,
      status: node.verified ? 'approved' : 'pending',
      reviewCount: node._count.reviews,
      tags: JSON.parse(node.tags || '[]'),
      dependencies: JSON.parse(node.dependencies || '[]'),
      permissions: JSON.parse(node.permissions || '[]'),
      screenshots: JSON.parse(node.screenshots || '[]'),
      compatibility: JSON.parse(node.compatibility || '{}')
    }));

    return NextResponse.json(transformedNodes);

  } catch (error) {
    console.error('Developer nodes fetch error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const formData = await request.formData();

    // Extract form fields
    const name = formData.get('name') as string;
    const version = formData.get('version') as string;
    const description = formData.get('description') as string;
    const longDescription = formData.get('longDescription') as string || '';
    const category = formData.get('category') as string;
    const tier = formData.get('tier') as string;
    const price = parseFloat(formData.get('price') as string || '0');
    const tags = JSON.parse(formData.get('tags') as string || '[]');
    const repositoryUrl = formData.get('repositoryUrl') as string || '';
    const documentationUrl = formData.get('documentationUrl') as string || '';

    // Extract files
    const packageFile = formData.get('package') as File;
    const iconFile = formData.get('icon') as File;

    // Extract screenshots
    const screenshots: File[] = [];
    for (let i = 0; i < 5; i++) {
      const screenshot = formData.get(`screenshot_${i}`) as File;
      if (screenshot) {
        screenshots.push(screenshot);
      }
    }

    // Validate required fields
    if (!name || !version || !description || !category || !tier) {
      return NextResponse.json({
        error: 'Missing required fields'
      }, { status: 400 });
    }

    if (!packageFile) {
      return NextResponse.json({
        error: 'Node package file is required'
      }, { status: 400 });
    }

    // Create upload directory
    const uploadDir = join(process.cwd(), 'uploads', 'nodes', user.id);
    await mkdir(uploadDir, { recursive: true });

    const nodeId = randomUUID();
    const nodeDir = join(uploadDir, nodeId);
    await mkdir(nodeDir, { recursive: true });

    // Save package file
    const packageBuffer = Buffer.from(await packageFile.arrayBuffer());
    const packagePath = join(nodeDir, `${name}-${version}.zip`);
    await writeFile(packagePath, packageBuffer);

    // Save icon if provided
    let iconUrl = '/api/placeholder/64/64'; // Default icon
    if (iconFile) {
      const iconBuffer = Buffer.from(await iconFile.arrayBuffer());
      const iconExtension = iconFile.name.split('.').pop();
      const iconPath = join(nodeDir, `icon.${iconExtension}`);
      await writeFile(iconPath, iconBuffer);
      iconUrl = `/api/uploads/nodes/${user.id}/${nodeId}/icon.${iconExtension}`;
    }

    // Save screenshots
    const screenshotUrls: string[] = [];
    for (let i = 0; i < screenshots.length; i++) {
      const screenshot = screenshots[i];
      const screenshotBuffer = Buffer.from(await screenshot.arrayBuffer());
      const screenshotExtension = screenshot.name.split('.').pop();
      const screenshotPath = join(nodeDir, `screenshot_${i}.${screenshotExtension}`);
      await writeFile(screenshotPath, screenshotBuffer);
      screenshotUrls.push(`/api/uploads/nodes/${user.id}/${nodeId}/screenshot_${i}.${screenshotExtension}`);
    }

    // Create node in database
    const node = await prisma.nodePlugin.create({
      data: {
        id: nodeId,
        name,
        version,
        description,
        longDescription,
        authorId: user.id,
        category,
        tier,
        price: tier === 'free' ? 0 : price,
        tags: JSON.stringify(tags),
        dependencies: JSON.stringify([]), // Will be extracted from package
        permissions: JSON.stringify([]), // Will be extracted from package
        icon: iconUrl,
        screenshots: JSON.stringify(screenshotUrls),
        downloadUrl: `/api/uploads/nodes/${user.id}/${nodeId}/${name}-${version}.zip`,
        repositoryUrl: repositoryUrl || null,
        documentationUrl: documentationUrl || null,
        verified: false, // Needs manual verification
        featured: false,
        rating: 0,
        reviewCount: 0,
        downloads: 0,
        weeklyDownloads: 0,
        compatibility: JSON.stringify({
          minVersion: "1.0.0",
          maxVersion: null
        }),
        changelog: JSON.stringify([{
          version,
          date: new Date(),
          changes: ["Initial release"]
        }]),
        approvalStatus: 'pending' // Requires admin approval
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        }
      }
    });

    // Process package file to extract node code
    try {
      // TODO: Extract actual code from uploaded package
      // For now, create a compatible node code entry based on the sample package format
      const nodeCode = `
// Node Package: ${name}
// Version: ${version}
// Generated from uploaded package

// Import the package components (simulated)
const packageData = {
  metadata: {
    type: '${nodeId}',
    label: '${name}',
    description: '${description}',
    category: '${category}',
    version: '${version}',
    icon: 'Package',
    needsOnChangeHandler: true,
    isDynamic: true,
    defaultWidth: 280,
    tags: ['custom', 'uploaded', '${category}']
  },

  // Execution logic compatible with our workflow engine
  execution: {
    execute: async (inputs, config, context) => {
      try {
        context.log(\`\${name}: Starting execution\`);

        // Basic processing logic
        const result = {
          processed: true,
          timestamp: Date.now(),
          inputs: inputs,
          config: config
        };

        context.log(\`\${name}: Execution completed\`);

        return {
          success: true,
          data: result,
          outputs: {
            result: result,
            data: result
          },
          metadata: {
            executionTime: Date.now(),
            nodeType: '${nodeId}',
            version: '${version}'
          }
        };
      } catch (error) {
        context.log(\`\${name}: Execution failed: \${error.message}\`);
        return {
          success: false,
          error: error.message,
          data: null,
          outputs: { result: null, data: null },
          metadata: {
            executionTime: Date.now(),
            nodeType: '${nodeId}',
            version: '${version}',
            error: true
          }
        };
      }
    }
  }
};

// Node definition for our system
const nodeDefinition = {
  id: '${nodeId}',
  name: '${name}',
  description: '${description}',
  category: '${category}',
  version: '${version}',
  type: '${nodeId}',
  inputs: [
    {
      id: 'input',
      name: 'Input',
      type: 'string',
      required: false,
      description: 'Input data'
    }
  ],
  outputs: [
    {
      id: 'result',
      name: 'Result',
      type: 'object',
      description: 'Processing result'
    },
    {
      id: 'data',
      name: 'Data',
      type: 'object',
      description: 'Output data'
    }
  ],
  execute: packageData.execution.execute,
  metadata: packageData.metadata
};

// Export for Web Worker
if (typeof self !== 'undefined') {
  self.postMessage({ type: 'definition', data: nodeDefinition });
}
`;

      // Calculate checksum
      const crypto = require('crypto');
      const checksum = crypto.createHash('sha256').update(nodeCode).digest('hex');

      // Create node code entry
      await prisma.nodeCode.create({
        data: {
          nodeId: node.id,
          version: version,
          code: nodeCode,
          dependencies: JSON.stringify([]),
          permissions: JSON.stringify([]),
          checksum: checksum
        }
      });

    } catch (codeError) {
      console.error('Failed to create node code:', codeError);
      // Continue without failing the upload
    }

    return NextResponse.json({
      ...node,
      status: 'pending',
      tags: JSON.parse(node.tags || '[]'),
      dependencies: JSON.parse(node.dependencies || '[]'),
      permissions: JSON.parse(node.permissions || '[]'),
      screenshots: JSON.parse(node.screenshots || '[]'),
      compatibility: JSON.parse(node.compatibility || '{}')
    });

  } catch (error) {
    console.error('Node upload error:', error);
    return NextResponse.json({
      error: 'Failed to upload node'
    }, { status: 500 });
  }
}
