import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {
      authorId: user.id
    };

    if (status !== 'all') {
      if (!['pending', 'approved', 'rejected'].includes(status)) {
        return NextResponse.json(
          { error: 'Invalid status. Must be: pending, approved, rejected, or all' },
          { status: 400 }
        );
      }
      whereClause.approvalStatus = status;
    }

    // Get developer's nodes with pagination
    const [nodes, totalCount] = await Promise.all([
      prisma.nodePlugin.findMany({
        where: whereClause,
        include: {
          approvedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          nodeCodes: {
            select: {
              id: true,
              version: true,
              createdAt: true
            }
          },
          _count: {
            select: {
              installedNodes: true,
              reviews: true,
              purchases: true
            }
          }
        },
        orderBy: [
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.nodePlugin.count({
        where: whereClause
      })
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Get summary statistics for this developer
    const stats = await prisma.nodePlugin.groupBy({
      by: ['approvalStatus'],
      where: {
        authorId: user.id
      },
      _count: {
        id: true
      }
    });

    const statusCounts = {
      pending: 0,
      approved: 0,
      rejected: 0
    };

    stats.forEach(stat => {
      if (stat.approvalStatus in statusCounts) {
        statusCounts[stat.approvalStatus as keyof typeof statusCounts] = stat._count.id;
      }
    });

    // Get total earnings and downloads for approved nodes
    const approvedNodes = await prisma.nodePlugin.findMany({
      where: {
        authorId: user.id,
        approvalStatus: 'approved'
      },
      select: {
        downloads: true,
        weeklyDownloads: true,
        _count: {
          select: {
            purchases: true
          }
        }
      }
    });

    const totalDownloads = approvedNodes.reduce((sum, node) => sum + node.downloads, 0);
    const totalWeeklyDownloads = approvedNodes.reduce((sum, node) => sum + node.weeklyDownloads, 0);
    const totalPurchases = approvedNodes.reduce((sum, node) => sum + node._count.purchases, 0);

    return NextResponse.json({
      success: true,
      data: {
        nodes: nodes.map(node => ({
          id: node.id,
          name: node.name,
          version: node.version,
          description: node.description,
          category: node.category,
          tier: node.tier,
          price: node.price,
          icon: node.icon,
          tags: JSON.parse(node.tags || '[]'),
          approvalStatus: node.approvalStatus,
          approvedAt: node.approvedAt,
          rejectionReason: node.rejectionReason,
          createdAt: node.createdAt,
          lastUpdated: node.lastUpdated,
          approvedBy: node.approvedBy,
          hasCode: node.nodeCodes.length > 0,
          installCount: node._count.installedNodes,
          reviewCount: node._count.reviews,
          purchaseCount: node._count.purchases,
          rating: node.rating,
          downloads: node.downloads,
          weeklyDownloads: node.weeklyDownloads,
          verified: node.verified,
          featured: node.featured
        })),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage,
          hasPrevPage
        },
        stats: {
          ...statusCounts,
          total: totalCount,
          totalDownloads,
          totalWeeklyDownloads,
          totalPurchases
        }
      }
    });

  } catch (error) {
    console.error('Get developer node status error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch node status' },
      { status: 500 }
    );
  }
}
