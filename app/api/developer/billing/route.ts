import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user's bank accounts
    const bankAccounts = await prisma.bankAccount.findMany({
      where: { userId: user.id },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    // Get withdrawal history
    const withdrawalHistory = await prisma.withdrawalRequest.findMany({
      where: { userId: user.id },
      include: {
        bankAccount: {
          select: {
            bankName: true,
            accountNumber: true
          }
        }
      },
      orderBy: { requestedAt: 'desc' },
      take: 20 // Limit to last 20 withdrawals
    });

    // Calculate total earnings from revenue
    const revenueStats = await prisma.revenue.aggregate({
      where: { userId: user.id },
      _sum: {
        commission: true
      }
    });

    // Calculate total withdrawn (completed withdrawals)
    const withdrawnStats = await prisma.withdrawalRequest.aggregate({
      where: {
        userId: user.id,
        status: 'completed'
      },
      _sum: {
        amount: true
      }
    });

    // Calculate pending withdrawals
    const pendingStats = await prisma.withdrawalRequest.aggregate({
      where: {
        userId: user.id,
        status: {
          in: ['pending', 'processing']
        }
      },
      _sum: {
        amount: true
      }
    });

    const totalEarnings = revenueStats._sum.commission || 0;
    const totalWithdrawn = withdrawnStats._sum.amount || 0;
    const pendingWithdrawals = pendingStats._sum.amount || 0;
    const availableBalance = totalEarnings - totalWithdrawn - pendingWithdrawals;

    // Update user balance if it's different
    if (user.availableBalance !== availableBalance || user.totalEarnings !== totalEarnings || user.totalWithdrawn !== totalWithdrawn) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          availableBalance,
          totalEarnings,
          totalWithdrawn
        }
      });
    }

    const billingData = {
      availableBalance: Math.max(0, availableBalance), // Ensure non-negative
      totalEarnings,
      totalWithdrawn,
      pendingWithdrawals,
      bankAccounts: bankAccounts.map(account => ({
        id: account.id,
        bankName: account.bankName,
        accountNumber: account.accountNumber,
        accountHolderName: account.accountHolderName,
        isDefault: account.isDefault,
        isVerified: account.isVerified,
        createdAt: account.createdAt.toISOString()
      })),
      withdrawalHistory: withdrawalHistory.map(withdrawal => ({
        id: withdrawal.id,
        amount: withdrawal.amount,
        currency: withdrawal.currency,
        status: withdrawal.status,
        requestedAt: withdrawal.requestedAt.toISOString(),
        processedAt: withdrawal.processedAt?.toISOString(),
        completedAt: withdrawal.completedAt?.toISOString(),
        bankAccount: {
          bankName: withdrawal.bankAccount.bankName,
          accountNumber: withdrawal.bankAccount.accountNumber
        }
      }))
    };

    return NextResponse.json(billingData);

  } catch (error) {
    console.error('Error fetching billing data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
