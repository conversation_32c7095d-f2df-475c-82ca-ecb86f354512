import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user's bank accounts
    const bankAccounts = await prisma.bankAccount.findMany({
      where: { userId: user.id },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    return NextResponse.json(bankAccounts);

  } catch (error) {
    console.error('Error fetching bank accounts:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    const { bankName, accountNumber, accountHolderName, branchCode, isDefault } = body;

    // Validate required fields
    if (!bankName || !accountNumber || !accountHolderName) {
      return NextResponse.json(
        { error: 'Missing required fields: bankName, accountNumber, accountHolderName' },
        { status: 400 }
      );
    }

    // Validate account number (should be numeric and reasonable length)
    if (!/^\d{8,20}$/.test(accountNumber)) {
      return NextResponse.json(
        { error: 'Account number must be 8-20 digits' },
        { status: 400 }
      );
    }

    // Check if account number already exists for this user
    const existingAccount = await prisma.bankAccount.findFirst({
      where: {
        userId: user.id,
        accountNumber: accountNumber
      }
    });

    if (existingAccount) {
      return NextResponse.json(
        { error: 'This account number is already registered' },
        { status: 400 }
      );
    }

    // If this is set as default, unset other default accounts
    if (isDefault) {
      await prisma.bankAccount.updateMany({
        where: {
          userId: user.id,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      });
    }

    // Create new bank account
    const bankAccount = await prisma.bankAccount.create({
      data: {
        userId: user.id,
        bankName,
        accountNumber,
        accountHolderName: accountHolderName.toUpperCase(), // Store in uppercase
        branchCode: branchCode || null,
        isDefault: isDefault || false,
        isVerified: false // New accounts need verification
      }
    });

    return NextResponse.json({
      message: 'Bank account added successfully',
      bankAccount: {
        id: bankAccount.id,
        bankName: bankAccount.bankName,
        accountNumber: bankAccount.accountNumber,
        accountHolderName: bankAccount.accountHolderName,
        isDefault: bankAccount.isDefault,
        isVerified: bankAccount.isVerified,
        createdAt: bankAccount.createdAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Error creating bank account:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
