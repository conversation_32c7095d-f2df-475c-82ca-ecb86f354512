import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    // Calculate date range
    const now = new Date();
    let startDate = new Date();

    switch (range) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Get developer's nodes (simplified without relations that might not exist)
    const nodes = await prisma.nodePlugin.findMany({
      where: { authorId: user.id }
    });

    // Calculate total metrics (simplified)
    const totalDownloads = nodes.reduce((sum, node) => sum + (node.downloads || 0), 0);
    const totalRevenue = nodes.reduce((sum, node) => sum + (node.price || 0) * (node.downloads || 0) * 0.1, 0); // Simplified revenue calculation

    const averageRating = nodes.length > 0
      ? nodes.reduce((sum, node) => sum + (node.rating || 0), 0) / nodes.length
      : 0;

    const totalNodes = nodes.length;

    // Calculate weekly downloads (simplified)
    const weeklyDownloads = nodes.reduce((sum, node) => sum + (node.weeklyDownloads || 0), 0);

    // Calculate monthly revenue (simplified)
    const monthlyRevenue = totalRevenue * 0.3; // Approximate 30% of total revenue as monthly

    // Get top performing nodes
    const topNodes = nodes
      .map(node => ({
        id: node.id,
        name: node.name,
        downloads: node.downloads || 0,
        revenue: (node.price || 0) * (node.downloads || 0) * 0.1,
        rating: node.rating || 0
      }))
      .sort((a, b) => b.downloads - a.downloads)
      .slice(0, 5);

    // Generate download trend data (simplified)
    const downloadTrend = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(now.getDate() - i);

      // Simplified trend calculation - in a real app, you'd have daily metrics
      const dailyDownloads = Math.floor(Math.random() * 50) + 10; // Mock data
      const dailyRevenue = Math.floor(Math.random() * 100) + 20; // Mock data

      downloadTrend.push({
        date: date.toISOString().split('T')[0],
        downloads: dailyDownloads,
        revenue: dailyRevenue
      });
    }

    const analytics = {
      totalDownloads,
      totalRevenue,
      averageRating,
      totalNodes,
      weeklyDownloads,
      monthlyRevenue,
      topNodes,
      downloadTrend
    };

    return NextResponse.json(analytics);

  } catch (error) {
    console.error('Analytics fetch error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
