import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { nodeAnalytics } from '@/lib/node-analytics';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');

    const stats = await nodeAnalytics.getSystemAnalytics(days);

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error getting system analytics:', error);
    return NextResponse.json(
      { error: 'Failed to get system analytics' },
      { status: 500 }
    );
  }
}
