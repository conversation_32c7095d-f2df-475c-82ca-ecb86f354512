import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // Find all workflows that contain this node type
    const workflows = await prisma.workflow.findMany({
      where: {
        userId: user.id
      },
      select: {
        id: true,
        name: true,
        nodes: true,
        edges: true
      }
    });

    let removedNodes = 0;
    let updatedWorkflows = 0;
    const affectedWorkflows: string[] = [];

    for (const workflow of workflows) {
      try {
        const nodes = JSON.parse(workflow.nodes || '[]');
        const edges = JSON.parse(workflow.edges || '[]');

        // Find nodes of this type
        const nodesToRemove = nodes.filter((node: any) =>
          node.type === nodeId || node.data?.nodeType === nodeId
        );

        if (nodesToRemove.length === 0) {
          continue; // No nodes of this type in this workflow
        }

        // Get IDs of nodes to remove
        const nodeIdsToRemove = nodesToRemove.map((node: any) => node.id);

        // Remove nodes
        const filteredNodes = nodes.filter((node: any) =>
          !nodeIdsToRemove.includes(node.id)
        );

        // Remove edges connected to these nodes
        const filteredEdges = edges.filter((edge: any) =>
          !nodeIdsToRemove.includes(edge.source) &&
          !nodeIdsToRemove.includes(edge.target)
        );

        // Update workflow
        await prisma.workflow.update({
          where: { id: workflow.id },
          data: {
            nodes: JSON.stringify(filteredNodes),
            edges: JSON.stringify(filteredEdges),
            updatedAt: new Date()
          }
        });

        removedNodes += nodesToRemove.length;
        updatedWorkflows++;
        affectedWorkflows.push(workflow.name);

      } catch (error) {
        console.warn(`Failed to process workflow ${workflow.id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Removed ${removedNodes} nodes from ${updatedWorkflows} workflows`,
      removedNodes,
      updatedWorkflows,
      affectedWorkflows
    });

  } catch (error) {
    console.error('Remove from workflows error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
