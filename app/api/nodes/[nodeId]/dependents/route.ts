import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // Find all nodes that depend on this node
    const allNodes = await prisma.nodePlugin.findMany({
      select: {
        id: true,
        name: true,
        dependencies: true
      }
    });

    const dependents: string[] = [];

    // Check each node's dependencies
    for (const node of allNodes) {
      try {
        const dependencies = JSON.parse(node.dependencies || '[]');
        const dependsOnTarget = dependencies.some((dep: any) => dep.nodeId === nodeId);

        if (dependsOnTarget) {
          // Check if this dependent node is installed by the user
          const isInstalled = await prisma.installedNode.findFirst({
            where: {
              userId: user.id,
              nodeId: node.id,
              status: 'installed'
            }
          });

          if (isInstalled) {
            dependents.push(node.name);
          }
        }
      } catch (error) {
        // Skip nodes with invalid dependency JSON
        console.warn(`Invalid dependencies for node ${node.id}:`, error);
      }
    }

    return NextResponse.json({ dependents });

  } catch (error) {
    console.error('Dependents check error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
