import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // First, check if this is an installed marketplace node
    const installedNode = await prisma.installedNode.findFirst({
      where: {
        userId: user.id,
        nodeId: nodeId,
        status: 'installed'
      },
      include: {
        node: true
      }
    });

    if (installedNode && installedNode.node) {
      // Get the node code from NodeCode table
      const nodeCode = await prisma.nodeCode.findFirst({
        where: {
          nodeId: nodeId,
          version: installedNode.node.version
        }
      });

      if (!nodeCode) {
        return NextResponse.json({
          error: 'Node code not found'
        }, { status: 404 });
      }

      // Return the complete node definition
      const definition = {
        id: installedNode.node.id,
        type: installedNode.node.id, // Use ID as type for marketplace nodes
        name: installedNode.node.name,
        description: installedNode.node.description,
        category: installedNode.node.category,
        version: installedNode.node.version,
        code: nodeCode.code,
        icon: installedNode.node.icon,
        inputs: [], // TODO: Extract from node code or store in NodePlugin
        outputs: [], // TODO: Extract from node code or store in NodePlugin
        properties: {}, // TODO: Extract from node code or store in NodePlugin
        dependencies: JSON.parse(installedNode.node.dependencies || '[]'),
        permissions: JSON.parse(installedNode.node.permissions || '[]'),
        metadata: {
          author: installedNode.node.authorId,
          tier: installedNode.node.tier,
          price: installedNode.node.price,
          rating: installedNode.node.rating,
          downloads: installedNode.node.downloads,
          lastUpdated: installedNode.node.lastUpdated,
          compatibility: JSON.parse(installedNode.node.compatibility || '{}')
        }
      };

      return NextResponse.json(definition);
    }

    // If not found as installed node, check if it's a built-in node type
    // This would require integration with the node registry
    // For now, return a generic built-in node definition

    const builtInNodes: Record<string, any> = {
      'textInput': {
        id: 'textInput',
        type: 'textInput',
        name: 'Text Input',
        description: 'Input text data',
        category: 'input',
        version: '1.0.0',
        code: `
          function executeNode(env) {
            const { nodeData, inputs } = env;
            return {
              value: nodeData.value || inputs.value || ''
            };
          }
        `,
        icon: 'Type',
        inputs: [],
        outputs: [
          { id: 'value', name: 'Text Value', type: 'string' }
        ],
        properties: {
          value: { type: 'string', default: '', label: 'Text Value' }
        }
      },
      'textOutput': {
        id: 'textOutput',
        type: 'textOutput',
        name: 'Text Output',
        description: 'Display text data',
        category: 'output',
        version: '1.0.0',
        code: `
          function executeNode(env) {
            const { inputs } = env;
            return {
              displayValue: inputs.inputValue || inputs.value || ''
            };
          }
        `,
        icon: 'FileText',
        inputs: [
          { id: 'inputValue', name: 'Input Value', type: 'string' }
        ],
        outputs: [],
        properties: {}
      },
      'numberInput': {
        id: 'numberInput',
        type: 'numberInput',
        name: 'Number Input',
        description: 'Input numeric data',
        category: 'input',
        version: '1.0.0',
        code: `
          function executeNode(env) {
            const { nodeData, inputs } = env;
            const value = nodeData.value || inputs.value || 0;
            return {
              value: parseFloat(value) || 0
            };
          }
        `,
        icon: 'Hash',
        inputs: [],
        outputs: [
          { id: 'value', name: 'Number Value', type: 'number' }
        ],
        properties: {
          value: { type: 'number', default: 0, label: 'Number Value' }
        }
      }
    };

    const builtInDefinition = builtInNodes[nodeId];
    if (builtInDefinition) {
      return NextResponse.json(builtInDefinition);
    }

    return NextResponse.json({
      error: 'Node definition not found'
    }, { status: 404 });

  } catch (error) {
    console.error('Node definition fetch error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
