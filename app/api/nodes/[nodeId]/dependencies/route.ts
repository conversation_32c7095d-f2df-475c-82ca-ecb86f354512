import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const version = searchParams.get('version');

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // Get node plugin
    const nodePlugin = await prisma.nodePlugin.findUnique({
      where: { id: nodeId }
    });

    if (!nodePlugin) {
      return NextResponse.json({ error: 'Node not found' }, { status: 404 });
    }

    // Parse dependencies from node metadata
    const dependencies = JSON.parse(nodePlugin.dependencies || '[]');

    // Check which dependencies are already installed
    const dependencyChecks = await Promise.all(
      dependencies.map(async (dep: any) => {
        const isInstalled = await prisma.installedNode.findFirst({
          where: {
            userId: user.id,
            nodeId: dep.nodeId,
            status: 'installed'
          }
        });

        return {
          nodeId: dep.nodeId,
          version: dep.version || 'latest',
          required: dep.required !== false,
          installed: !!isInstalled
        };
      })
    );

    return NextResponse.json(dependencyChecks);

  } catch (error) {
    console.error('Dependencies check error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
