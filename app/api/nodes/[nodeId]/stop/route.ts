import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // Find all workflows that contain this node type
    const workflows = await prisma.workflow.findMany({
      where: {
        userId: user.id
      },
      select: {
        id: true,
        nodes: true
      }
    });

    let stoppedInstances = 0;

    for (const workflow of workflows) {
      try {
        const nodes = JSON.parse(workflow.nodes || '[]');
        let hasChanges = false;

        // Find nodes of this type and mark them as stopped
        const updatedNodes = nodes.map((node: any) => {
          if (node.type === nodeId || node.data?.nodeType === nodeId) {
            stoppedInstances++;
            hasChanges = true;
            return {
              ...node,
              data: {
                ...node.data,
                status: 'stopped',
                stoppedAt: new Date().toISOString()
              }
            };
          }
          return node;
        });

        // Update workflow if changes were made
        if (hasChanges) {
          await prisma.workflow.update({
            where: { id: workflow.id },
            data: {
              nodes: JSON.stringify(updatedNodes),
              updatedAt: new Date()
            }
          });
        }
      } catch (error) {
        console.warn(`Failed to process workflow ${workflow.id}:`, error);
      }
    }

    // TODO: In a real implementation, this would also:
    // - Stop any running Web Workers
    // - Cancel ongoing operations
    // - Clean up event listeners
    // - Release resources

    return NextResponse.json({
      success: true,
      message: `Stopped ${stoppedInstances} node instances`,
      stoppedInstances
    });

  } catch (error) {
    console.error('Stop node instances error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
