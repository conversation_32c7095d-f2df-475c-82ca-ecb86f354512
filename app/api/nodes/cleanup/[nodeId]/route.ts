import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // Clean up failed installation
    try {
      // Remove any failed installation records
      await prisma.installedNode.deleteMany({
        where: {
          userId: user.id,
          nodeId: nodeId,
          status: 'failed'
        }
      });

      // Remove from user library if it exists
      await prisma.userLibrary.deleteMany({
        where: {
          userId: user.id,
          nodeId: nodeId
        }
      });

      // TODO: Clean up any temporary files or cached data
      // This would involve:
      // - Removing downloaded packages
      // - Clearing node loader cache
      // - Cleaning up any temporary directories

      return NextResponse.json({
        success: true,
        message: 'Cleanup completed successfully'
      });

    } catch (cleanupError) {
      console.error('Cleanup error:', cleanupError);
      return NextResponse.json({
        error: 'Cleanup failed',
        details: cleanupError instanceof Error ? cleanupError.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Cleanup endpoint error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
