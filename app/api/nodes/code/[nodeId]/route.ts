import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { nodeId } = await params;
    const { searchParams } = new URL(request.url);
    const version = searchParams.get('version');

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user has the node installed
    const installation = await prisma.installedNode.findUnique({
      where: {
        userId_nodeId: {
          userId: user.id,
          nodeId: nodeId
        }
      }
    });

    if (!installation || installation.status !== 'installed') {
      return NextResponse.json({
        error: 'Node not installed or not ready'
      }, { status: 403 });
    }

    // Get node code
    const nodeCode = await prisma.nodeCode.findFirst({
      where: {
        nodeId: nodeId,
        ...(version && { version })
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!nodeCode) {
      return NextResponse.json({
        error: 'Node code not found'
      }, { status: 404 });
    }

    // Update last used timestamp
    await prisma.installedNode.update({
      where: { id: installation.id },
      data: { lastUsed: new Date() }
    });

    return NextResponse.json({
      success: true,
      nodeCode: {
        id: nodeCode.id,
        nodeId: nodeCode.nodeId,
        version: nodeCode.version,
        code: nodeCode.code,
        dependencies: nodeCode.dependencies,
        permissions: nodeCode.permissions,
        checksum: nodeCode.checksum
      }
    });

  } catch (error) {
    console.error('Get node code error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
