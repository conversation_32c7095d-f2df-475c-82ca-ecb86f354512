import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { nodeId, version } = await request.json();

    if (!nodeId) {
      return NextResponse.json({ error: 'Node ID is required' }, { status: 400 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get node plugin
    const nodePlugin = await prisma.nodePlugin.findUnique({
      where: { id: nodeId },
      include: {
        nodeCodes: {
          where: version ? { version } : {},
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    if (!nodePlugin) {
      return NextResponse.json({ error: 'Node not found' }, { status: 404 });
    }

    // Check if node is already installed
    const existingInstallation = await prisma.installedNode.findUnique({
      where: {
        userId_nodeId: {
          userId: user.id,
          nodeId: nodeId
        }
      }
    });

    if (existingInstallation) {
      return NextResponse.json({
        error: 'Node already installed',
        installation: existingInstallation
      }, { status: 409 });
    }

    // Check if user has access to the node (free, purchased, or subscribed)
    const hasAccess = await checkNodeAccess(user.id, nodePlugin);
    if (!hasAccess) {
      return NextResponse.json({
        error: 'Access denied. Please purchase or subscribe to this node.'
      }, { status: 403 });
    }

    // Get node code
    const nodeCode = nodePlugin.nodeCodes[0];
    if (!nodeCode) {
      return NextResponse.json({
        error: 'Node code not available'
      }, { status: 404 });
    }

    // Create installation record
    const installation = await prisma.installedNode.create({
      data: {
        userId: user.id,
        nodeId: nodeId,
        version: nodeCode.version,
        status: 'installing',
        installPath: `/nodes/${user.id}/${nodeId}`,
        config: {},
        enabled: true
      }
    });

    // Simulate installation process (in real implementation, this would be more complex)
    try {
      // Validate node code
      const isValid = validateNodeCode(nodeCode.code, nodeCode.checksum);
      if (!isValid) {
        throw new Error('Node code validation failed');
      }

      // Update installation status
      await prisma.installedNode.update({
        where: { id: installation.id },
        data: {
          status: 'installed',
          updatedAt: new Date()
        }
      });

      // Add to user library for backward compatibility
      await prisma.userLibrary.upsert({
        where: {
          userId_nodeId: {
            userId: user.id,
            nodeId: nodeId
          }
        },
        create: {
          userId: user.id,
          nodeId: nodeId,
          version: nodeCode.version,
          enabled: true
        },
        update: {
          version: nodeCode.version,
          enabled: true
        }
      });

      return NextResponse.json({
        success: true,
        installation: {
          ...installation,
          status: 'installed'
        },
        node: nodePlugin
      });

    } catch (error) {
      // Update installation status to failed
      await prisma.installedNode.update({
        where: { id: installation.id },
        data: {
          status: 'failed',
          updatedAt: new Date()
        }
      });

      console.error('Node installation failed:', error);
      return NextResponse.json({
        error: 'Installation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Node installation error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// Helper function to check if user has access to the node
async function checkNodeAccess(userId: string, nodePlugin: any): Promise<boolean> {
  // Free nodes are always accessible
  if (nodePlugin.tier === 'free') {
    return true;
  }

  // Check if user has purchased the node
  const purchase = await prisma.nodePurchase.findFirst({
    where: {
      userId: userId,
      nodeId: nodePlugin.id,
      status: 'completed'
    }
  });

  if (purchase) {
    return true;
  }

  // Check if user has an active subscription that includes this node
  const subscription = await prisma.subscription.findFirst({
    where: {
      userId: userId,
      status: 'active'
    }
  });

  if (subscription && nodePlugin.tier === 'subscription') {
    return true;
  }

  return false;
}

// Helper function to validate node code integrity
function validateNodeCode(code: string, expectedChecksum: string): boolean {
  const actualChecksum = crypto.createHash('sha256').update(code).digest('hex');
  return actualChecksum === expectedChecksum;
}
