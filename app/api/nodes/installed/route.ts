import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get installed nodes with node details
    const installedNodes = await prisma.installedNode.findMany({
      where: {
        userId: user.id
      },
      include: {
        node: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        }
      },
      orderBy: {
        installedAt: 'desc'
      }
    });

    // Transform the data for frontend consumption
    const transformedNodes = installedNodes.map(installation => ({
      id: installation.id,
      nodeId: installation.nodeId,
      version: installation.version,
      status: installation.status,
      enabled: installation.enabled,
      config: installation.config,
      installedAt: installation.installedAt,
      updatedAt: installation.updatedAt,
      lastUsed: installation.lastUsed,
      node: {
        id: installation.node.id,
        name: installation.node.name,
        description: installation.node.description,
        icon: installation.node.icon,
        category: installation.node.category,
        tier: installation.node.tier,
        version: installation.node.version,
        tags: JSON.parse(installation.node.tags || '[]'),
        author: installation.node.author,
        verified: installation.node.verified,
        rating: installation.node.rating,
        downloads: installation.node.downloads
      }
    }));

    return NextResponse.json({
      success: true,
      installedNodes: transformedNodes,
      total: transformedNodes.length
    });

  } catch (error) {
    console.error('Get installed nodes error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { nodeId, enabled, config } = await request.json();

    if (!nodeId) {
      return NextResponse.json({ error: 'Node ID is required' }, { status: 400 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Update installed node
    const updatedInstallation = await prisma.installedNode.update({
      where: {
        userId_nodeId: {
          userId: user.id,
          nodeId: nodeId
        }
      },
      data: {
        ...(enabled !== undefined && { enabled }),
        ...(config !== undefined && { config }),
        updatedAt: new Date()
      },
      include: {
        node: true
      }
    });

    return NextResponse.json({
      success: true,
      installation: updatedInstallation
    });

  } catch (error) {
    console.error('Update installed node error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
