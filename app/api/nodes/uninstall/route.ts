import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { nodeId } = await request.json();

    if (!nodeId) {
      return NextResponse.json({ error: 'Node ID is required' }, { status: 400 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if node is installed
    const installation = await prisma.installedNode.findUnique({
      where: {
        userId_nodeId: {
          userId: user.id,
          nodeId: nodeId
        }
      }
    });

    if (!installation) {
      return NextResponse.json({
        error: 'Node not installed'
      }, { status: 404 });
    }

    // Update installation status to uninstalling
    await prisma.installedNode.update({
      where: { id: installation.id },
      data: {
        status: 'uninstalling',
        updatedAt: new Date()
      }
    });

    try {
      // Perform uninstallation (cleanup, remove files, etc.)
      // In a real implementation, this would involve:
      // - Stopping any running instances
      // - Cleaning up temporary files
      // - Removing cached data
      // - Updating workflow dependencies

      // Remove from installed nodes
      await prisma.installedNode.delete({
        where: { id: installation.id }
      });

      // Update user library
      await prisma.userLibrary.updateMany({
        where: {
          userId: user.id,
          nodeId: nodeId
        },
        data: {
          enabled: false
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Node uninstalled successfully'
      });

    } catch (error) {
      // Revert status if uninstallation fails
      await prisma.installedNode.update({
        where: { id: installation.id },
        data: {
          status: 'installed',
          updatedAt: new Date()
        }
      });

      console.error('Node uninstallation failed:', error);
      return NextResponse.json({
        error: 'Uninstallation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Node uninstallation error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
