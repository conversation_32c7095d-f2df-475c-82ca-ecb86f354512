import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { nodeUpdater } from '@/lib/node-updater';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    const updateInfos = await nodeUpdater.checkForUpdates(user.id);

    return NextResponse.json({
      updates: updateInfos,
      stats: await nodeUpdater.getUpdateStats(user.id)
    });

  } catch (error) {
    console.error('Error checking for updates:', error);
    return NextResponse.json(
      { error: 'Failed to check for updates' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    const body = await request.json();
    const { nodeId, updateAll } = body;

    if (updateAll) {
      // Update all non-breaking changes
      const results = await nodeUpdater.updateAllNodes(user.id);
      return NextResponse.json({ results });
    } else if (nodeId) {
      // Update specific node
      const result = await nodeUpdater.updateNode(user.id, nodeId);
      return NextResponse.json({ result });
    } else {
      return NextResponse.json(
        { error: 'Missing nodeId or updateAll parameter' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error updating nodes:', error);
    return NextResponse.json(
      { error: 'Failed to update nodes' },
      { status: 500 }
    );
  }
}
