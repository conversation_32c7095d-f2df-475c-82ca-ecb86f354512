import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

// GET /api/workflow - Get all workflows (public access)
export async function GET(request: NextRequest) {
  try {
    // Get all workflows (no authentication required)
    const workflows = await prisma.workflow.findMany({
      orderBy: { updatedAt: 'desc' },
    });

    return NextResponse.json(workflows, { status: 200 });
  } catch (error) {
    console.error('Error fetching workflows:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflows' },
      { status: 500 }
    );
  }
}

// POST /api/workflow - Create a new workflow
export async function POST(request: NextRequest) {
  try {
    // Get session to identify the user
    const session = await getServerSession(authOptions);

    // Get workflow data from request
    const { name, description, nodes, edges } = await request.json();

    // Validate input
    if (!name) {
      return NextResponse.json(
        { error: 'Workflow name is required' },
        { status: 400 }
      );
    }

    let userId: string;

    if (session?.user?.email) {
      // Get authenticated user
      const user = await prisma.user.findUnique({
        where: { email: session.user.email }
      });

      if (!user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      userId = user.id;
    } else {
      // For unauthenticated users, create or get anonymous user
      let anonymousUser = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      });

      if (!anonymousUser) {
        // Create anonymous user if it doesn't exist
        anonymousUser = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            username: 'anonymous',
            name: 'Anonymous User',
            emailVerified: true, // Skip verification for anonymous user
          }
        });
      }

      userId = anonymousUser.id;
    }

    // Create workflow
    const workflow = await prisma.workflow.create({
      data: {
        name,
        description,
        nodes: nodes ? JSON.stringify(nodes) : JSON.stringify([]),
        edges: edges ? JSON.stringify(edges) : JSON.stringify([]),
        userId,
      },
    });

    return NextResponse.json(workflow, { status: 201 });
  } catch (error) {
    console.error('Error creating workflow:', error);
    return NextResponse.json(
      { error: 'Failed to create workflow' },
      { status: 500 }
    );
  }
}
