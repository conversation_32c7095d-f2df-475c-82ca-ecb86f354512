import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { PrismaClient } from '@prisma/client';
import { WorkflowExecutionQueue } from '@/lib/workflow/execution-queue';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    console.log(`[Queue Management] Getting queue data for user: ${userId}`);

    // Get execution queue instance
    const executionQueue = WorkflowExecutionQueue.getInstance();
    const queueStats = executionQueue.getStats();

    // Get all executions from database (recent ones)
    const executions = await prisma.workflowExecution.findMany({
      include: {
        workflow: {
          select: {
            name: true
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: { startTime: 'desc' },
      take: 100 // Get last 100 executions
    });

    // Format executions for the UI
    const formattedExecutions = executions.map(execution => ({
      id: execution.id,
      workflowId: execution.workflowId,
      workflowName: execution.workflow?.name || `Workflow ${execution.workflowId}`,
      status: execution.status,
      startTime: execution.startTime.toISOString(),
      endTime: execution.endTime?.toISOString(),
      progress: execution.progress || (execution.status === 'completed' ? 100 : execution.status === 'failed' ? 0 : 50),
      error: execution.error,
      userId: execution.userId,
      userName: execution.user?.name || execution.user?.email || 'Unknown User',
      duration: execution.endTime && execution.startTime 
        ? execution.endTime.getTime() - execution.startTime.getTime()
        : null
    }));

    // Calculate real queue statistics from database
    const statusCounts = await prisma.workflowExecution.groupBy({
      by: ['status'],
      _count: {
        status: true
      },
      where: {
        startTime: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      }
    });

    const realQueueStats = {
      totalQueued: statusCounts.find(s => s.status === 'queued')?._count.status || queueStats.totalQueued,
      totalRunning: statusCounts.find(s => s.status === 'running')?._count.status || queueStats.totalRunning,
      totalCompleted: statusCounts.find(s => s.status === 'completed')?._count.status || queueStats.totalCompleted,
      totalFailed: statusCounts.find(s => s.status === 'failed')?._count.status || queueStats.totalFailed
    };

    return NextResponse.json({
      queueStats: realQueueStats,
      executions: formattedExecutions,
      totalExecutions: executions.length
    });

  } catch (error) {
    console.error('[Queue Management] Error getting queue data:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get queue data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, executionId } = body;

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id || 'anonymous';

    const executionQueue = WorkflowExecutionQueue.getInstance();

    switch (action) {
      case 'cancel':
        if (!executionId) {
          return NextResponse.json(
            { error: 'Execution ID is required for cancel action' },
            { status: 400 }
          );
        }

        const cancelled = await executionQueue.cancelExecution(executionId);
        
        if (cancelled) {
          return NextResponse.json({
            success: true,
            message: 'Execution cancelled successfully'
          });
        } else {
          return NextResponse.json(
            { error: 'Execution not found or cannot be cancelled' },
            { status: 404 }
          );
        }

      case 'retry':
        if (!executionId) {
          return NextResponse.json(
            { error: 'Execution ID is required for retry action' },
            { status: 400 }
          );
        }

        // Get the failed execution
        const failedExecution = await prisma.workflowExecution.findUnique({
          where: { id: executionId },
          include: {
            workflow: true
          }
        });

        if (!failedExecution || failedExecution.status !== 'failed') {
          return NextResponse.json(
            { error: 'Execution not found or not in failed state' },
            { status: 404 }
          );
        }

        // Parse workflow data
        const nodes = JSON.parse(failedExecution.workflow.nodes);
        const edges = JSON.parse(failedExecution.workflow.edges);
        const variables = JSON.parse(failedExecution.variables || '{}');
        const options = {
          mode: 'sequential',
          continueOnError: true,
          retryAttempts: 3,
          debugMode: false,
          maxConcurrentNodes: 3,
          ...JSON.parse(failedExecution.options || '{}')
        };

        // Create execution context
        const executionContext = {
          workflowId: failedExecution.workflowId,
          userId: failedExecution.userId,
          variables,
          secrets: {},
          settings: {},
          log: (message: string, level: string = 'info') => {
            console.log(`[Retry ${failedExecution.workflowId}] ${level.toUpperCase()}: ${message}`);
          }
        };

        // Queue the retry
        const retryExecutionId = await executionQueue.enqueue(
          failedExecution.workflowId,
          failedExecution.userId,
          nodes,
          edges,
          executionContext,
          options,
          7 // High priority for retries
        );

        return NextResponse.json({
          success: true,
          message: 'Execution retry queued successfully',
          retryExecutionId
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('[Queue Management] Error processing action:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
