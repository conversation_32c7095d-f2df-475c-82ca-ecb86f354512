"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Marketplace } from "@/components/marketplace/marketplace";
import { AppSidebar } from "@/components/app-sidebar";
import { InstallationProgressPanel } from "@/components/marketplace/installation-progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, XCircle, AlertTriangle } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"

export default function MarketplacePage() {
  const [isInstalling, setIsInstalling] = useState<string | null>(null);
  const [paymentAlert, setPaymentAlert] = useState<{ type: 'success' | 'error' | 'cancelled'; message: string } | null>(null);
  const searchParams = useSearchParams();

  useEffect(() => {
    // Check for payment status in URL params
    const paymentStatus = searchParams.get('payment');
    const paymentType = searchParams.get('type');

    if (paymentStatus) {
      switch (paymentStatus) {
        case 'success':
          setPaymentAlert({
            type: 'success',
            message: paymentType === 'subscription'
              ? 'Subscription activated successfully!'
              : 'Node purchased successfully!'
          });
          break;
        case 'cancelled':
          setPaymentAlert({
            type: 'cancelled',
            message: paymentType === 'subscription'
              ? 'Subscription payment was cancelled.'
              : 'Node purchase was cancelled.'
          });
          break;
        case 'error':
          setPaymentAlert({
            type: 'error',
            message: 'Payment processing failed. Please try again.'
          });
          break;
      }

      // Clear alert after 5 seconds
      setTimeout(() => setPaymentAlert(null), 5000);
    }
  }, [searchParams]);

  const handleInstallNode = async (nodeId: string) => {
    setIsInstalling(nodeId);

    try {
      // Simulate installation process
      console.log("Installing node:", nodeId);

      // TODO: Implement actual installation logic
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log("Node installed successfully:", nodeId);
    } catch (error) {
      console.error("Failed to install node:", error);
    } finally {
      setIsInstalling(null);
    }
  };

  const handlePurchaseNode = async (nodeId: string) => {
    try {
      console.log("Redirecting to payment for node:", nodeId);

      // TODO: Implement payment flow
      await new Promise(resolve => setTimeout(resolve, 1000));

      // After successful payment, install the node
      await handleInstallNode(nodeId);
    } catch (error) {
      console.error("Payment failed:", error);
    }
  };

  const handleNodeToggle = (nodeId: string, enabled: boolean) => {
    console.log(`Node ${nodeId} ${enabled ? 'enabled' : 'disabled'}`);
  };

  const handleNodeUninstall = (nodeId: string) => {
    console.log("Node uninstalled:", nodeId);
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbPage>Marketplace</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="mb-4">
            <h1 className="text-3xl font-bold">Marketplace</h1>
            <p className="text-muted-foreground">Discover and install workflow nodes to extend your automation capabilities</p>
          </div>

          {/* Payment Status Alert */}
          {paymentAlert && (
            <Alert className={`mb-4 ${
              paymentAlert.type === 'success' ? 'border-green-500 bg-green-50 dark:bg-green-950/20' :
              paymentAlert.type === 'cancelled' ? 'border-orange-500 bg-orange-50 dark:bg-orange-950/20' :
              'border-red-500 bg-red-50 dark:bg-red-950/20'
            }`}>
              {paymentAlert.type === 'success' && <CheckCircle className="h-4 w-4 text-green-600" />}
              {paymentAlert.type === 'cancelled' && <AlertTriangle className="h-4 w-4 text-orange-600" />}
              {paymentAlert.type === 'error' && <XCircle className="h-4 w-4 text-red-600" />}
              <AlertDescription className={
                paymentAlert.type === 'success' ? 'text-green-700 dark:text-green-300' :
                paymentAlert.type === 'cancelled' ? 'text-orange-700 dark:text-orange-300' :
                'text-red-700 dark:text-red-300'
              }>
                {paymentAlert.message}
              </AlertDescription>
            </Alert>
          )}

          <Marketplace
            onInstallNode={handleInstallNode}
            onPurchaseNode={handlePurchaseNode}
          />
        </div>
      </SidebarInset>

      {/* Installation Progress Panel */}
      <InstallationProgressPanel />
    </SidebarProvider>
  );
}
