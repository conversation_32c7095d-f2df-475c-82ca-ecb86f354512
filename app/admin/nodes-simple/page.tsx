"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, Package, Check, X } from "lucide-react";

export default function AdminNodesPageSimple() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [nodes, setNodes] = useState([]);
  const [error, setError] = useState<string | null>(null);

  // Check admin access
  useEffect(() => {
    if (status === "loading") return;
    
    if (!session?.user) {
      router.push("/auth/signin");
      return;
    }
  }, [session, status, router]);

  // Load nodes data
  useEffect(() => {
    if (!session?.user) return;

    const loadNodes = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/admin/nodes/pending?status=pending&limit=10', {
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('API Response:', data);
        
        setNodes(data.data?.nodes || []);
      } catch (error) {
        console.error('Error loading nodes:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    loadNodes();
  }, [session]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!session?.user) {
    return null;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Node Approval System (Simple)</h1>
          <p className="text-muted-foreground">Review and approve developer-submitted nodes</p>
        </div>
        <Button onClick={() => router.push('/admin/nodes')}>
          Back to Full Version
        </Button>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-700">Error: {error}</p>
          </CardContent>
        </Card>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading nodes...</span>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Pending Nodes ({nodes.length})</CardTitle>
            <CardDescription>Nodes waiting for approval</CardDescription>
          </CardHeader>
          <CardContent>
            {nodes.length === 0 ? (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No pending nodes found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {nodes.map((node: any) => (
                  <div key={node.id} className="border rounded p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold">{node.name}</h3>
                        <p className="text-sm text-muted-foreground">{node.description}</p>
                        <Badge variant="outline" className="mt-2">
                          {node.approvalStatus || 'pending'}
                        </Badge>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" className="bg-green-600 hover:bg-green-700">
                          <Check className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-600 border-red-600">
                          <X className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
