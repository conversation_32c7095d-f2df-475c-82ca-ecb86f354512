"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbLink,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  Users,
  Activity,
  Shield,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Calendar,
  LogIn,
  UserCheck
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Analytics {
  users?: {
    total: number;
    active: number;
    new: number;
    byStatus: Array<{ status: string; count: number }>;
    growth: Array<{ date: string; count: number }>;
  };
  activity?: {
    total: number;
    byAction: Array<{ action: string; count: number }>;
    byResource: Array<{ resource: string; count: number }>;
    timeline: Array<{ date: string; count: number }>;
  };
  logins?: {
    total: number;
    successful: number;
    failed: number;
    uniqueUsers: number;
    successRate: string;
    timeline: Array<{ date: string; total: number; successful: number; failed: number }>;
  };
  system?: {
    totalRoles: number;
    totalPermissions: number;
    roleDistribution: Array<{ id: string; name: string; displayName: string; userCount: number }>;
    topRoles: Array<{ roleId: string; roleName: string; userCount: number }>;
  };
}

interface AnalyticsResponse {
  analytics: Analytics;
  period: string;
  startDate: string;
  endDate: string;
}

export default function AdminAnalytics() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('30d');

  // Check admin access
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session?.user) {
      router.push('/login');
      return;
    }

    const user = session.user as any;
    if (!user.isAdmin && !user.isSuperAdmin) {
      router.push('/dashboard');
      return;
    }

    loadAnalytics();
  }, [session, status, router, period]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/admin/analytics?period=${period}`);
      
      if (response.ok) {
        const data: AnalyticsResponse = await response.json();
        setAnalytics(data.analytics);
      } else {
        throw new Error('Failed to load analytics');
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getPeriodLabel = (period: string) => {
    switch (period) {
      case '7d': return 'Last 7 days';
      case '30d': return 'Last 30 days';
      case '90d': return 'Last 90 days';
      case '1y': return 'Last year';
      default: return 'Last 30 days';
    }
  };

  if (loading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/admin">Administration</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbItem>
                    <BreadcrumbPage>Analytics</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
            <p className="text-muted-foreground">Loading analytics...</p>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">Administration</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem>
                  <BreadcrumbPage>Analytics</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Welcome Section */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">System Analytics</h1>
              <p className="text-muted-foreground">
                View detailed analytics and system insights
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Select value={period} onValueChange={setPeriod}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={loadAnalytics} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          {analytics && (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* User Stats */}
              {analytics.users && (
                <>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{analytics.users.total}</div>
                      <p className="text-xs text-muted-foreground">
                        {analytics.users.new} new in {getPeriodLabel(period).toLowerCase()}
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                      <UserCheck className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{analytics.users.active}</div>
                      <p className="text-xs text-muted-foreground">
                        {((analytics.users.active / analytics.users.total) * 100).toFixed(1)}% of total users
                      </p>
                    </CardContent>
                  </Card>
                </>
              )}

              {/* Login Stats */}
              {analytics.logins && (
                <>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Login Success Rate</CardTitle>
                      <LogIn className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{analytics.logins.successRate}%</div>
                      <p className="text-xs text-muted-foreground">
                        {analytics.logins.successful} of {analytics.logins.total} attempts
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
                      <Activity className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{analytics.activity?.total || 0}</div>
                      <p className="text-xs text-muted-foreground">
                        In {getPeriodLabel(period).toLowerCase()}
                      </p>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          )}

          {/* Detailed Analytics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            {/* User Status Distribution */}
            {analytics?.users && (
              <Card className="col-span-3">
                <CardHeader>
                  <CardTitle>User Status Distribution</CardTitle>
                  <CardDescription>Users by account status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.users.byStatus.map((status) => (
                      <div key={status.status} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="capitalize">
                            {status.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        <div className="text-sm font-medium">{status.count}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Top Activities */}
            {analytics?.activity && (
              <Card className="col-span-4">
                <CardHeader>
                  <CardTitle>Top Activities</CardTitle>
                  <CardDescription>Most frequent user actions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.activity.byAction.slice(0, 5).map((action) => (
                      <div key={action.action} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="capitalize">
                            {action.action.replace('_', ' ')}
                          </Badge>
                        </div>
                        <div className="text-sm font-medium">{action.count}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* System Overview */}
          <div className="grid gap-4 md:grid-cols-2">
            {/* Role Distribution */}
            {analytics?.system && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Role Distribution
                  </CardTitle>
                  <CardDescription>Users assigned to each role</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.system.roleDistribution.map((role) => (
                      <div key={role.id} className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{role.displayName}</div>
                          <div className="text-sm text-muted-foreground">{role.name}</div>
                        </div>
                        <Badge variant="secondary">{role.userCount} users</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Resource Usage */}
            {analytics?.activity && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Resource Usage
                  </CardTitle>
                  <CardDescription>Most accessed system resources</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.activity.byResource.slice(0, 5).map((resource) => (
                      <div key={resource.resource} className="flex items-center justify-between">
                        <Badge variant="outline" className="capitalize">
                          {resource.resource}
                        </Badge>
                        <div className="text-sm font-medium">{resource.count} accesses</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* System Stats */}
          {analytics?.system && (
            <Card>
              <CardHeader>
                <CardTitle>System Configuration</CardTitle>
                <CardDescription>Current system setup and configuration</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{analytics.system.totalRoles}</div>
                    <div className="text-sm text-muted-foreground">Total Roles</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{analytics.system.totalPermissions}</div>
                    <div className="text-sm text-muted-foreground">Total Permissions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{analytics.users?.total || 0}</div>
                    <div className="text-sm text-muted-foreground">Total Users</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{analytics.activity?.total || 0}</div>
                    <div className="text-sm text-muted-foreground">Total Activities</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
