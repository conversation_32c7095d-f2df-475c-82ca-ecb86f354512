"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, CheckCircle, AlertCircle, UserPlus } from "lucide-react";

export default function SeedPermissionsPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Developer role assignment state
  const [assignLoading, setAssignLoading] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const [assignResult, setAssignResult] = useState<any>(null);
  const [assignError, setAssignError] = useState<string | null>(null);

  const seedPermissions = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/admin/seed-developer-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to seed permissions');
      }

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('Error seeding permissions:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const assignDeveloperRole = async () => {
    try {
      setAssignLoading(true);
      setAssignError(null);
      setAssignResult(null);

      if (!userEmail.trim()) {
        throw new Error('Please enter a user email');
      }

      const response = await fetch('/api/admin/assign-developer-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userEmail: userEmail.trim() })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to assign developer role');
      }

      const data = await response.json();
      setAssignResult(data);
      setUserEmail(""); // Clear the input
    } catch (error) {
      console.error('Error assigning developer role:', error);
      setAssignError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setAssignLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Seed Developer Permissions</h1>
        <p className="text-muted-foreground">Initialize developer roles and permissions in the database</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Permission Seeding</CardTitle>
          <CardDescription>
            This will create all system permissions and roles, including developer permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={seedPermissions}
            disabled={loading}
            className="w-full"
          >
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Seed Developer Permissions
          </Button>

          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <p className="text-red-700">Error: {error}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {result && (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-4">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <p className="text-green-700 font-semibold">{result.message}</p>
                </div>

                <div className="space-y-2 text-sm">
                  <p><strong>Permissions Created:</strong> {result.stats.permissionsCreated}</p>
                  <p><strong>Roles Created:</strong> {result.stats.rolesCreated}</p>

                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Role Details:</h4>
                    <div className="space-y-2">
                      {result.stats.roles.map((role: any) => (
                        <div key={role.name} className="border rounded p-2 bg-white">
                          <p><strong>{role.displayName}</strong></p>
                          <p className="text-xs text-muted-foreground">
                            {role.permissionCount} permissions, {role.userCount} users
                          </p>
                          {role.users.length > 0 && (
                            <div className="mt-1">
                              <p className="text-xs font-medium">Users:</p>
                              {role.users.map((user: any) => (
                                <p key={user.id} className="text-xs text-muted-foreground ml-2">
                                  - {user.name} ({user.email})
                                </p>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Assign Developer Role
          </CardTitle>
          <CardDescription>
            Assign the developer role to a user so they can access developer pages and upload nodes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="userEmail">User Email</Label>
            <Input
              id="userEmail"
              type="email"
              placeholder="<EMAIL>"
              value={userEmail}
              onChange={(e) => setUserEmail(e.target.value)}
              disabled={assignLoading}
            />
          </div>

          <Button
            onClick={assignDeveloperRole}
            disabled={assignLoading || !userEmail.trim()}
            className="w-full"
          >
            {assignLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Assign Developer Role
          </Button>

          {assignError && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <p className="text-red-700">Error: {assignError}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {assignResult && (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <p className="text-green-700 font-semibold">{assignResult.message}</p>
                </div>

                <div className="text-sm">
                  <p><strong>User:</strong> {assignResult.user.name} ({assignResult.user.email})</p>
                  <p><strong>Roles:</strong> {assignResult.user.roles.join(', ')}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
