'use client';

import { useSession } from 'next-auth/react';
import { FormEvent, useState, useCallback, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  AlertCircle,
  BadgeCheck,
  UserIcon,
  ShieldIcon,
  PencilIcon,
  CrownIcon,
  CalendarIcon,
  MailIcon,
  WorkflowIcon,
  PlusIcon,
  SettingsIcon,
  StarIcon
} from 'lucide-react';
import Image from 'next/image';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import Link from 'next/link';
import { AppSidebar } from "@/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"

// Subscription interface
interface Subscription {
  id: string;
  planId: string;
  status: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  createdAt: string;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const [user, setUser] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isResendingVerification, setIsResendingVerification] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(true);

  // Memoize form data to prevent unnecessary re-renders
  const [formData, setFormData] = useState<{name: string; bio: string}>(() => ({
    name: user?.name || '',
    bio: user?.bio || '',
  }));

  // Password form data
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (status === 'authenticated' && session) {
        try {
          const response = await fetch('/api/user/profile');
          if (response.ok) {
            const userData = await response.json();
            setUser(userData);
            setFormData({
              name: userData.name || '',
              bio: userData.bio || '',
            });
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
          setError('Failed to load profile data');
        }
      }
    };

    fetchUserProfile();
  }, [session, status]);

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        bio: user.bio || '',
      });
    }
  }, [user]);

  // Fetch subscription data
  useEffect(() => {
    const fetchSubscription = async () => {
      if (!user) return;

      try {
        const response = await fetch('/api/payments/subscriptions');
        if (response.ok) {
          const data = await response.json();
          if (data.subscriptions && data.subscriptions.length > 0) {
            setSubscription(data.subscriptions[0]); // Get the most recent subscription
          }
        }
      } catch (error) {
        console.error('Error fetching subscription:', error);
      } finally {
        setIsLoadingSubscription(false);
      }
    };

    fetchSubscription();
  }, [user]);

  // Memoize handleChange to prevent recreation on each render
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));
    },
    []
  );

  // Memoize handleSubmit to prevent recreation on each render
  const handleSubmit = useCallback(async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        setUser(updatedUser);
        setIsDialogOpen(false);
        setSuccess('Profile updated successfully!');
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setError('Failed to update profile');
    } finally {
      setIsLoading(false);
    }

    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccess(null);
    }, 3000);
  }, [formData]);

  // Handle password form changes
  const handlePasswordChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setPasswordData(prev => ({ ...prev, [name]: value }));
    },
    []
  );

  // Handle password change submission
  const handlePasswordSubmit = useCallback(async (e: FormEvent) => {
    e.preventDefault();

    // Validate passwords match
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setSuccess(null);
      // You might want to add a separate error state for password errors
      return;
    }

    // Validate password length
    if (passwordData.newPassword.length < 6) {
      setSuccess(null);
      return;
    }

    setIsChangingPassword(true);

    try {
      const response = await fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword,
        }),
      });

      if (response.ok) {
        setSuccess('Password changed successfully!');
        setIsPasswordDialogOpen(false);
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
      } else {
        const data = await response.json();
        setSuccess(null);
        console.error('Failed to change password:', data.error);
      }
    } catch (error) {
      console.error('Error changing password:', error);
      setSuccess(null);
    } finally {
      setIsChangingPassword(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    }
  }, [passwordData]);

  // Memoize handleResendVerification to prevent recreation on each render
  const handleResendVerification = useCallback(async () => {
    setIsResendingVerification(true);
    setSuccess(null);

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        setSuccess('Verification email sent! Please check your inbox.');
      } else {
        const data = await response.json();
        setSuccess(null);
        console.error('Failed to resend verification email:', data.error);
      }
    } catch (error) {
      if ((error as Error).name !== 'AbortError') {
        console.error('Error resending verification email:', error);
      }
    } finally {
      setIsResendingVerification(false);

      // Clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    }
  }, []);

  // Memoize getInitials function to prevent recreation on each render
  const getInitials = useCallback((name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  }, []);

  // Memoize userInitials to prevent recalculation on every render
  const userInitials = useMemo(() => {
    if (!user) return 'U';
    return user.name
      ? getInitials(user.name)
      : user.username
        ? user.username.substring(0, 2).toUpperCase()
        : 'U';
  }, [user, getInitials]);

  // Helper functions for subscription display
  const getPlanDisplayName = (planId: string) => {
    switch (planId) {
      case 'free':
        return 'Free Plan';
      case 'pro':
        return 'Pro Plan';
      case 'enterprise':
        return 'Enterprise Plan';
      default:
        return 'Unknown Plan';
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free':
        return <UserIcon className="h-4 w-4" />;
      case 'pro':
        return <StarIcon className="h-4 w-4" />;
      case 'enterprise':
        return <CrownIcon className="h-4 w-4" />;
      default:
        return <UserIcon className="h-4 w-4" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'canceled':
        return 'destructive';
      case 'past_due':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  // Show loading while session is loading or user data is being fetched
  if (status === 'loading' || (status === 'authenticated' && !user)) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <div className="max-w-md mx-auto py-10">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading profile...</p>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (status === 'unauthenticated' || !session) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbPage>Profile</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <div className="max-w-md mx-auto py-10">
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-xl">Authentication Required</CardTitle>
                  <CardDescription>
                    Please log in to view your profile
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <ShieldIcon className="h-16 w-16 text-muted-foreground mb-4" />
                  <p className="text-center text-muted-foreground mb-6">
                    You need to be logged in to access your profile information and account settings.
                  </p>
                </CardContent>
                <CardFooter className="flex justify-center">
                  <Button asChild>
                    <a href="/login">Login</a>
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbPage>Profile</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Header Section */}
          <div className="mb-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Profile</h1>
                <p className="text-muted-foreground mt-1">
                  Manage your account settings and subscription
                </p>
              </div>
              <Button asChild>
                <Link href="/workflow-manager" className="flex items-center gap-2">
                  <WorkflowIcon className="h-4 w-4" />
                  My Workflows
                </Link>
              </Button>
            </div>
          </div>

      {/* Alert Messages */}
      {error && (
        <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded-lg flex items-center gap-2 mb-6">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg flex items-center gap-2 mb-6">
          <CheckCircle className="h-4 w-4" />
          <span>{success}</span>
        </div>
      )}

      {/* Welcome Message for New Subscribers */}
      {subscription && subscription.createdAt &&
       new Date(subscription.createdAt).getTime() > Date.now() - 24 * 60 * 60 * 1000 && (
        <Card className="mb-6 border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold text-green-900">Welcome to {getPlanDisplayName(subscription.planId)}!</h3>
                <p className="text-green-700 text-sm">
                  You're all set! Start creating powerful workflows with your new subscription.
                </p>
              </div>
              <div className="ml-auto">
                <Button asChild size="sm">
                  <Link href="/workflows/new" className="flex items-center gap-2">
                    <PlusIcon className="h-4 w-4" />
                    Create Workflow
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Profile Card with Tabs - Full Width */}
      <Card className="w-full">
        {/* Profile Header */}
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage
                  src={user?.avatar || undefined}
                  alt={user?.name || user?.username || 'User'}
                />
                <AvatarFallback className="text-lg font-semibold">{userInitials}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-xl">{user?.name || user?.username || 'User'}</CardTitle>
                <CardDescription className="flex items-center gap-2 mt-1">
                  <MailIcon className="h-4 w-4" />
                  {user?.email || 'No email'}
                  {user?.emailVerified && (
                    <BadgeCheck className="h-5 w-5 text-blue-500" />
                  )}
                </CardDescription>
              </div>
            </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <PencilIcon className="h-4 w-4" />
                  Edit Profile
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Edit Profile</DialogTitle>
                  <DialogDescription>
                    Update your profile information.
                  </DialogDescription>
                </DialogHeader>
                <form className="space-y-4 py-4" onSubmit={handleSubmit}>
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name || ''}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <textarea
                      id="bio"
                      name="bio"
                      value={formData.bio || ''}
                      onChange={handleChange}
                      rows={4}
                      className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                  </div>
                </form>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    onClick={handleSubmit}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Saving...' : 'Save Changes'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>

        {/* Profile Information Section */}
        <CardContent className="p-6 space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <UserIcon className="h-5 w-5" />
              Profile Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1">
                <h4 className="text-sm font-medium text-muted-foreground">Username</h4>
                <p className="text-sm font-mono">{user?.username || 'No username'}</p>
              </div>
              <div className="space-y-1">
                <h4 className="text-sm font-medium text-muted-foreground">Member Since</h4>
                <p className="text-sm flex items-center gap-1">
                  <CalendarIcon className="h-3 w-3" />
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  }) : 'Unknown'}
                </p>
              </div>
              <div className="space-y-1 md:col-span-2">
                <h4 className="text-sm font-medium text-muted-foreground">Bio</h4>
                <p className="text-sm">{user?.bio || 'No bio provided'}</p>
              </div>
            </div>
          </div>
        </CardContent>

        <Separator />

        {/* Subscription Section */}
        <CardContent className="p-6">
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <CrownIcon className="h-5 w-5" />
              Subscription
            </h3>
            {isLoadingSubscription ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : subscription ? (
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  {getPlanIcon(subscription.planId)}
                  <div>
                    <h4 className="text-lg font-semibold">{getPlanDisplayName(subscription.planId)}</h4>
                    <Badge variant={getStatusBadgeVariant(subscription.status)} className="text-xs">
                      {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Plan</h4>
                    <p className="text-sm font-medium">{getPlanDisplayName(subscription.planId)}</p>
                  </div>
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Status</h4>
                    <p className="text-sm font-medium capitalize">{subscription.status}</p>
                  </div>
                  {subscription.currentPeriodEnd && (
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium text-muted-foreground">
                        {subscription.planId === 'free' ? 'Active Since' : 'Renews On'}
                      </h4>
                      <p className="text-sm font-medium">
                        {new Date(subscription.currentPeriodEnd).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </p>
                    </div>
                  )}
                </div>

                <div className="flex gap-4">
                  <Button asChild variant="outline">
                    <Link href="/marketplace?tab=subscription">
                      <SettingsIcon className="h-4 w-4 mr-2" />
                      Manage Subscription
                    </Link>
                  </Button>
                  {subscription.planId === 'free' && (
                    <Button asChild>
                      <Link href="/marketplace?tab=subscription">
                        <StarIcon className="h-4 w-4 mr-2" />
                        Upgrade Plan
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <CrownIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h4 className="font-semibold mb-2">No Active Subscription</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  Get started with a subscription to unlock all features.
                </p>
                <Button asChild>
                  <Link href="/marketplace?tab=subscription">
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Choose Plan
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>

        <Separator />

        {/* Security Section */}
        <CardContent className="p-6 space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <ShieldIcon className="h-5 w-5" />
              Security
            </h3>

            {/* Email Verification */}
            <div className="space-y-3 mb-6">
              <h4 className="text-base font-medium">Email Verification</h4>
              {user?.emailVerified ? (
                <div className="flex items-center gap-3">
                  <BadgeCheck className="h-5 w-5 text-blue-500" />
                  <p className="text-sm text-muted-foreground">Your email address has been verified</p>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="inline-flex items-center justify-center w-5 h-5 bg-red-500 rounded-full">
                      <AlertCircle className="h-3 w-3 text-white" />
                    </div>
                    <p className="text-sm text-muted-foreground">Please verify your email address</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResendVerification}
                    disabled={isResendingVerification}
                  >
                    {isResendingVerification ? 'Sending...' : 'Send Verification Email'}
                  </Button>
                </div>
              )}
            </div>

            <div className="border-t pt-6">
              {/* Password */}
              <div className="space-y-3 mb-6">
                <h4 className="text-base font-medium">Password</h4>
                <p className="text-sm text-muted-foreground">Last changed: Never</p>
                <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      Change Password
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>Change Password</DialogTitle>
                      <DialogDescription>
                        Enter your current password and choose a new one.
                      </DialogDescription>
                    </DialogHeader>
                    <form className="space-y-4 py-4" onSubmit={handlePasswordSubmit}>
                      <div className="space-y-2">
                        <Label htmlFor="currentPassword">Current Password</Label>
                        <Input
                          id="currentPassword"
                          name="currentPassword"
                          type="password"
                          value={passwordData.currentPassword}
                          onChange={handlePasswordChange}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="newPassword">New Password</Label>
                        <Input
                          id="newPassword"
                          name="newPassword"
                          type="password"
                          value={passwordData.newPassword}
                          onChange={handlePasswordChange}
                          required
                          minLength={6}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">Confirm New Password</Label>
                        <Input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          value={passwordData.confirmPassword}
                          onChange={handlePasswordChange}
                          required
                          minLength={6}
                        />
                      </div>
                    </form>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsPasswordDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        onClick={handlePasswordSubmit}
                        disabled={isChangingPassword}
                      >
                        {isChangingPassword ? 'Changing...' : 'Change Password'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
