"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, Eye, Square, CheckCircle, XCircle, Clock, AlertCircle, Play, Download } from 'lucide-react';
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Breadcrumb<PERSON>ist,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";

interface QueueStats {
  totalQueued: number;
  totalRunning: number;
  totalCompleted: number;
  totalFailed: number;
}

interface ExecutionRecord {
  id: string;
  workflowId: string;
  workflowName: string;
  status: string;
  startTime: string;
  endTime?: string;
  progress: number;
  error?: string;
  userId: string;
  userName: string;
  duration?: number;
}

export default function QueueManagementPage() {
  const [queueStats, setQueueStats] = useState<QueueStats | null>(null);
  const [executions, setExecutions] = useState<ExecutionRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    loadQueueData();
    const interval = setInterval(loadQueueData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadQueueData = async () => {
    try {
      setLoading(true);

      // Load real queue statistics and executions
      const response = await fetch('/api/queue-management');

      if (response.ok) {
        const data = await response.json();
        setQueueStats(data.queueStats);
        setExecutions(data.executions || []);
        console.log('Loaded queue data:', data);
      } else {
        console.error('Failed to load queue data:', response.statusText);
        // Fallback to empty data
        setQueueStats({
          totalQueued: 0,
          totalRunning: 0,
          totalCompleted: 0,
          totalFailed: 0
        });
        setExecutions([]);
      }
    } catch (error) {
      console.error('Error loading queue data:', error);
      // Fallback to empty data
      setQueueStats({
        totalQueued: 0,
        totalRunning: 0,
        totalCompleted: 0,
        totalFailed: 0
      });
      setExecutions([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === 'completed' ? 'default' : 
                   status === 'failed' ? 'destructive' : 
                   status === 'running' ? 'secondary' : 'outline';
    
    return (
      <Badge variant={variant} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status}
      </Badge>
    );
  };

  const formatDuration = (startTime: string, endTime?: string, duration?: number) => {
    if (duration) {
      if (duration < 1000) return `${duration}ms`;
      if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
      return `${(duration / 60000).toFixed(1)}m`;
    }

    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const calculatedDuration = end.getTime() - start.getTime();

    if (calculatedDuration < 1000) return `${calculatedDuration}ms`;
    if (calculatedDuration < 60000) return `${(calculatedDuration / 1000).toFixed(1)}s`;
    return `${(calculatedDuration / 60000).toFixed(1)}m`;
  };

  const handleCancelExecution = async (executionId: string) => {
    try {
      const response = await fetch('/api/queue-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'cancel',
          executionId
        })
      });

      if (response.ok) {
        console.log('Execution cancelled successfully');
        await loadQueueData(); // Refresh data
      } else {
        const error = await response.json();
        console.error('Failed to cancel execution:', error);
      }
    } catch (error) {
      console.error('Error cancelling execution:', error);
    }
  };

  const handleRetryExecution = async (executionId: string) => {
    try {
      const response = await fetch('/api/queue-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'retry',
          executionId
        })
      });

      if (response.ok) {
        console.log('Execution retry queued successfully');
        await loadQueueData(); // Refresh data
      } else {
        const error = await response.json();
        console.error('Failed to retry execution:', error);
      }
    } catch (error) {
      console.error('Error retrying execution:', error);
    }
  };

  const handleViewResults = (execution: ExecutionRecord) => {
    // Open results in a new window or modal
    const resultsWindow = window.open('', '_blank', 'width=800,height=600');
    if (resultsWindow) {
      resultsWindow.document.write(`
        <html>
          <head>
            <title>Execution Results - ${execution.workflowName}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { border-bottom: 1px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }
              .status { padding: 4px 8px; border-radius: 4px; color: white; }
              .completed { background-color: #22c55e; }
              .failed { background-color: #ef4444; }
              pre { background: #f5f5f5; padding: 15px; border-radius: 4px; overflow: auto; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>Execution Results</h1>
              <p><strong>Workflow:</strong> ${execution.workflowName}</p>
              <p><strong>Status:</strong> <span class="status ${execution.status}">${execution.status}</span></p>
              <p><strong>Started:</strong> ${new Date(execution.startTime).toLocaleString()}</p>
              ${execution.endTime ? `<p><strong>Completed:</strong> ${new Date(execution.endTime).toLocaleString()}</p>` : ''}
              <p><strong>Duration:</strong> ${formatDuration(execution.startTime, execution.endTime, execution.duration)}</p>
            </div>
            <h2>Results Data</h2>
            <pre>${JSON.stringify(execution, null, 2)}</pre>
          </body>
        </html>
      `);
    }
  };

  const handleExportResults = (execution: ExecutionRecord) => {
    const exportData = {
      executionId: execution.id,
      workflowId: execution.workflowId,
      workflowName: execution.workflowName,
      status: execution.status,
      startTime: execution.startTime,
      endTime: execution.endTime,
      duration: execution.duration,
      progress: execution.progress,
      error: execution.error,
      exportedAt: new Date().toISOString()
    };

    // Export as JSON
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `execution-${execution.id}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const filteredExecutions = executions.filter(execution => {
    if (statusFilter !== 'all' && execution.status !== statusFilter) return false;
    if (selectedWorkflow && selectedWorkflow !== 'all' && execution.workflowId !== selectedWorkflow) return false;
    return true;
  });

  const uniqueWorkflows = Array.from(new Set(executions.map(e => e.workflowId)));

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/workflow-manager">Workflows</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Scheduler</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <div className="ml-auto px-4">
            <Button onClick={loadQueueData} disabled={loading} className="flex items-center gap-2">
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="mb-4">
            <h1 className="text-3xl font-bold">Workflow Scheduler</h1>
            <p className="text-muted-foreground">
              Schedule and monitor workflow executions with real-time status tracking and result management
            </p>
          </div>
          <div className="space-y-6">

      {/* Queue Statistics */}
      {queueStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                <div>
                  <div className="text-2xl font-bold text-yellow-600">{queueStats.totalQueued}</div>
                  <div className="text-sm text-muted-foreground">Queued</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Play className="h-5 w-5 text-blue-500" />
                <div>
                  <div className="text-2xl font-bold text-blue-600">{queueStats.totalRunning}</div>
                  <div className="text-sm text-muted-foreground">Running</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <div className="text-2xl font-bold text-green-600">{queueStats.totalCompleted}</div>
                  <div className="text-sm text-muted-foreground">Completed</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <XCircle className="h-5 w-5 text-red-500" />
                <div>
                  <div className="text-2xl font-bold text-red-600">{queueStats.totalFailed}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="workflow-filter">Workflow</Label>
              <Select value={selectedWorkflow} onValueChange={setSelectedWorkflow}>
                <SelectTrigger>
                  <SelectValue placeholder="All workflows" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All workflows</SelectItem>
                  {uniqueWorkflows.map(workflowId => (
                    <SelectItem key={workflowId} value={workflowId}>
                      {workflowId}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <Label htmlFor="status-filter">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="queued">Queued</SelectItem>
                  <SelectItem value="running">Running</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Execution History */}
      <Card>
        <CardHeader>
          <CardTitle>Execution Queue ({filteredExecutions.length})</CardTitle>
          <CardDescription>All workflow executions across the system</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading queue data...</span>
            </div>
          ) : filteredExecutions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No executions found matching the current filters.
            </div>
          ) : (
            <div className="w-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40%]">Workflow</TableHead>
                    <TableHead className="w-[20%]">Status</TableHead>
                    <TableHead className="w-[25%]">Time</TableHead>
                    <TableHead className="w-[15%]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
              </Table>
              <ScrollArea className="h-96">
                <Table>
                  <TableBody>
                    {filteredExecutions.map((execution) => (
                      <TableRow key={execution.id}>
                        <TableCell className="w-[40%]">
                          <div className="space-y-1">
                            <div className="font-medium text-sm truncate" title={execution.workflowName}>
                              {execution.workflowName}
                            </div>
                            <div className="text-xs text-muted-foreground truncate" title={execution.userName}>
                              by {execution.userName}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="w-[20%]">
                          <div className="space-y-1">
                            {getStatusBadge(execution.status)}
                            <div className="text-xs text-muted-foreground">
                              {execution.progress}%
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="w-[25%]">
                          <div className="space-y-1">
                            <div className="text-sm">
                              {new Date(execution.startTime).toLocaleDateString()}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {new Date(execution.startTime).toLocaleTimeString()} • {formatDuration(execution.startTime, execution.endTime, execution.duration)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="w-[15%]">
                          <div className="flex gap-1">
                            {(execution.status === 'completed' || execution.status === 'failed') && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewResults(execution)}
                                title="View Results"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            )}
                            {execution.status === 'completed' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleExportResults(execution)}
                                title="Export Results"
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                            )}
                            {execution.status === 'queued' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleCancelExecution(execution.id)}
                                title="Cancel"
                              >
                                <Square className="h-3 w-3" />
                              </Button>
                            )}
                            {execution.status === 'failed' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRetryExecution(execution.id)}
                                title="Retry"
                              >
                                <RefreshCw className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>
          )}
        </CardContent>
      </Card>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
