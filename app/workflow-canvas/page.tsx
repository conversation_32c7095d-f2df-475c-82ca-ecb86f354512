"use client";

import WorkflowProvider from "@/components/workflow/workflow-provider";
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Maximize } from "lucide-react";
import { useState } from "react";

export default function BlankWorkflowCanvasPage() {
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Create a blank workflow
  const blankWorkflow = {
    id: '',
    name: 'New Workflow',
    description: null,
    nodes: [],
    edges: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    userId: ''
  };

  // Fullscreen mode - just the canvas
  if (isFullscreen) {
    return (
      <div className="h-screen w-screen overflow-hidden relative">
        <Button
          variant="outline"
          size="sm"
          className="absolute top-4 right-4 z-50 bg-background/80 backdrop-blur-sm"
          onClick={() => setIsFullscreen(false)}
        >
          Exit Fullscreen
        </Button>
        <WorkflowProvider initialWorkflow={blankWorkflow} />
      </div>
    );
  }

  // Dashboard layout mode
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/workflow-manager">Workflows</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>New Workflow</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <div className="ml-auto px-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFullscreen(true)}
            >
              <Maximize className="h-4 w-4 mr-2" />
              Fullscreen
            </Button>
          </div>
        </header>
        <div className="flex-1 overflow-hidden">
          <WorkflowProvider initialWorkflow={blankWorkflow} />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
