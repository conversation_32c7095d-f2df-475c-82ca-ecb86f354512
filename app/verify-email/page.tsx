'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams?.get('token');

  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationStatus, setVerificationStatus] = useState<'success' | 'error' | null>(null);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (!token) {
      setIsVerifying(false);
      setVerificationStatus('error');
      setErrorMessage('Verification token is missing');
      return;
    }

    const verifyEmail = async () => {
      try {
        const response = await fetch(`/api/auth/verify-email?token=${token}`);

        if (response.ok) {
          setVerificationStatus('success');
        } else {
          const data = await response.json();
          setVerificationStatus('error');
          setErrorMessage(data.error || 'Failed to verify email');
        }
      } catch (error) {
        setVerificationStatus('error');
        setErrorMessage('An error occurred during verification');
      } finally {
        setIsVerifying(false);
      }
    };

    verifyEmail();
  }, [token]);

  return (
    <div className="max-w-md mx-auto py-10">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Email Verification</CardTitle>
          <CardDescription>
            Verify your email address
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isVerifying ? (
            <div className="text-center">
              <p className="mb-4 text-muted-foreground">Verifying your email address...</p>
              <div className="flex justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            </div>
          ) : verificationStatus === 'success' ? (
            <div className="text-center">
              <div className="bg-muted border border-border text-primary px-4 py-3 rounded mb-4 flex items-center gap-2 justify-center">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>Your email has been successfully verified!</span>
              </div>
              <p className="mb-6 text-muted-foreground">You can now log in to your account.</p>
              <Button asChild>
                <Link href="/login">
                  Go to Login
                </Link>
              </Button>
            </div>
          ) : (
            <div className="text-center">
              <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded mb-4 flex items-center gap-2 justify-center">
                <AlertCircle className="h-4 w-4" />
                <span>{errorMessage || 'Failed to verify email'}</span>
              </div>
              <p className="mb-6 text-muted-foreground">
                There was a problem verifying your email. Please try again or contact support.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <Link href="/login">
                    Go to Login
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/">
                    Go to Home
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
