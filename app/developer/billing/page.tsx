"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Wallet,
  CreditCard,
  Download,
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Building2
} from "lucide-react";
import { BankAccountForm } from "@/components/developer/bank-account-form";
import { WithdrawalRequestForm } from "@/components/developer/withdrawal-request-form";

interface BillingData {
  availableBalance: number;
  totalEarnings: number;
  totalWithdrawn: number;
  pendingWithdrawals: number;
  bankAccounts: BankAccount[];
  withdrawalHistory: WithdrawalRequest[];
}

interface BankAccount {
  id: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  isDefault: boolean;
  isVerified: boolean;
  createdAt: string;
}

interface WithdrawalRequest {
  id: string;
  amount: number;
  currency: string;
  status: string;
  requestedAt: string;
  processedAt?: string;
  completedAt?: string;
  bankAccount: {
    bankName: string;
    accountNumber: string;
  };
}

export default function DeveloperBillingPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [billingData, setBillingData] = useState<BillingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showBankForm, setShowBankForm] = useState(false);
  const [showWithdrawalForm, setShowWithdrawalForm] = useState(false);

  // Check admin access
  useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      router.push("/auth/signin");
      return;
    }

    const user = session.user as any;
    if (!user.isAdmin && !user.isSuperAdmin) {
      router.push("/dashboard");
      return;
    }

    loadBillingData();
  }, [session, status, router]);

  const loadBillingData = async () => {
    try {
      const response = await fetch('/api/developer/billing');
      if (response.ok) {
        const data = await response.json();
        setBillingData(data);
      } else {
        // Fallback to default data if API fails
        setBillingData({
          availableBalance: 0,
          totalEarnings: 0,
          totalWithdrawn: 0,
          pendingWithdrawals: 0,
          bankAccounts: [],
          withdrawalHistory: []
        });
      }
    } catch (error) {
      console.error('Failed to load billing data:', error);
      setBillingData({
        availableBalance: 0,
        totalEarnings: 0,
        totalWithdrawn: 0,
        pendingWithdrawals: 0,
        bankAccounts: [],
        withdrawalHistory: []
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'rejected':
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      completed: "default",
      processing: "secondary",
      pending: "outline",
      rejected: "destructive",
      cancelled: "destructive"
    };

    return (
      <Badge variant={variants[status] || "outline"} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (status === "loading" || loading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/developer">Developer</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Billing</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
                      <div className="h-8 bg-muted rounded w-3/4"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!session?.user) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/developer">Developer</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Billing</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Billing & Withdrawals</h1>
              <p className="text-muted-foreground">
                Manage your earnings, bank accounts, and withdrawal requests
              </p>
            </div>
            <div className="flex gap-2">
              <Dialog open={showBankForm} onOpenChange={setShowBankForm}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <Building2 className="h-4 w-4 mr-2" />
                    Add Bank Account
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Indonesian Bank Account</DialogTitle>
                    <DialogDescription>
                      Add your Indonesian bank account details for commission withdrawals.
                    </DialogDescription>
                  </DialogHeader>
                  <BankAccountForm
                    onSuccess={() => {
                      setShowBankForm(false);
                      loadBillingData();
                    }}
                  />
                </DialogContent>
              </Dialog>

              <Dialog open={showWithdrawalForm} onOpenChange={setShowWithdrawalForm}>
                <DialogTrigger asChild>
                  <Button disabled={!billingData?.bankAccounts.length || billingData?.availableBalance <= 0}>
                    <Download className="h-4 w-4 mr-2" />
                    Request Withdrawal
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Request Withdrawal</DialogTitle>
                    <DialogDescription>
                      Request to withdraw your available commission balance.
                    </DialogDescription>
                  </DialogHeader>
                  <WithdrawalRequestForm
                    availableBalance={billingData?.availableBalance || 0}
                    bankAccounts={billingData?.bankAccounts || []}
                    onSuccess={() => {
                      setShowWithdrawalForm(false);
                      loadBillingData();
                    }}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Balance Overview */}
          {billingData && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Available Balance</CardTitle>
                  <Wallet className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(billingData.availableBalance)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Ready for withdrawal
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCurrency(billingData.totalEarnings)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    All-time commission earnings
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Withdrawn</CardTitle>
                  <Download className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCurrency(billingData.totalWithdrawn)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Successfully withdrawn
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Withdrawals</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-yellow-600">
                    {formatCurrency(billingData.pendingWithdrawals)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Being processed
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Bank Accounts */}
          <Card>
            <CardHeader>
              <CardTitle>Bank Accounts</CardTitle>
              <CardDescription>
                Manage your Indonesian bank accounts for commission withdrawals
              </CardDescription>
            </CardHeader>
            <CardContent>
              {billingData?.bankAccounts.length ? (
                <div className="space-y-4">
                  {billingData.bankAccounts.map((account) => (
                    <div key={account.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <Building2 className="h-8 w-8 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{account.bankName}</div>
                          <div className="text-sm text-muted-foreground">
                            {account.accountHolderName} • ****{account.accountNumber.slice(-4)}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {account.isDefault && (
                          <Badge variant="secondary">Default</Badge>
                        )}
                        {account.isVerified ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Verified
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-yellow-600">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Pending Verification
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Bank Accounts</h3>
                  <p className="text-muted-foreground mb-4">
                    Add an Indonesian bank account to start receiving commission withdrawals.
                  </p>
                  <Button onClick={() => setShowBankForm(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Bank Account
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Withdrawal History */}
          <Card>
            <CardHeader>
              <CardTitle>Withdrawal History</CardTitle>
              <CardDescription>
                Track your commission withdrawal requests and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              {billingData?.withdrawalHistory.length ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Bank Account</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Completed</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {billingData.withdrawalHistory.map((withdrawal) => (
                      <TableRow key={withdrawal.id}>
                        <TableCell>
                          {new Date(withdrawal.requestedAt).toLocaleDateString('id-ID')}
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(withdrawal.amount)}
                        </TableCell>
                        <TableCell>
                          {withdrawal.bankAccount.bankName} • ****{withdrawal.bankAccount.accountNumber.slice(-4)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(withdrawal.status)}
                        </TableCell>
                        <TableCell>
                          {withdrawal.completedAt
                            ? new Date(withdrawal.completedAt).toLocaleDateString('id-ID')
                            : '-'
                          }
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <Download className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Withdrawal History</h3>
                  <p className="text-muted-foreground">
                    Your withdrawal requests will appear here once you make them.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}