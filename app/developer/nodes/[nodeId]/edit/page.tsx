"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Save, ArrowLeft, Package, AlertCircle, CheckCircle, X } from "lucide-react";
import { toast } from "sonner";
import { NodeCategory, NodeTier } from "@/lib/marketplace/types";

const nodeEditSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  version: z.string().regex(/^\d+\.\d+\.\d+$/, "Version must be in format x.y.z"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  longDescription: z.string().optional(),
  category: z.nativeEnum(NodeCategory),
  tier: z.nativeEnum(NodeTier),
  price: z.number().min(0).optional(),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
  repositoryUrl: z.string().url().optional().or(z.literal("")),
  documentationUrl: z.string().url().optional().or(z.literal("")),
});

type NodeEditFormData = z.infer<typeof nodeEditSchema>;

interface NodeData {
  id: string;
  name: string;
  version: string;
  description: string;
  longDescription?: string;
  category: string;
  tier: string;
  price?: number;
  tags: string[];
  repositoryUrl?: string;
  documentationUrl?: string;
  approvalStatus: string;
  icon: string;
  screenshots: string[];
}

export default function EditNodePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const nodeId = params.nodeId as string;

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nodeData, setNodeData] = useState<NodeData | null>(null);
  const [tagInput, setTagInput] = useState("");

  const form = useForm<NodeEditFormData>({
    resolver: zodResolver(nodeEditSchema),
    defaultValues: {
      name: "",
      version: "1.0.0",
      description: "",
      longDescription: "",
      category: NodeCategory.UTILITY,
      tier: NodeTier.FREE,
      price: 0,
      tags: [],
      repositoryUrl: "",
      documentationUrl: "",
    },
  });

  const watchedTier = form.watch("tier");
  const watchedTags = form.watch("tags");

  // Check authentication and load node data
  useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      router.push("/auth/signin");
      return;
    }

    loadNodeData();
  }, [session, status, nodeId]);

  const loadNodeData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/developer/nodes/${nodeId}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Node not found');
        }
        throw new Error('Failed to load node data');
      }

      const data = await response.json();
      setNodeData(data);

      // Populate form with existing data
      form.reset({
        name: data.name,
        version: data.version,
        description: data.description,
        longDescription: data.longDescription || "",
        category: data.category as NodeCategory,
        tier: data.tier as NodeTier,
        price: data.price || 0,
        tags: data.tags || [],
        repositoryUrl: data.repositoryUrl || "",
        documentationUrl: data.documentationUrl || "",
      });

    } catch (err) {
      console.error('Error loading node:', err);
      setError(err instanceof Error ? err.message : 'Failed to load node');
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      form.setValue("tags", [...watchedTags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    form.setValue("tags", watchedTags.filter(tag => tag !== tagToRemove));
  };

  const onSubmit = async (data: NodeEditFormData) => {
    try {
      setSaving(true);

      const response = await fetch(`/api/developer/nodes/${nodeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update node');
      }

      toast.success('Node updated successfully!');
      router.push('/developer/nodes/status');

    } catch (err) {
      console.error('Error updating node:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to update node');
    } finally {
      setSaving(false);
    }
  };

  if (status === "loading" || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!session?.user) {
    return null;
  }

  if (error) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <Card className="w-full max-w-md">
              <CardContent className="p-6 text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h2 className="text-lg font-semibold mb-2">Error Loading Node</h2>
                <p className="text-muted-foreground mb-4">{error}</p>
                <Button onClick={() => router.push('/developer/nodes/status')}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Node Status
                </Button>
              </CardContent>
            </Card>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/developer">Developer</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/developer/nodes/status">Node Status</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Edit Node</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Edit Node</h1>
              <p className="text-muted-foreground">Update your node information and settings</p>
            </div>
            <Button 
              variant="outline" 
              onClick={() => router.push('/developer/nodes/status')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Status
            </Button>
          </div>

          {nodeData && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  {nodeData.name}
                  <Badge variant="outline">{nodeData.version}</Badge>
                  <Badge 
                    variant={nodeData.approvalStatus === 'approved' ? 'default' : 'secondary'}
                    className={
                      nodeData.approvalStatus === 'approved' ? 'bg-green-100 text-green-800' :
                      nodeData.approvalStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }
                  >
                    {nodeData.approvalStatus}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  Edit your node details. Changes will require re-approval if the node is currently approved.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {nodeData.approvalStatus === 'approved' && (
                  <Alert className="mb-6">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Note:</strong> This node is currently approved and live in the marketplace. 
                      Making changes will reset the approval status to "pending" and require admin re-approval.
                    </AlertDescription>
                  </Alert>
                )}

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Node Name</FormLabel>
                            <FormControl>
                              <Input placeholder="My Awesome Node" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="version"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Version</FormLabel>
                            <FormControl>
                              <Input placeholder="1.0.0" {...field} />
                            </FormControl>
                            <FormDescription>
                              Increment version number for updates
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Brief description of what your node does..."
                              rows={3}
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="longDescription"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Long Description (Optional)</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Detailed description, usage instructions, examples..."
                              rows={5}
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Category and Tier */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="category"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {Object.values(NodeCategory).map((category) => (
                                  <SelectItem key={category} value={category}>
                                    {category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="tier"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tier</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select tier" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {Object.values(NodeTier).map((tier) => (
                                  <SelectItem key={tier} value={tier}>
                                    {tier.charAt(0).toUpperCase() + tier.slice(1)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {watchedTier !== NodeTier.FREE && (
                        <FormField
                          control={form.control}
                          name="price"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Price (IDR)</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  min="0" 
                                  step="1000"
                                  placeholder="50000"
                                  {...field}
                                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>

                    {/* Tags */}
                    <div className="space-y-2">
                      <FormLabel>Tags</FormLabel>
                      <div className="flex gap-2 mb-2">
                        <Input
                          placeholder="Add a tag..."
                          value={tagInput}
                          onChange={(e) => setTagInput(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addTag();
                            }
                          }}
                        />
                        <Button type="button" onClick={addTag} variant="outline">
                          Add
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {watchedTags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="flex items-center gap-1">
                            {tag}
                            <X 
                              className="h-3 w-3 cursor-pointer" 
                              onClick={() => removeTag(tag)}
                            />
                          </Badge>
                        ))}
                      </div>
                      {watchedTags.length === 0 && (
                        <p className="text-sm text-red-500">At least one tag is required</p>
                      )}
                    </div>

                    {/* URLs */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="repositoryUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Repository URL (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="https://github.com/user/repo" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="documentationUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Documentation URL (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="https://docs.example.com" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Submit Button */}
                    <div className="flex gap-4">
                      <Button type="submit" disabled={saving}>
                        {saving ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4 mr-2" />
                        )}
                        {saving ? 'Saving...' : 'Save Changes'}
                      </Button>
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => router.push('/developer/nodes/status')}
                        disabled={saving}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
