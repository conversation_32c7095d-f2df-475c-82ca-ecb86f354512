"use client";

import { useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Loader2 } from "lucide-react";

export default function WorkflowRedirectPage() {
  const router = useRouter();
  const params = useParams();
  const workflowId = params?.id as string;

  useEffect(() => {
    // Redirect to the new workflow canvas page with the same ID
    router.push(`/workflow-canvas/${workflowId}`);
  }, [router, workflowId]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">Redirecting to Workflow Canvas...</p>
    </div>
  );
}
