"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";

export default function BlankWorkflowRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the new blank workflow canvas page
    router.push("/workflow-canvas");
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">Redirecting to Workflow Canvas...</p>
    </div>
  );
}
