import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import ConditionalNavbar from "@/components/conditional-navbar";
import NextAuthSessionProvider from "@/components/session-provider";
import { SettingsProvider } from "@/lib/settings/settings-context";
import { PageTransition } from "@/components/page-transition";

// Optimize font loading - only load when needed
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: 'swap',
  preload: false, // Don't preload to improve initial load time
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap',
  preload: false,
});

export const metadata: Metadata = {
  title: "WorkflowAI - Automate with Intelligence",
  description: "Build powerful automation workflows with our visual editor. Connect your favorite tools and scale your business operations effortlessly.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NextAuthSessionProvider>
          <SettingsProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <div className="min-h-screen flex flex-col">
                <PageTransition />
                <ConditionalNavbar />
                <main className="flex-1 w-full">
                  {children}
                </main>
              </div>
            </ThemeProvider>
          </SettingsProvider>
        </NextAuthSessionProvider>
      </body>
    </html>
  );
}
