"use client";

import { AppSidebar } from "@/components/app-sidebar"
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useSession } from "next-auth/react"
import Link from "next/link"
import {
  Workflow,
  Plus,
  Activity,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Play
} from "lucide-react"

export default function DashboardPage() {
  const { data: session } = useSession();
  const user = session?.user;

  const stats = [
    {
      title: "Total Workflows",
      value: "12",
      description: "+2 from last month",
      icon: Workflow,
      trend: "up"
    },
    {
      title: "Active Executions",
      value: "1,234",
      description: "+12% from last week",
      icon: Activity,
      trend: "up"
    },
    {
      title: "Success Rate",
      value: "98.5%",
      description: "+0.5% from last month",
      icon: CheckCircle,
      trend: "up"
    },
    {
      title: "Avg. Runtime",
      value: "2.3s",
      description: "-0.2s from last week",
      icon: Clock,
      trend: "down"
    }
  ];

  const recentWorkflows = [
    {
      name: "Data Sync Pipeline",
      status: "running",
      lastRun: "2 minutes ago",
      executions: 45
    },
    {
      name: "Email Automation",
      status: "success",
      lastRun: "1 hour ago",
      executions: 23
    },
    {
      name: "Report Generator",
      status: "failed",
      lastRun: "3 hours ago",
      executions: 12
    },
    {
      name: "Social Media Bot",
      status: "success",
      lastRun: "6 hours ago",
      executions: 89
    }
  ];

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbPage>Dashboard</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Welcome Section */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Welcome back, {user?.name || 'User'}!</h1>
              <p className="text-muted-foreground">Here's what's happening with your workflows today.</p>
            </div>
            <Button asChild>
              <Link href="/workflow-canvas">
                <Plus className="mr-2 h-4 w-4" />
                Create Workflow
              </Link>
            </Button>
          </div>

          {/* Stats Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat) => (
              <Card key={stat.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  <stat.icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">{stat.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Recent Activity */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Recent Workflows</CardTitle>
                <CardDescription>Your most recently active workflows</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentWorkflows.map((workflow, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Workflow className="h-4 w-4" />
                          <div>
                            <p className="text-sm font-medium">{workflow.name}</p>
                            <p className="text-xs text-muted-foreground">{workflow.lastRun}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={
                            workflow.status === 'running' ? 'default' :
                            workflow.status === 'success' ? 'secondary' : 'destructive'
                          }
                        >
                          {workflow.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">{workflow.executions} runs</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Get started with common tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full justify-start" variant="outline" asChild>
                  <Link href="/workflow-canvas">
                    <Plus className="mr-2 h-4 w-4" />
                    Create New Workflow
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="outline" asChild>
                  <Link href="/marketplace">
                    <Play className="mr-2 h-4 w-4" />
                    Browse Templates
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="outline" asChild>
                  <Link href="/marketplace?tab=subscription">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Upgrade Plan
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="outline" asChild>
                  <Link href="/docs">
                    <Users className="mr-2 h-4 w-4" />
                    View Documentation
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
