"use client";


import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Loader2,
  Plus,
  Edit,
  Trash2,
  ExternalLink,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  Pause,
  Activity,
  Zap
} from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";

import { format } from "date-fns";
import { AppSidebar } from "@/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"

interface Workflow {
  id: string;
  name: string;
  description: string | null;
  nodes: string; // JSON string of nodes
  edges: string; // JSON string of edges
  createdAt: string;
  updatedAt: string;
  userId: string;
  // Execution information
  lastExecution?: {
    id: string;
    status: 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';
    startTime: string;
    endTime?: string;
    duration?: number;
    error?: string;
  };
  executionCount?: number;
  nodeCount?: number;
}

export default function WorkflowManagerPage() {
  const router = useRouter();
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [isLoadingWorkflows, setIsLoadingWorkflows] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [workflowToDelete, setWorkflowToDelete] = useState<Workflow | null>(null);
  const [workflowToEdit, setWorkflowToEdit] = useState<Workflow | null>(null);
  const [newWorkflow, setNewWorkflow] = useState({
    name: "",
    description: "",
  });
  const [editWorkflow, setEditWorkflow] = useState({
    name: "",
    description: "",
  });

  // Fetch workflows when component mounts
  useEffect(() => {
    fetchWorkflows();
  }, []);

  const fetchWorkflows = async () => {
    setIsLoadingWorkflows(true);
    setError(null);

    try {
      const response = await fetch("/api/workflow");

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to fetch workflows");
      }

      const workflows = await response.json();

      // Enhance workflows with execution information
      const enhancedWorkflows = await Promise.all(
        workflows.map(async (workflow: Workflow) => {
          try {
            // Parse nodes to get count
            const nodes = JSON.parse(workflow.nodes || '[]');
            const nodeCount = nodes.length;

            // Fetch latest execution for this workflow
            const execResponse = await fetch(`/api/workflows/${workflow.id}/execute`);
            let lastExecution = null;
            let executionCount = 0;

            if (execResponse.ok) {
              const execData = await execResponse.json();
              if (execData.executions && execData.executions.length > 0) {
                const latest = execData.executions[0];
                lastExecution = {
                  id: latest.id,
                  status: latest.status,
                  startTime: latest.startTime,
                  endTime: latest.endTime,
                  duration: latest.duration,
                  error: latest.error
                };
                executionCount = execData.executions.length;
              }
            }

            return {
              ...workflow,
              nodeCount,
              lastExecution,
              executionCount
            };
          } catch (error) {
            console.error(`Error fetching execution data for workflow ${workflow.id}:`, error);
            return {
              ...workflow,
              nodeCount: 0,
              lastExecution: null,
              executionCount: 0
            };
          }
        })
      );

      setWorkflows(enhancedWorkflows);
    } catch (error) {
      console.error("Error fetching workflows:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch workflows");
    } finally {
      setIsLoadingWorkflows(false);
    }
  };

  const handleCreateWorkflow = async () => {
    if (!newWorkflow.name.trim()) {
      return;
    }

    setIsCreating(true);
    setError(null);

    try {
      // Create a clean version of the workflow data
      // This ensures we're not sending undefined or function properties
      const workflowData = {
        name: newWorkflow.name,
        description: newWorkflow.description,
        // Use empty arrays for nodes and edges
        nodes: JSON.stringify([]),
        edges: JSON.stringify([])
      };

      const response = await fetch("/api/workflow", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(workflowData),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to create workflow");
      }

      const newWorkflowData = await response.json();

      // Reset form
      setNewWorkflow({ name: "", description: "" });
      setIsCreateDialogOpen(false);

      // Log the workflow ID for debugging
      console.log("Created workflow with ID:", newWorkflowData.id);

      // Use router.push for navigation
      router.push(`/workflow-canvas/${newWorkflowData.id}`);
    } catch (error) {
      console.error("Error creating workflow:", error);
      setError(error instanceof Error ? error.message : "Failed to create workflow");
    } finally {
      setIsCreating(false);
    }
  };

  const handleEditWorkflow = async () => {
    if (!workflowToEdit) return;
    if (!editWorkflow.name.trim()) return;

    setIsUpdating(true);
    setError(null);

    try {
      // Create a clean version of the workflow data
      // This ensures we're not sending undefined or function properties
      const workflowData = {
        name: editWorkflow.name,
        description: editWorkflow.description,
        // Use empty arrays if nodes/edges are missing
        nodes: workflowToEdit.nodes || JSON.stringify([]),
        edges: workflowToEdit.edges || JSON.stringify([])
      };

      const response = await fetch(`/api/workflow/${workflowToEdit.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(workflowData),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to update workflow");
      }

      const updatedWorkflow = await response.json();

      // Update the workflow in the list
      setWorkflows((prev) =>
        prev.map((w) => (w.id === workflowToEdit.id ? updatedWorkflow : w))
      );

      setWorkflowToEdit(null);
      setIsEditDialogOpen(false);

      // Refresh the list
      fetchWorkflows();
    } catch (error) {
      console.error("Error updating workflow:", error);
      setError(error instanceof Error ? error.message : "Failed to update workflow");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteWorkflow = async () => {
    if (!workflowToDelete) return;

    setIsDeleting(true);
    setError(null);

    try {
      const response = await fetch(`/api/workflow/${workflowToDelete.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to delete workflow");
      }

      // Remove the deleted workflow from the list
      setWorkflows((prev) => prev.filter((w) => w.id !== workflowToDelete.id));
      setWorkflowToDelete(null);
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting workflow:", error);
      setError(error instanceof Error ? error.message : "Failed to delete workflow");
    } finally {
      setIsDeleting(false);
    }
  };

  // Helper functions for status display
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: { variant: "default" as const, icon: <Activity className="h-3 w-3" />, color: "bg-blue-500" },
      completed: { variant: "default" as const, icon: <CheckCircle className="h-3 w-3" />, color: "bg-green-500" },
      failed: { variant: "destructive" as const, icon: <XCircle className="h-3 w-3" />, color: "bg-red-500" },
      cancelled: { variant: "secondary" as const, icon: <XCircle className="h-3 w-3" />, color: "bg-gray-500" },
      paused: { variant: "outline" as const, icon: <Pause className="h-3 w-3" />, color: "bg-yellow-500" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.cancelled;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        {config.icon}
        {status}
      </Badge>
    );
  };

  const formatDuration = (duration: number | null | undefined) => {
    if (!duration) return "—";
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getExecutionSummary = (workflow: Workflow) => {
    if (!workflow.lastExecution) {
      return (
        <div className="flex items-center gap-2 text-muted-foreground">
          <Clock className="h-4 w-4" />
          <span>Never executed</span>
        </div>
      );
    }

    const { lastExecution } = workflow;
    return (
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          {getStatusBadge(lastExecution.status)}
          <span className="text-sm text-muted-foreground">
            {formatDuration(lastExecution.duration)}
          </span>
        </div>
        <div className="text-xs text-muted-foreground">
          {format(new Date(lastExecution.startTime), "MMM d, HH:mm")}
          {workflow.executionCount && workflow.executionCount > 1 && (
            <span className="ml-2">({workflow.executionCount} total)</span>
          )}
        </div>
        {lastExecution.error && (
          <div className="text-xs text-red-600 truncate max-w-[200px]" title={lastExecution.error}>
            Error: {lastExecution.error}
          </div>
        )}
      </div>
    );
  };



  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbPage>Workflows</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="mb-4">
            <h1 className="text-3xl font-bold">Workflow Manager</h1>
            <p className="text-muted-foreground">Create, manage, and monitor your workflow automations with real-time execution tracking</p>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Your Workflows</CardTitle>
                  <CardDescription>
                    Manage your automation workflows with execution monitoring and quick controls
                  </CardDescription>
                </div>
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" /> New Workflow
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {error && (
                <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded flex items-center gap-2">
                  <Trash2 className="h-4 w-4" />
                  <span>{error}</span>
                </div>
              )}

          {isLoadingWorkflows ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : workflows.length === 0 ? (
            <div className="text-center py-10 text-muted-foreground">
              <p>You don't have any workflows yet.</p>
              <p className="mt-2">
                Click the "New Workflow" button to create your first workflow.
              </p>
            </div>
          ) : (
            <ScrollArea className="w-full h-[400px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Execution Status</TableHead>
                  <TableHead>Nodes</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>

                <TableBody>
                  {workflows.map((workflow) => (
                    <TableRow key={workflow.id}>
                      <TableCell className="font-medium">
                        <div className="space-y-1">
                          <div>{workflow.name}</div>
                          {workflow.description && (
                            <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                              {workflow.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getExecutionSummary(workflow)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Zap className="h-3 w-3" />
                            {workflow.nodeCount || 0}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        {format(new Date(workflow.updatedAt), "MMM d, yyyy HH:mm")}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          {workflow.nodeCount && workflow.nodeCount > 0 && (
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={async () => {
                                try {
                                  const response = await fetch(`/api/workflows/${workflow.id}/execute`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({})
                                  });
                                  if (response.ok) {
                                    // Refresh workflows to show updated execution status
                                    fetchWorkflows();
                                  }
                                } catch (error) {
                                  console.error('Failed to execute workflow:', error);
                                }
                              }}
                              disabled={workflow.lastExecution?.status === 'running'}
                              title="Quick Execute"
                            >
                              <Play className="h-4 w-4" />
                              <span className="sr-only">Execute</span>
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="icon"
                            asChild
                          >
                            <Link href={`/workflow-canvas/${workflow.id}`}>
                              <ExternalLink className="h-4 w-4" />
                              <span className="sr-only">Open</span>
                            </Link>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => {
                              setWorkflowToEdit(workflow);
                              setEditWorkflow({
                                name: workflow.name,
                                description: workflow.description || "",
                              });
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => {
                              setWorkflowToDelete(workflow);
                              setIsDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>

            </Table>
             </ScrollArea>
          )}
            </CardContent>
          </Card>

          {/* Create Workflow Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Workflow</DialogTitle>
            <DialogDescription>
              Enter the details for your new workflow.
            </DialogDescription>
          </DialogHeader>
          <form className="space-y-4 py-4" onSubmit={(e) => { e.preventDefault(); handleCreateWorkflow(); }}>
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={newWorkflow.name}
                onChange={(e) =>
                  setNewWorkflow({ ...newWorkflow, name: e.target.value })
                }
                placeholder="My Workflow"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                value={newWorkflow.description}
                onChange={(e) =>
                  setNewWorkflow({ ...newWorkflow, description: e.target.value })
                }
                placeholder="Describe your workflow"
                rows={4}
                className="min-h-[100px]"
              />
            </div>
          </form>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateWorkflow} disabled={isCreating}>
              {isCreating ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Plus className="mr-2 h-4 w-4" />
              )}
              {isCreating ? "Creating..." : "Create Workflow"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Workflow Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Workflow</DialogTitle>
            <DialogDescription>
              Update the details of your workflow.
            </DialogDescription>
          </DialogHeader>
          <form className="space-y-4 py-4" onSubmit={(e) => { e.preventDefault(); handleEditWorkflow(); }}>
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                value={editWorkflow.name}
                onChange={(e) =>
                  setEditWorkflow({ ...editWorkflow, name: e.target.value })
                }
                placeholder="My Workflow"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description (optional)</Label>
              <Textarea
                id="edit-description"
                value={editWorkflow.description}
                onChange={(e) =>
                  setEditWorkflow({ ...editWorkflow, description: e.target.value })
                }
                placeholder="Describe your workflow"
                rows={4}
                className="min-h-[100px]"
              />
            </div>
          </form>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditWorkflow} disabled={isUpdating}>
              {isUpdating ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Edit className="mr-2 h-4 w-4" />
              )}
              {isUpdating ? "Updating..." : "Update Workflow"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Workflow Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Workflow</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this workflow? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-2">
            <div className="space-y-1">
              <h4 className="text-sm font-medium">Workflow Name</h4>
              <p className="text-sm font-medium">{workflowToDelete?.name}</p>
            </div>
            {workflowToDelete?.description && (
              <div className="space-y-1">
                <h4 className="text-sm font-medium">Description</h4>
                <p className="text-sm text-muted-foreground">
                  {workflowToDelete?.description}
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteWorkflow} disabled={isDeleting}>
              {isDeleting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
