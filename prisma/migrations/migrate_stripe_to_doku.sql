-- Migration: Replace Stripe fields with Doku fields
-- This migration updates the database schema to use Doku payment gateway instead of Stripe

-- Update User table: Replace stripeCustomerId with dokuCustomerId
ALTER TABLE "User" RENAME COLUMN "stripeCustomerId" TO "dokuCustomerId";

-- Update Subscription table: Replace Stripe fields with Doku fields
ALTER TABLE "Subscription" RENAME COLUMN "stripeSubscriptionId" TO "dokuSubscriptionId";
ALTER TABLE "Subscription" RENAME COLUMN "stripeCustomerId" TO "dokuCustomerId";

-- Update NodeSubscription table: Replace Stripe fields with Doku fields
ALTER TABLE "NodeSubscription" RENAME COLUMN "stripeSubscriptionId" TO "dokuSubscriptionId";

-- Add new Payment table for Doku transactions
CREATE TABLE IF NOT EXISTS "Payment" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "amount" REAL NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'IDR',
    "status" TEXT NOT NULL,
    "paymentMethod" TEXT,
    "invoiceNumber" TEXT NOT NULL,
    "dokuTokenId" TEXT,
    "dokuSessionId" TEXT,
    "dokuTransactionId" TEXT,
    "dokuReferenceNumber" TEXT,
    "metadata" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "Payment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- Add new Revenue table for tracking node sales revenue
CREATE TABLE IF NOT EXISTS "Revenue" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "nodeId" TEXT,
    "amount" REAL NOT NULL,
    "commission" REAL NOT NULL,
    "transactionId" TEXT,
    "type" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "Revenue_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "Revenue_nodeId_fkey" FOREIGN KEY ("nodeId") REFERENCES "NodePlugin" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- Create indexes for new tables
CREATE INDEX "Payment_userId_idx" ON "Payment"("userId");
CREATE INDEX "Payment_status_idx" ON "Payment"("status");
CREATE INDEX "Payment_invoiceNumber_idx" ON "Payment"("invoiceNumber");
CREATE INDEX "Payment_dokuTokenId_idx" ON "Payment"("dokuTokenId");
CREATE INDEX "Payment_dokuTransactionId_idx" ON "Payment"("dokuTransactionId");

CREATE INDEX "Revenue_userId_idx" ON "Revenue"("userId");
CREATE INDEX "Revenue_nodeId_idx" ON "Revenue"("nodeId");
CREATE INDEX "Revenue_type_idx" ON "Revenue"("type");

-- Update unique constraints
DROP INDEX IF EXISTS "User_stripeCustomerId_key";
CREATE UNIQUE INDEX "User_dokuCustomerId_key" ON "User"("dokuCustomerId");

DROP INDEX IF EXISTS "Subscription_stripeSubscriptionId_key";
CREATE UNIQUE INDEX "Subscription_dokuSubscriptionId_key" ON "Subscription"("dokuSubscriptionId");

-- Update other indexes
DROP INDEX IF EXISTS "Subscription_stripeSubscriptionId_idx";
CREATE INDEX "Subscription_dokuSubscriptionId_idx" ON "Subscription"("dokuSubscriptionId");

-- Update app settings to use Doku instead of Stripe
UPDATE "AppSettings" SET "value" = 'doku' WHERE "key" = 'provider' AND "value" = 'stripe';
UPDATE "AppSettings" SET "key" = 'dokuEnabled', "description" = 'Enable Doku payments' WHERE "key" = 'stripeEnabled';
UPDATE "AppSettings" SET "value" = 'idr' WHERE "key" = 'currency' AND "value" = 'usd';

-- Add new Doku-specific settings
INSERT OR IGNORE INTO "AppSettings" ("id", "category", "key", "value", "type", "description", "isSystem") VALUES
('doku_client_id', 'payments', 'dokuClientId', '', 'string', 'Doku Client ID', true),
('doku_secret_key', 'payments', 'dokuSecretKey', '', 'string', 'Doku Secret Key', true),
('doku_environment', 'payments', 'dokuEnvironment', 'sandbox', 'string', 'Doku Environment (sandbox/production)', true),
('doku_notification_url', 'payments', 'dokuNotificationUrl', '', 'string', 'Doku Notification URL', true);

-- Remove old Stripe-specific settings (optional - comment out if you want to keep them)
-- DELETE FROM "AppSettings" WHERE "key" IN ('stripePublishableKey', 'stripeSecretKey', 'stripeWebhookSecret');
