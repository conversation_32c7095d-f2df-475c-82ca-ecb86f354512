-- Add Application Settings table
CREATE TABLE IF NOT EXISTS "AppSettings" (
    "id" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'string',
    "description" TEXT,
    "isSystem" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,

    CONSTRAINT "AppSettings_pkey" PRIMARY KEY ("id")
);

-- Create unique index for category + key combination
CREATE UNIQUE INDEX "AppSettings_category_key_key" ON "AppSettings"("category", "key");

-- Create index for faster category queries
CREATE INDEX "AppSettings_category_idx" ON "AppSettings"("category");

-- Create index for system settings
CREATE INDEX "AppSettings_isSystem_idx" ON "AppSettings"("isSystem");

-- Add foreign key constraint for user references
ALTER TABLE "AppSettings" ADD CONSTRAINT "AppSettings_createdBy_fkey" 
    FOREIGN KEY ("createdBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "AppSettings" ADD CONSTRAINT "AppSettings_updatedBy_fkey" 
    FOREIGN KEY ("updatedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Insert default settings
INSERT OR IGNORE INTO "AppSettings" ("id", "category", "key", "value", "type", "description", "isSystem") VALUES
-- Payment Settings
('pay_enabled', 'payments', 'enabled', 'false', 'boolean', 'Enable payment processing', true),
('pay_provider', 'payments', 'provider', 'disabled', 'string', 'Payment provider (stripe, paypal, disabled)', true),
('pay_stripe_enabled', 'payments', 'stripeEnabled', 'false', 'boolean', 'Enable Stripe payments', true),
('pay_test_mode', 'payments', 'testMode', 'true', 'boolean', 'Use test mode for payments', true),
('pay_currency', 'payments', 'currency', 'usd', 'string', 'Default currency', true),
('pay_commission', 'payments', 'commissionRate', '0.25', 'number', 'Commission rate (0-1)', true),

-- Billing Settings
('bill_enabled', 'billing', 'enabled', 'false', 'boolean', 'Enable billing system', true),
('bill_auto', 'billing', 'automaticBilling', 'false', 'boolean', 'Enable automatic billing', true),
('bill_cycle', 'billing', 'billingCycle', 'monthly', 'string', 'Billing cycle (monthly, yearly)', true),
('bill_grace', 'billing', 'gracePeriodDays', '7', 'number', 'Grace period in days', true),

-- Subscription Settings
('sub_enabled', 'subscriptions', 'enabled', 'false', 'boolean', 'Enable subscription system', true),
('sub_free_plan', 'subscriptions', 'allowFreePlan', 'true', 'boolean', 'Allow free plan', true),
('sub_upgrades', 'subscriptions', 'allowUpgrades', 'false', 'boolean', 'Allow plan upgrades', true),
('sub_trial_days', 'subscriptions', 'trialPeriodDays', '14', 'number', 'Trial period in days', true),

-- Marketplace Settings
('market_enabled', 'marketplace', 'enabled', 'true', 'boolean', 'Enable marketplace', true),
('market_paid_nodes', 'marketplace', 'paidNodesEnabled', 'false', 'boolean', 'Enable paid nodes', true),
('market_free_nodes', 'marketplace', 'freeNodesEnabled', 'true', 'boolean', 'Enable free nodes', true),
('market_approval', 'marketplace', 'nodeApprovalRequired', 'true', 'boolean', 'Require node approval', true),
('market_uploads', 'marketplace', 'allowNodeUploads', 'true', 'boolean', 'Allow node uploads', true),
('market_max_size', 'marketplace', 'maxNodeSize', '10', 'number', 'Max node size in MB', true),
('market_reviews', 'marketplace', 'reviewSystemEnabled', 'true', 'boolean', 'Enable review system', true),

-- Developer Settings
('dev_enabled', 'developer', 'enabled', 'true', 'boolean', 'Enable developer features', true),
('dev_publishing', 'developer', 'nodePublishingEnabled', 'true', 'boolean', 'Enable node publishing', true),
('dev_analytics', 'developer', 'analyticsEnabled', 'true', 'boolean', 'Enable developer analytics', true),
('dev_revenue_share', 'developer', 'revenueShareEnabled', 'false', 'boolean', 'Enable revenue sharing', true),
('dev_max_nodes', 'developer', 'maxNodesPerDeveloper', '50', 'number', 'Max nodes per developer', true),

-- Workflow Settings
('wf_enabled', 'workflows', 'enabled', 'true', 'boolean', 'Enable workflow system', true),
('wf_max_workflows', 'workflows', 'maxWorkflowsPerUser', '100', 'number', 'Max workflows per user', true),
('wf_max_nodes', 'workflows', 'maxNodesPerWorkflow', '50', 'number', 'Max nodes per workflow', true),
('wf_timeout', 'workflows', 'executionTimeoutMinutes', '30', 'number', 'Execution timeout in minutes', true),
('wf_scheduling', 'workflows', 'schedulingEnabled', 'true', 'boolean', 'Enable workflow scheduling', true),

-- Email Settings
('email_enabled', 'email', 'enabled', 'false', 'boolean', 'Enable email system', true),
('email_provider', 'email', 'provider', 'disabled', 'string', 'Email provider', true),
('email_verification', 'email', 'verificationEnabled', 'false', 'boolean', 'Enable email verification', true),
('email_notifications', 'email', 'notificationsEnabled', 'false', 'boolean', 'Enable email notifications', true),

-- Security Settings
('sec_2fa', 'security', 'twoFactorEnabled', 'false', 'boolean', 'Enable two-factor authentication', true),
('sec_session_timeout', 'security', 'sessionTimeoutMinutes', '480', 'number', 'Session timeout in minutes', true),
('sec_max_attempts', 'security', 'maxLoginAttempts', '5', 'number', 'Max login attempts', true),
('sec_lockout', 'security', 'lockoutDurationMinutes', '15', 'number', 'Lockout duration in minutes', true),

-- Feature Flags
('feat_beta', 'features', 'betaFeaturesEnabled', 'false', 'boolean', 'Enable beta features', true),
('feat_experimental', 'features', 'experimentalFeaturesEnabled', 'false', 'boolean', 'Enable experimental features', true),
('feat_debug', 'features', 'debugModeEnabled', 'false', 'boolean', 'Enable debug mode', true),
('feat_monitoring', 'features', 'performanceMonitoringEnabled', 'true', 'boolean', 'Enable performance monitoring', true);
