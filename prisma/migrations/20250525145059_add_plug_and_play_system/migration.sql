/*
  Warnings:

  - A unique constraint covering the columns `[stripeCustomerId]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "User" ADD COLUMN "stripeCustomerId" TEXT;

-- CreateTable
CREATE TABLE "NodePlugin" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "longDescription" TEXT,
    "authorId" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "tier" TEXT NOT NULL,
    "price" REAL,
    "subscriptionType" TEXT,
    "tags" TEXT NOT NULL,
    "dependencies" TEXT NOT NULL,
    "permissions" TEXT NOT NULL,
    "icon" TEXT NOT NULL,
    "screenshots" TEXT NOT NULL,
    "downloadUrl" TEXT NOT NULL,
    "repositoryUrl" TEXT,
    "documentationUrl" TEXT,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "rating" REAL NOT NULL DEFAULT 0,
    "reviewCount" INTEGER NOT NULL DEFAULT 0,
    "downloads" INTEGER NOT NULL DEFAULT 0,
    "weeklyDownloads" INTEGER NOT NULL DEFAULT 0,
    "lastUpdated" DATETIME NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "compatibility" TEXT NOT NULL,
    "changelog" TEXT,
    CONSTRAINT "NodePlugin_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "NodeReview" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "nodeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "rating" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "comment" TEXT NOT NULL,
    "helpful" INTEGER NOT NULL DEFAULT 0,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "NodeReview_nodeId_fkey" FOREIGN KEY ("nodeId") REFERENCES "NodePlugin" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "NodeReview_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "NodePurchase" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "nodeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "amount" REAL NOT NULL,
    "commission" REAL NOT NULL DEFAULT 0,
    "netAmount" REAL NOT NULL DEFAULT 0,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "paymentIntentId" TEXT,
    "status" TEXT NOT NULL,
    "completedAt" DATETIME,
    "refundedAt" DATETIME,
    "refundAmount" REAL,
    "refundReason" TEXT,
    "metadata" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "NodePurchase_nodeId_fkey" FOREIGN KEY ("nodeId") REFERENCES "NodePlugin" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "NodePurchase_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "NodeSubscription" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "planId" TEXT NOT NULL,
    "nodeIds" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "currentPeriodStart" DATETIME NOT NULL,
    "currentPeriodEnd" DATETIME NOT NULL,
    "cancelAtPeriodEnd" BOOLEAN NOT NULL DEFAULT false,
    "stripeSubscriptionId" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "NodeSubscription_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "NodeUsage" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "nodeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "workflowId" TEXT,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "duration" INTEGER,
    "success" BOOLEAN NOT NULL DEFAULT true,
    "error" TEXT,
    CONSTRAINT "NodeUsage_nodeId_fkey" FOREIGN KEY ("nodeId") REFERENCES "NodePlugin" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "NodeUsage_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "UserLibrary" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "nodeId" TEXT NOT NULL,
    "installedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "version" TEXT,
    "settings" TEXT,
    CONSTRAINT "UserLibrary_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "UserLibrary_nodeId_fkey" FOREIGN KEY ("nodeId") REFERENCES "NodePlugin" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Subscription" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "stripeSubscriptionId" TEXT NOT NULL,
    "stripeCustomerId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "planId" TEXT NOT NULL,
    "currentPeriodStart" DATETIME NOT NULL,
    "currentPeriodEnd" DATETIME NOT NULL,
    "cancelAtPeriodEnd" BOOLEAN NOT NULL DEFAULT false,
    "canceledAt" DATETIME,
    "trialStart" DATETIME,
    "trialEnd" DATETIME,
    "metadata" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Subscription_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "InstalledNode" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "nodeId" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "installPath" TEXT,
    "config" JSONB,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "installedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "lastUsed" DATETIME,
    CONSTRAINT "InstalledNode_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "InstalledNode_nodeId_fkey" FOREIGN KEY ("nodeId") REFERENCES "NodePlugin" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "NodeExecutionLog" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "workflowId" TEXT,
    "nodeId" TEXT NOT NULL,
    "nodeType" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "input" JSONB,
    "output" JSONB,
    "error" TEXT,
    "duration" INTEGER,
    "startedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" DATETIME,
    CONSTRAINT "NodeExecutionLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "NodeExecutionLog_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "Workflow" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "NodeCode" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "nodeId" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "dependencies" JSONB,
    "permissions" JSONB,
    "checksum" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "NodeCode_nodeId_fkey" FOREIGN KEY ("nodeId") REFERENCES "NodePlugin" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "NodePlugin_authorId_idx" ON "NodePlugin"("authorId");

-- CreateIndex
CREATE INDEX "NodePlugin_category_idx" ON "NodePlugin"("category");

-- CreateIndex
CREATE INDEX "NodePlugin_tier_idx" ON "NodePlugin"("tier");

-- CreateIndex
CREATE INDEX "NodePlugin_featured_idx" ON "NodePlugin"("featured");

-- CreateIndex
CREATE INDEX "NodePlugin_verified_idx" ON "NodePlugin"("verified");

-- CreateIndex
CREATE INDEX "NodeReview_nodeId_idx" ON "NodeReview"("nodeId");

-- CreateIndex
CREATE INDEX "NodeReview_userId_idx" ON "NodeReview"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "NodeReview_nodeId_userId_key" ON "NodeReview"("nodeId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "NodePurchase_paymentIntentId_key" ON "NodePurchase"("paymentIntentId");

-- CreateIndex
CREATE INDEX "NodePurchase_nodeId_idx" ON "NodePurchase"("nodeId");

-- CreateIndex
CREATE INDEX "NodePurchase_userId_idx" ON "NodePurchase"("userId");

-- CreateIndex
CREATE INDEX "NodePurchase_status_idx" ON "NodePurchase"("status");

-- CreateIndex
CREATE INDEX "NodePurchase_paymentIntentId_idx" ON "NodePurchase"("paymentIntentId");

-- CreateIndex
CREATE INDEX "NodeSubscription_userId_idx" ON "NodeSubscription"("userId");

-- CreateIndex
CREATE INDEX "NodeSubscription_status_idx" ON "NodeSubscription"("status");

-- CreateIndex
CREATE INDEX "NodeUsage_nodeId_idx" ON "NodeUsage"("nodeId");

-- CreateIndex
CREATE INDEX "NodeUsage_userId_idx" ON "NodeUsage"("userId");

-- CreateIndex
CREATE INDEX "NodeUsage_timestamp_idx" ON "NodeUsage"("timestamp");

-- CreateIndex
CREATE INDEX "UserLibrary_userId_idx" ON "UserLibrary"("userId");

-- CreateIndex
CREATE INDEX "UserLibrary_nodeId_idx" ON "UserLibrary"("nodeId");

-- CreateIndex
CREATE UNIQUE INDEX "UserLibrary_userId_nodeId_key" ON "UserLibrary"("userId", "nodeId");

-- CreateIndex
CREATE UNIQUE INDEX "Subscription_stripeSubscriptionId_key" ON "Subscription"("stripeSubscriptionId");

-- CreateIndex
CREATE INDEX "Subscription_userId_idx" ON "Subscription"("userId");

-- CreateIndex
CREATE INDEX "Subscription_status_idx" ON "Subscription"("status");

-- CreateIndex
CREATE INDEX "Subscription_stripeSubscriptionId_idx" ON "Subscription"("stripeSubscriptionId");

-- CreateIndex
CREATE INDEX "InstalledNode_userId_idx" ON "InstalledNode"("userId");

-- CreateIndex
CREATE INDEX "InstalledNode_nodeId_idx" ON "InstalledNode"("nodeId");

-- CreateIndex
CREATE INDEX "InstalledNode_status_idx" ON "InstalledNode"("status");

-- CreateIndex
CREATE UNIQUE INDEX "InstalledNode_userId_nodeId_key" ON "InstalledNode"("userId", "nodeId");

-- CreateIndex
CREATE INDEX "NodeExecutionLog_userId_idx" ON "NodeExecutionLog"("userId");

-- CreateIndex
CREATE INDEX "NodeExecutionLog_workflowId_idx" ON "NodeExecutionLog"("workflowId");

-- CreateIndex
CREATE INDEX "NodeExecutionLog_nodeId_idx" ON "NodeExecutionLog"("nodeId");

-- CreateIndex
CREATE INDEX "NodeExecutionLog_status_idx" ON "NodeExecutionLog"("status");

-- CreateIndex
CREATE INDEX "NodeExecutionLog_startedAt_idx" ON "NodeExecutionLog"("startedAt");

-- CreateIndex
CREATE INDEX "NodeCode_nodeId_idx" ON "NodeCode"("nodeId");

-- CreateIndex
CREATE INDEX "NodeCode_version_idx" ON "NodeCode"("version");

-- CreateIndex
CREATE UNIQUE INDEX "NodeCode_nodeId_version_key" ON "NodeCode"("nodeId", "version");

-- CreateIndex
CREATE UNIQUE INDEX "User_stripeCustomerId_key" ON "User"("stripeCustomerId");
