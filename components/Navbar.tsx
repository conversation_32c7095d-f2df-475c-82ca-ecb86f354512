'use client';

import { useSession, signOut } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { ThemeToggle } from '@/components/theme-toggle';
import dynamic from 'next/dynamic';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { HomeIcon, UserIcon, SettingsIcon, ShieldIcon, Menu as MenuIcon } from 'lucide-react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";

// Dynamically import MobileNav with SSR disabled
const MobileNav = dynamic(() => import('@/components/mobile-nav').then(mod => ({ default: mod.MobileNav })), {
  ssr: false,
  loading: () => (
    <Button variant="ghost" size="icon" className="md:hidden">
      <MenuIcon className="h-5 w-5" />
      <span className="sr-only">Menu</span>
    </Button>
  )
});

export default function Navbar() {
  const { data: session, status } = useSession();
  const pathname = usePathname();

  const isAuthenticated = status === 'authenticated';
  const isLoading = status === 'loading';
  const user = session?.user;

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  };

  const userInitials = user?.name
    ? getInitials(user.name)
    : user?.email
      ? user.email.substring(0, 2).toUpperCase()
      : 'U';

  return (
    <nav className="border-b bg-background sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex h-14 items-center">
        {/* Left section: Logo and mobile menu */}
        <div className="flex items-center gap-4 md:gap-6">
          <div className="md:hidden">
            <MobileNav />
          </div>
          <Link href="/" className="font-semibold text-lg flex items-center gap-2 font-sans">
            <ShieldIcon className="h-5 w-5 flex-shrink-0" />
            <span className="font-sans">Auth Demo</span>
          </Link>

          {/* Main navigation */}
          <nav className="hidden md:flex items-center gap-6 text-sm">
            <Link
              href="/"
              className={cn(
                "transition-colors hover:text-foreground/80",
                pathname === "/" ? "text-foreground font-medium" : "text-foreground/60"
              )}
            >
              Overview
            </Link>

            {isAuthenticated && (
              <>
                <Link
                  href="/profile"
                  className={cn(
                    "transition-colors hover:text-foreground/80",
                    pathname === "/profile" ? "text-foreground font-medium" : "text-foreground/60"
                  )}
                >
                  Profile
                </Link>

                <Link
                  href="/settings"
                  className={cn(
                    "transition-colors hover:text-foreground/80",
                    pathname === "/settings" ? "text-foreground font-medium" : "text-foreground/60"
                  )}
                >
                  Settings
                </Link>

                <Link
                  href="/workflow-manager"
                  className={cn(
                    "transition-colors hover:text-foreground/80",
                    pathname === "/workflow-manager" || pathname?.startsWith("/workflow-canvas") ? "text-foreground font-medium" : "text-foreground/60"
                  )}
                >
                  Workflows
                </Link>

                <Link
                  href="/marketplace"
                  className={cn(
                    "transition-colors hover:text-foreground/80",
                    pathname === "/marketplace" ? "text-foreground font-medium" : "text-foreground/60"
                  )}
                >
                  Marketplace
                </Link>
              </>
            )}


          </nav>
        </div>

        {/* Right section: User controls */}
        <div className="flex items-center gap-4 ml-auto">
          <ThemeToggle />

          {isLoading ? (
            <div className="text-sm">Loading...</div>
          ) : isAuthenticated ? (
            <>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.image || ''} alt={user?.name || 'User'} />
                      <AvatarFallback>{userInitials}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end">
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user?.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile">Profile</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings">Settings</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-destructive focus:text-destructive"
                    onClick={() => signOut()}
                  >
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/login">Login</Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/register">Register</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </nav>
  );
}
