"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Download,
  CheckCircle,
  XCircle,
  AlertCircle,
  Package,
  Clock,
  Loader2,
  X,
  Minimize2,
  Maximize2
} from "lucide-react";
import { NodeInstallationManager, InstallationProgress } from "@/lib/node-installation";

export function InstallationProgressPanel() {
  const [installations, setInstallations] = useState<InstallationProgress[]>([]);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const installationManager = NodeInstallationManager.getInstance();

  useEffect(() => {
    // Check for active installations on mount
    const activeInstallations = installationManager.getActiveInstallations();
    setInstallations(activeInstallations);
    setIsVisible(activeInstallations.length > 0);

    // Listen for installation updates
    const handleInstallationUpdate = () => {
      const current = installationManager.getActiveInstallations();
      setInstallations(current);
      setIsVisible(current.length > 0);
    };

    // Poll for updates (in a real app, you'd use WebSockets or Server-Sent Events)
    const interval = setInterval(handleInstallationUpdate, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [installationManager]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
      case 'downloading':
      case 'validating':
      case 'installing':
      case 'configuring':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'pending':
      case 'downloading':
      case 'validating':
      case 'installing':
      case 'configuring':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatDuration = (start: Date, end?: Date) => {
    const endTime = end || new Date();
    const duration = Math.floor((endTime.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) {
      return `${duration}s`;
    } else if (duration < 3600) {
      return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    } else {
      return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`;
    }
  };

  const clearCompleted = () => {
    // This would typically call a method on the installation manager
    // For now, we'll just hide completed installations
    setInstallations(prev => prev.filter(inst => 
      inst.status !== 'completed' && inst.status !== 'failed'
    ));
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96">
      <Card className="shadow-lg border-2">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Package className="h-4 w-4" />
              Node Installations
              {installations.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {installations.length}
                </Badge>
              )}
            </CardTitle>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="h-6 w-6 p-0"
              >
                {isMinimized ? (
                  <Maximize2 className="h-3 w-3" />
                ) : (
                  <Minimize2 className="h-3 w-3" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="pt-0">
            <ScrollArea className="max-h-64">
              <div className="space-y-3">
                {installations.map((installation) => (
                  <div key={installation.nodeId} className="space-y-2 p-3 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(installation.status)}
                        <span className="text-sm font-medium truncate">
                          {installation.nodeId}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {formatDuration(installation.startTime, installation.endTime)}
                      </span>
                    </div>

                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-xs">
                        <span className={getStatusColor(installation.status)}>
                          {installation.message}
                        </span>
                        <span className="text-muted-foreground">
                          {installation.progress}%
                        </span>
                      </div>
                      <Progress value={installation.progress} className="h-1" />
                    </div>

                    {installation.error && (
                      <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
                        {installation.error}
                      </div>
                    )}

                    {installation.status === 'completed' && (
                      <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
                        Installation completed successfully!
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>

            {installations.some(inst => inst.status === 'completed' || inst.status === 'failed') && (
              <div className="mt-3 pt-3 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearCompleted}
                  className="w-full text-xs"
                >
                  Clear Completed
                </Button>
              </div>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  );
}
