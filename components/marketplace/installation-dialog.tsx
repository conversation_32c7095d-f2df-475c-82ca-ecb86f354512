"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Download,
  CheckCircle,
  XCircle,
  AlertCircle,
  Package,
  Clock,
  Loader2,
  X
} from "lucide-react";
import { NodeInstallationManager, InstallationProgress, DependencyInfo } from "@/lib/node-installation";
import { NodePlugin } from "@/lib/marketplace/types";

interface InstallationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  node: NodePlugin | null;
  onInstallComplete?: (success: boolean) => void;
}

export function InstallationDialog({
  open,
  onOpenChange,
  node,
  onInstallComplete
}: InstallationDialogProps) {
  const [progress, setProgress] = useState<InstallationProgress | null>(null);
  const [dependencies, setDependencies] = useState<DependencyInfo[]>([]);
  const [isInstalling, setIsInstalling] = useState(false);
  const [error, setError] = useState<string>("");

  const installationManager = NodeInstallationManager.getInstance();

  useEffect(() => {
    if (!open || !node) {
      setProgress(null);
      setDependencies([]);
      setIsInstalling(false);
      setError("");
      return;
    }

    // Check dependencies when dialog opens
    checkDependencies();
  }, [open, node]);

  const checkDependencies = async () => {
    if (!node) return;

    try {
      const response = await fetch(`/api/nodes/${node.id}/dependencies`, {
        credentials: 'include'
      });
      if (response.ok) {
        const deps = await response.json();
        setDependencies(deps);
      } else {
        console.warn('Failed to check dependencies:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Failed to check dependencies:', error);
      // Don't show error to user for dependency check failure
      // as it's not critical for installation
    }
  };

  const handleInstall = async () => {
    if (!node) return;

    console.log('Starting installation for node:', node.id);
    setIsInstalling(true);
    setError("");

    // Subscribe to progress updates
    const unsubscribe = installationManager.onProgress(node.id, (progress) => {
      console.log('Installation progress:', progress);
      setProgress(progress);
    });

    try {
      console.log('Calling installationManager.installNode...');
      const success = await installationManager.installNode(node.id, {
        version: node.version,
        onProgress: setProgress
      });

      console.log('Installation result:', success);
      if (success) {
        setTimeout(() => {
          onInstallComplete?.(true);
          onOpenChange(false);
        }, 2000); // Show success for 2 seconds
      } else {
        setError('Installation failed. Please try again.');
        onInstallComplete?.(false);
      }
    } catch (error) {
      console.error('Installation error in dialog:', error);
      const errorMessage = error instanceof Error ? error.message : 'Installation failed';
      setError(errorMessage);
      onInstallComplete?.(false);
    } finally {
      setIsInstalling(false);
      unsubscribe();
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
      case 'downloading':
      case 'validating':
      case 'installing':
      case 'configuring':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'pending':
      case 'downloading':
      case 'validating':
      case 'installing':
      case 'configuring':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  if (!node) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Install Node
          </DialogTitle>
          <DialogDescription>
            Install "{node.name}" to your workflow library
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Node Info */}
          <div className="flex items-start gap-3 p-3 border rounded-lg">
            <img
              src={node.icon}
              alt={node.name}
              className="w-10 h-10 rounded object-cover"
            />
            <div className="flex-1">
              <h3 className="font-medium">{node.name}</h3>
              <p className="text-sm text-muted-foreground">{node.description}</p>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">v{node.version}</Badge>
                <Badge variant={node.tier === 'free' ? 'secondary' : 'default'}>
                  {node.tier}
                </Badge>
              </div>
            </div>
          </div>

          {/* Dependencies */}
          {dependencies.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Dependencies</h4>
              <div className="space-y-1">
                {dependencies.map((dep) => (
                  <div key={dep.nodeId} className="flex items-center justify-between text-sm">
                    <span>{dep.nodeId}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        v{dep.version}
                      </Badge>
                      {dep.installed ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <Download className="h-4 w-4 text-blue-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Installation Progress */}
          {progress && (
            <div className="space-y-3">
              <Separator />
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(progress.status)}
                    <span className={`text-sm font-medium ${getStatusColor(progress.status)}`}>
                      {progress.message}
                    </span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {progress.progress}%
                  </span>
                </div>

                <Progress value={progress.progress} className="h-2" />

                {progress.status === 'completed' && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Node installed successfully! You can now use it in your workflows.
                    </AlertDescription>
                  </Alert>
                )}

                {progress.status === 'failed' && progress.error && (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>
                      {progress.error}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && !progress && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Installation Info */}
          {!progress && !error && (
            <div className="text-sm text-muted-foreground space-y-1">
              <p>This will install the node and its dependencies to your library.</p>
              {node.tier !== 'free' && (
                <p className="text-amber-600">
                  ⚠️ This is a premium node. Make sure you have the required subscription.
                </p>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isInstalling && progress?.status !== 'completed' && progress?.status !== 'failed'}
          >
            {progress?.status === 'completed' ? 'Close' : 'Cancel'}
          </Button>

          {!progress || progress.status === 'failed' ? (
            <Button
              onClick={handleInstall}
              disabled={isInstalling}
              className="min-w-[100px]"
            >
              {isInstalling ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Installing...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Install
                </>
              )}
            </Button>
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  );
}
