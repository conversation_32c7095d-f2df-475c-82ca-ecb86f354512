"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  RefreshCw, 
  Download, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Package,
  TrendingUp,
  Shield
} from "lucide-react";
import { NodeUpdateInfo, UpdateResult } from "@/lib/node-updater";

interface NodeUpdatesProps {
  className?: string;
}

export function NodeUpdates({ className }: NodeUpdatesProps) {
  const [updates, setUpdates] = useState<NodeUpdateInfo[]>([]);
  const [stats, setStats] = useState({
    totalInstalled: 0,
    hasUpdates: 0,
    breakingUpdates: 0,
    safeUpdates: 0
  });
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<Set<string>>(new Set());
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkForUpdates = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/nodes/updates');
      if (response.ok) {
        const data = await response.json();
        setUpdates(data.updates);
        setStats(data.stats);
        setLastChecked(new Date());
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateNode = async (nodeId: string) => {
    setUpdating(prev => new Set(prev).add(nodeId));
    try {
      const response = await fetch('/api/nodes/updates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nodeId })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.result.success) {
          // Remove the updated node from the list
          setUpdates(prev => prev.filter(update => update.nodeId !== nodeId));
          setStats(prev => ({
            ...prev,
            hasUpdates: prev.hasUpdates - 1,
            safeUpdates: prev.safeUpdates - 1
          }));
        }
      }
    } catch (error) {
      console.error('Error updating node:', error);
    } finally {
      setUpdating(prev => {
        const newSet = new Set(prev);
        newSet.delete(nodeId);
        return newSet;
      });
    }
  };

  const updateAllSafe = async () => {
    try {
      const response = await fetch('/api/nodes/updates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updateAll: true })
      });

      if (response.ok) {
        const data = await response.json();
        // Refresh the updates list
        await checkForUpdates();
      }
    } catch (error) {
      console.error('Error updating all nodes:', error);
    }
  };

  useEffect(() => {
    checkForUpdates();
  }, []);

  const getUpdateTypeColor = (updateType: string) => {
    switch (updateType) {
      case 'major': return 'destructive';
      case 'minor': return 'default';
      case 'patch': return 'secondary';
      default: return 'outline';
    }
  };

  const getUpdateTypeIcon = (updateType: string) => {
    switch (updateType) {
      case 'major': return <AlertTriangle className="h-4 w-4" />;
      case 'minor': return <TrendingUp className="h-4 w-4" />;
      case 'patch': return <Shield className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Node Updates
            {stats.hasUpdates > 0 && (
              <Badge variant="destructive" className="ml-2">
                {stats.hasUpdates}
              </Badge>
            )}
          </div>
          <div className="flex gap-2">
            {stats.safeUpdates > 0 && (
              <Button
                size="sm"
                onClick={updateAllSafe}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Update All Safe
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={checkForUpdates}
              disabled={loading}
              className="gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Check
            </Button>
          </div>
        </CardTitle>
        
        {lastChecked && (
          <p className="text-sm text-muted-foreground">
            Last checked: {lastChecked.toLocaleTimeString()}
          </p>
        )}
      </CardHeader>

      <CardContent>
        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold">{stats.totalInstalled}</div>
            <div className="text-sm text-muted-foreground">Installed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.hasUpdates}</div>
            <div className="text-sm text-muted-foreground">Updates</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.safeUpdates}</div>
            <div className="text-sm text-muted-foreground">Safe</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.breakingUpdates}</div>
            <div className="text-sm text-muted-foreground">Breaking</div>
          </div>
        </div>

        <Separator className="mb-4" />

        {/* Updates List */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Checking for updates...
          </div>
        ) : updates.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-medium mb-2">All nodes are up to date!</h3>
            <p className="text-muted-foreground">
              Your installed nodes are running the latest versions.
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[400px]">
            <div className="space-y-3">
              {updates.map((update) => (
                <Card key={update.nodeId} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">Node {update.nodeId}</h4>
                        <Badge 
                          variant={getUpdateTypeColor(update.updateType)}
                          className="gap-1"
                        >
                          {getUpdateTypeIcon(update.updateType)}
                          {update.updateType}
                        </Badge>
                        {update.breaking && (
                          <Badge variant="destructive" className="gap-1">
                            <AlertTriangle className="h-3 w-3" />
                            Breaking
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-muted-foreground mb-2">
                        {update.currentVersion} → {update.latestVersion}
                      </div>

                      {update.changelog && (
                        <div className="text-sm bg-muted p-2 rounded mb-2">
                          <strong>Changelog:</strong> {update.changelog}
                        </div>
                      )}

                      {update.breaking && (
                        <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                          ⚠️ This is a breaking change that may affect existing workflows.
                          Please review the changelog before updating.
                        </div>
                      )}
                    </div>

                    <Button
                      size="sm"
                      onClick={() => updateNode(update.nodeId)}
                      disabled={updating.has(update.nodeId)}
                      variant={update.breaking ? "outline" : "default"}
                      className="ml-4"
                    >
                      {updating.has(update.nodeId) ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                          Updating
                        </>
                      ) : (
                        <>
                          <Download className="h-4 w-4 mr-1" />
                          Update
                        </>
                      )}
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
