"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Trash2,
  Settings,
  Download,
  AlertCircle,
  CheckCircle,
  Clock,
  Package,
  TrendingUp,
  Store
} from "lucide-react";
import { Marketplace } from "./marketplace";

// Define types for the API response
interface InstalledNodeData {
  id: string;
  nodeId: string;
  version: string;
  status: string;
  enabled: boolean;
  config: any;
  installedAt: string;
  updatedAt: string;
  lastUsed?: string;
  node: {
    id: string;
    name: string;
    description: string;
    icon: string;
    category: string;
    tier: string;
    version: string;
    tags: string[];
    author: {
      id: string;
      name: string;
      email: string;
      avatar?: string;
    };
    verified: boolean;
    rating: number;
    downloads: number;
  };
}

interface NodeManagerProps {
  onNodeToggle?: (nodeId: string, enabled: boolean) => void;
  onNodeUninstall?: (nodeId: string) => void;
}

export function NodeManager({ onNodeToggle, onNodeUninstall }: NodeManagerProps) {
  const [installedNodes, setInstalledNodes] = useState<InstalledNodeData[]>([]);
  const [availableUpdates, setAvailableUpdates] = useState<InstalledNodeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('installed');

  useEffect(() => {
    loadInstalledNodes();
  }, []);

  const loadInstalledNodes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/nodes/installed');

      if (!response.ok) {
        throw new Error('Failed to fetch installed nodes');
      }

      const data = await response.json();

      if (data.success) {
        setInstalledNodes(data.installedNodes);

        // Filter nodes that have updates available (compare versions)
        const updates = data.installedNodes.filter((installation: InstalledNodeData) =>
          installation.version !== installation.node.version
        );
        setAvailableUpdates(updates);
      } else {
        console.error('Failed to load installed nodes:', data.error);
      }
    } catch (error) {
      console.error('Failed to load installed nodes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleNode = async (nodeId: string, enabled: boolean) => {
    try {
      const response = await fetch('/api/nodes/installed', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId, enabled }),
      });

      if (!response.ok) {
        throw new Error('Failed to toggle node');
      }

      onNodeToggle?.(nodeId, enabled);
      loadInstalledNodes();
    } catch (error) {
      console.error('Failed to toggle node:', error);
    }
  };

  const handleUninstallNode = async (nodeId: string) => {
    try {
      const response = await fetch(`/api/nodes/uninstall`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId }),
      });

      if (!response.ok) {
        throw new Error('Failed to uninstall node');
      }

      onNodeUninstall?.(nodeId);
      loadInstalledNodes();
    } catch (error) {
      console.error('Failed to uninstall node:', error);
    }
  };

  const getStatusIcon = (installation: InstalledNodeData) => {
    if (!installation.enabled) {
      return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
    if (installation.version !== installation.node.version) {
      return <Download className="h-4 w-4 text-blue-500" />;
    }
    if (installation.status === 'error') {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  const getStatusText = (installation: InstalledNodeData) => {
    if (!installation.enabled) {
      return 'Disabled';
    }
    if (installation.version !== installation.node.version) {
      return 'Update Available';
    }
    if (installation.status === 'error') {
      return 'Error';
    }
    return 'Installed';
  };

  const InstalledNodeCard = ({ installation }: { installation: InstalledNodeData }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <img
            src={installation.node.icon}
            alt={installation.node.name}
            className="w-10 h-10 rounded-md object-cover"
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold truncate">{installation.node.name}</h3>
              <Badge variant="outline" className="text-xs">
                v{installation.version}
              </Badge>
              {installation.node.verified && (
                <Badge variant="secondary" className="text-xs">
                  Verified
                </Badge>
              )}
              {getStatusIcon(installation)}
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Avatar className="w-4 h-4">
                <AvatarImage src={installation.node.author.avatar} />
                <AvatarFallback className="text-xs">
                  {installation.node.author.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span>{installation.node.author.name}</span>
              <span>•</span>
              <span>{getStatusText(installation)}</span>
            </div>
            <p className="text-sm text-muted-foreground truncate mt-1">
              {installation.node.description}
            </p>
            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Download className="h-3 w-3" />
                <span>{installation.node.downloads.toLocaleString()} downloads</span>
              </div>
              <div className="flex items-center gap-1">
                <span>★ {installation.node.rating.toFixed(1)}</span>
              </div>
              {installation.lastUsed && (
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  <span>Last used {new Date(installation.lastUsed).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Switch
              checked={installation.enabled}
              onCheckedChange={(enabled) => handleToggleNode(installation.nodeId, enabled)}
              disabled={installation.status === 'error'}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUninstallNode(installation.nodeId)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Calculate stats from installed nodes
  const stats = {
    total: installedNodes.length,
    enabled: installedNodes.filter(node => node.enabled).length,
    disabled: installedNodes.filter(node => !node.enabled).length,
    updates: availableUpdates.length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Node Manager</h1>
        <p className="text-muted-foreground">
          Manage your installed nodes and discover new ones
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Nodes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Enabled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.enabled}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Disabled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-muted-foreground">{stats.disabled}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Updates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.updates}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="installed" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Installed ({installedNodes.length})
          </TabsTrigger>
          <TabsTrigger value="updates" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Updates ({availableUpdates.length})
          </TabsTrigger>
          <TabsTrigger value="marketplace" className="flex items-center gap-2">
            <Store className="h-4 w-4" />
            Marketplace
          </TabsTrigger>
        </TabsList>

        <TabsContent value="installed" className="space-y-4">
          {installedNodes.length > 0 ? (
            <div className="space-y-3">
              {installedNodes.map((installation) => (
                <InstalledNodeCard key={installation.id} installation={installation} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No nodes installed</h3>
              <p className="text-muted-foreground mb-4">
                Browse the marketplace to discover and install nodes
              </p>
              <Button onClick={() => setActiveTab('marketplace')}>
                Browse Marketplace
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="updates" className="space-y-4">
          {availableUpdates.length > 0 ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  {availableUpdates.length} update{availableUpdates.length !== 1 ? 's' : ''} available
                </p>
                <Button size="sm">Update All</Button>
              </div>
              {availableUpdates.map((installation) => (
                <InstalledNodeCard key={installation.id} installation={installation} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">All nodes are up to date</h3>
              <p className="text-muted-foreground">
                Your installed nodes are running the latest versions
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="marketplace">
          <Marketplace />
        </TabsContent>
      </Tabs>
    </div>
  );
}
