"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Star,
  Download,
  Eye,
  ShoppingCart,
  Check,
  Clock,
  AlertCircle,
  ExternalLink,
  BadgeCheck,
  Loader2,
  Trash2
} from "lucide-react";
import { NodePlugin, NodeTier, InstallationStatus } from "@/lib/marketplace/types";
import { marketplaceHelpers } from "@/lib/marketplace/api";
import { useInstalledNodes } from "@/hooks/use-installed-nodes";
import { InstallationDialog } from "./installation-dialog";
import { NodeInstallationManager, InstallationProgress } from "@/lib/node-installation";

interface NodeCardProps {
  node: NodePlugin;
  installationStatus?: InstallationStatus;
  onInstall?: (nodeId: string) => void;
  onUninstall?: (nodeId: string) => void;
  onPreview?: (node: NodePlugin) => void;
  onPurchase?: (nodeId: string) => void;
  compact?: boolean;
}

export function NodeCard({
  node,
  installationStatus = InstallationStatus.NOT_INSTALLED,
  onInstall,
  onUninstall,
  onPreview,
  onPurchase,
  compact = false
}: NodeCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showInstallDialog, setShowInstallDialog] = useState(false);
  const [installProgress, setInstallProgress] = useState<InstallationProgress | null>(null);
  const router = useRouter();
  const { isNodeInstalled, fetchInstalledNodes, uninstallNode } = useInstalledNodes();
  const installationManager = NodeInstallationManager.getInstance();

  const handleViewDetails = () => {
    if (onPreview) {
      onPreview(node);
    } else {
      router.push(`/marketplace/node/${node.id}`);
    }
  };

  // Check for active installation progress
  useEffect(() => {
    const progress = installationManager.getProgress(node.id);
    if (progress) {
      setInstallProgress(progress);
    }

    // Subscribe to progress updates
    const unsubscribe = installationManager.onProgress(node.id, (progress) => {
      setInstallProgress(progress);
    });

    return unsubscribe;
  }, [node.id, installationManager]);

  const handleInstall = async () => {
    if (node.tier === 'free') {
      setShowInstallDialog(true);
    } else if (onInstall) {
      setIsLoading(true);
      try {
        await onInstall(node.id);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleInstallComplete = (success: boolean) => {
    if (success) {
      fetchInstalledNodes(); // Refresh installed nodes
    }
    setInstallProgress(null);
  };

  const handlePurchase = async () => {
    if (!onPurchase) return;

    setIsLoading(true);
    try {
      await onPurchase(node.id);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUninstall = async () => {
    try {
      setIsLoading(true);
      const success = await uninstallNode(node.id);
      if (success && onUninstall) {
        onUninstall(node.id);
      }
    } catch (error) {
      console.error('Failed to uninstall node:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTierBadge = (tier: NodeTier) => {
    switch (tier) {
      case NodeTier.FREE:
        return <Badge variant="secondary">Free</Badge>;
      case NodeTier.PREMIUM:
        return <Badge variant="default">Premium</Badge>;
      case NodeTier.ENTERPRISE:
        return <Badge variant="destructive">Enterprise</Badge>;
      case NodeTier.SUBSCRIPTION:
        return <Badge variant="outline">Subscription</Badge>;
      default:
        return null;
    }
  };

  const getInstallationButton = () => {
    // Check if node is already installed
    if (isNodeInstalled(node.id)) {
      return (
        <div className="flex gap-1 w-full">
          <Button variant="outline" disabled className="flex-1">
            <Check className="h-4 w-4 mr-2" />
            Installed
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleUninstall}
            disabled={isLoading}
            className="px-2"
            title="Uninstall node"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      );
    }

    // Check for active installation progress
    if (installProgress) {
      switch (installProgress.status) {
        case 'completed':
          return (
            <Button variant="outline" disabled className="w-full">
              <Check className="h-4 w-4 mr-2" />
              Installed
            </Button>
          );
        case 'failed':
          return (
            <Button variant="destructive" onClick={handleInstall} disabled={isLoading} className="w-full">
              <AlertCircle className="h-4 w-4 mr-2" />
              Retry Install
            </Button>
          );
        default:
          return (
            <Button variant="outline" disabled className="w-full">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {installProgress.message}
            </Button>
          );
      }
    }

    // Check installation status
    switch (installationStatus) {
      case InstallationStatus.INSTALLED:
        return (
          <Button variant="outline" disabled className="w-full">
            <Check className="h-4 w-4 mr-2" />
            Installed
          </Button>
        );
      case InstallationStatus.INSTALLING:
        return (
          <Button variant="outline" disabled className="w-full">
            <Clock className="h-4 w-4 mr-2 animate-spin" />
            Installing...
          </Button>
        );
      case InstallationStatus.UPDATE_AVAILABLE:
        return (
          <Button onClick={handleInstall} disabled={isLoading} className="w-full">
            <Download className="h-4 w-4 mr-2" />
            Update
          </Button>
        );
      case InstallationStatus.ERROR:
        return (
          <Button variant="destructive" onClick={handleInstall} disabled={isLoading} className="w-full">
            <AlertCircle className="h-4 w-4 mr-2" />
            Retry Install
          </Button>
        );
      default:
        if (node.tier === NodeTier.FREE) {
          return (
            <Button onClick={handleInstall} disabled={isLoading} size="sm" className="w-full">
              <Download className="h-4 w-4 mr-1" />
              Install
            </Button>
          );
        } else {
          return (
            <Button onClick={handlePurchase} disabled={isLoading} size="sm" className="w-full">
              <ShoppingCart className="h-4 w-4 mr-1" />
              Purchase
            </Button>
          );
        }
    }
  };

  const StarRating = ({ rating, size = 4 }: { rating: number; size?: number }) => {
    const sizeClass = size === 3 ? 'h-3 w-3' : 'h-4 w-4';

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClass} ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-muted-foreground'
            }`}
          />
        ))}
        <span className="text-xs text-muted-foreground ml-1">
          ({node.reviewCount})
        </span>
      </div>
    );
  };

  if (compact) {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items- gap-3">
            <img
              src={node.icon}
              alt={node.name}
              className="w-10 h-10 rounded-md object-cover border"
            />
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm line-clamp-1 mb-2">{node.name}</h3>

              <div className="flex items-center gap-0 text-xs text-muted-foreground mt-2">
               <BadgeCheck className="h-3 w-3 text-blue-500 mr-1" />
                <span className="truncate text-xs">by {node.author.name}</span>
               
              </div>

              {/* Star Rating with Stats */}
              <div className="flex items-center justify-between mb-2">
                <StarRating rating={node.rating} size={3} />
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>{marketplaceHelpers.formatDownloads(node.downloads)}</span>
                  <span>v{node.version}</span>
                </div>
              </div>

              <div className="flex items-center gap-1.5 mb-1">
                {getTierBadge(node.tier)}
                {node.featured && (
                  <Badge variant="outline" className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
                    Featured
                  </Badge>
                )}
              </div>

              <p className="text-xs text-muted-foreground line-clamp-2 mb-1">
                {node.description}
              </p>

              {/* Price - Below Description */}
              {node.tier === NodeTier.FREE ? (
                <div className="text-xs font-semibold text-green-600">
                  Free
                </div>
              ) : node.price && (
                <div className="text-xs font-semibold text-primary">
                  {marketplaceHelpers.formatPrice(node.price)}
                </div>
              )}
            </div>
            <div className="flex flex-col gap-2">
              {getInstallationButton()}
              <Button
                variant="outline"
                size="sm"
                onClick={handleViewDetails}
                className="text-xs"
              >
                <Eye className="h-3 w-3 mr-1" />
                View
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-lg transition-all duration-200 group h-full flex flex-col">
      <CardHeader className="pb-3">
        {/* Top Section: Icon and Name */}
        <div className="flex items- gap-3">
          <div className="flex-shrink-0">
            <img
              src={node.icon}
              alt={node.name}
              className="w-12 h-12 rounded-lg object-cover border"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-base leading-tight line-clamp-2 mb-3">{node.name}</h3>
          </div>
        </div>

        {/* Author Section with Verified Badge */}
        <div className="flex items-center gap-0 text-sm text-muted-foreground mt-2">
          
          <BadgeCheck className="h-3 w-3 text-blue-500 mr-1" />
          <span className="truncate text-xs"> {node.author.name}</span>
         
        </div>

        {/* Star Rating with Stats */}
        <div className="flex items-center justify-between mb-3">
          <StarRating rating={node.rating} size={3} />
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>{marketplaceHelpers.formatDownloads(node.downloads)}</span>
            <span>v{node.version}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-3">
        {/* Badges Section */}
        <div className="flex flex-wrap gap-1.5 mb-2 min-h-[20px]">
          {getTierBadge(node.tier)}
          {node.featured && (
            <Badge variant="outline" className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
              Featured
            </Badge>
          )}
        </div> 
        <div className="flex flex-wrap gap-1 mb-2 min-h-[20px]">
          {node.tags.slice(0, 2).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {node.tags.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{node.tags.length - 2}
            </Badge>
          )}
        </div>

        <p className="text-xs text-muted-foreground line-clamp-3 mb-2 flex-1 min-h-[48px]">
          {node.description}
        </p>

        {/* Price Section - Below Description */}
        {node.tier === NodeTier.FREE ? (
          <div className="text-sm font-semibold text-green-600 mb-2">
            Free
          </div>
        ) : node.price && (
          <div className="text-sm font-semibold text-primary mb-2">
            {marketplaceHelpers.formatPrice(node.price)}
          </div>
        )}

        

        {/* Bottom Section: Action Buttons */}
        <div className="flex gap-2 mt-auto">
          <div className="flex-1">
            {getInstallationButton()}
          </div>
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={handleViewDetails}
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>
        </div>
      </CardContent>

      {/* Installation Dialog */}
      <InstallationDialog
        open={showInstallDialog}
        onOpenChange={setShowInstallDialog}
        node={node}
        onInstallComplete={handleInstallComplete}
      />
    </Card>
  );
}
