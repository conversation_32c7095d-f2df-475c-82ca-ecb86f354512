"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FreePlanActivation } from './free-plan-activation';
import { subscriptionPlans, formatPrice } from '@/lib/doku/config';
import {
  Crown,
  Calendar,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Loader2,
  X
} from 'lucide-react';

interface Subscription {
  id: string;
  status: string;
  planId: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  trialStart?: string;
  trialEnd?: string;
}

export function SubscriptionManager() {
  const { data: session, status } = useSession();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [canceling, setCanceling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFreePlanModal, setShowFreePlanModal] = useState(false);

  useEffect(() => {
    if (session) {
      fetchSubscription();
    }
  }, [session]);

  const fetchSubscription = async () => {
    try {
      const response = await fetch('/api/payments/subscriptions');
      if (response.ok) {
        const data = await response.json();
        setSubscription(data.subscription);
      }
    } catch (err: any) {
      setError('Failed to fetch subscription details');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeSubscription = async (newPlanId: string) => {
    if (!subscription) return;

    setLoading(true);
    setError(null);

    try {
      // If upgrading to a paid plan from free, use checkout
      if (subscription.planId === 'free' && newPlanId !== 'free') {
        const response = await fetch('/api/payments/checkout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ planId: newPlanId }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create checkout session');
        }

        const { checkoutUrl } = await response.json();
        window.open(checkoutUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        return;
      }

      // For plan changes between paid plans
      const response = await fetch('/api/payments/subscriptions/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ newPlanId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update subscription');
      }

      await fetchSubscription();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async (immediately: boolean = false, reason?: string) => {
    if (!subscription) return;

    setCanceling(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        subscriptionId: subscription.id,
        immediately: immediately.toString(),
        ...(reason && { reason })
      });

      const response = await fetch(
        `/api/payments/subscriptions?${params}`,
        { method: 'DELETE' }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to cancel subscription');
      }

      await fetchSubscription();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setCanceling(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'trialing':
        return <Badge className="bg-blue-100 text-blue-800">Trial</Badge>;
      case 'past_due':
        return <Badge variant="destructive">Past Due</Badge>;
      case 'canceled':
        return <Badge variant="secondary">Canceled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Show loading while session is loading or fetching subscription
  if (status === 'loading' || loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          Loading subscription details...
        </CardContent>
      </Card>
    );
  }

  // Don't render if not authenticated
  if (!session) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Please log in to view your subscription.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleSelectPlan = async (planId: string) => {
    if (planId === 'free') {
      setShowFreePlanModal(true);
      return;
    }

    // For paid plans, redirect to payment flow
    console.log('Redirecting to payment for plan:', planId);
    // TODO: Implement payment flow for subscriptions
  };

  const handleActivateFreePlan = async () => {
    const response = await fetch('/api/payments/subscriptions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ planId: 'free' }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to activate free plan');
    }

    // Refresh subscription data
    await fetchSubscription();
  };

  // Main component render
  return (
    <>
      {/* Subscription Content */}
      {subscription ? renderActiveSubscription() : renderPlanSelection()}

      {/* Free Plan Activation Modal */}
      <FreePlanActivation
        isOpen={showFreePlanModal}
        onClose={() => setShowFreePlanModal(false)}
        onActivate={handleActivateFreePlan}
      />
    </>
  );

  function renderActiveSubscription() {
    const plan = subscriptionPlans[subscription!.planId as keyof typeof subscriptionPlans];

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Crown className="h-5 w-5 mr-2" />
              Current Subscription
            </div>
            {getStatusBadge(subscription!.status)}
          </CardTitle>
          <CardDescription>
            Manage your subscription and billing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Plan Details */}
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg">{plan?.name || subscription!.planId}</h3>
              <p className="text-muted-foreground">{plan?.description}</p>
              <div className="text-2xl font-bold mt-2">
                {plan?.price === 0 ? 'Free' : `${formatPrice(plan?.price || 0, 'IDR')}/month`}
              </div>
            </div>

            {/* Billing Period */}
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                <span>Current period:</span>
              </div>
              <span>
                {formatDate(subscription!.currentPeriodStart)} - {formatDate(subscription!.currentPeriodEnd)}
              </span>
            </div>

            {/* Trial Information */}
            {subscription!.trialEnd && new Date(subscription!.trialEnd) > new Date() && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Your trial ends on {formatDate(subscription!.trialEnd)}
                </AlertDescription>
              </Alert>
            )}

            {/* Cancellation Notice */}
            {subscription!.cancelAtPeriodEnd && (
              <Alert variant="destructive">
                <X className="h-4 w-4" />
                <AlertDescription>
                  Your subscription will be canceled on {formatDate(subscription!.currentPeriodEnd)}
                </AlertDescription>
              </Alert>
            )}
          </div>

          <Separator />

          {/* Features */}
          {plan?.features && (
            <div>
              <h4 className="font-medium mb-3">Included Features</h4>
              <ul className="space-y-2">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <Separator />

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Plan Upgrade/Downgrade Options */}
          <div>
            <h4 className="font-medium mb-3">Available Plans</h4>
            <div className="grid gap-3 md:grid-cols-2">
              {Object.entries(subscriptionPlans)
                .filter(([planId]) => planId !== subscription!.planId)
                .map(([planId, plan]) => (
                  <Card key={planId} className="border">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h5 className="font-medium">{plan.name}</h5>
                          <p className="text-sm text-muted-foreground">{plan.description}</p>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">
                            {plan.price === 0 ? 'Free' : `${formatPrice(plan.price, 'IDR')}/mo`}
                          </div>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant={plan.price > (subscriptionPlans[subscription!.planId as keyof typeof subscriptionPlans]?.price || 0) ? 'default' : 'outline'}
                        onClick={() => handleUpgradeSubscription(planId)}
                        disabled={loading}
                        className="w-full"
                      >
                        {loading ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : plan.price > (subscriptionPlans[subscription!.planId as keyof typeof subscriptionPlans]?.price || 0) ? (
                          'Upgrade'
                        ) : (
                          'Downgrade'
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </div>

          <Separator />

          {/* Actions */}
          <div className="flex space-x-3">
            {subscription!.planId !== 'free' && (
              <Button variant="outline" className="flex-1">
                <CreditCard className="h-4 w-4 mr-2" />
                Update Payment Method
              </Button>
            )}

            {subscription!.status === 'active' && !subscription!.cancelAtPeriodEnd && subscription!.planId !== 'free' && (
              <Button
                variant="destructive"
                onClick={() => handleCancelSubscription(false, 'User requested cancellation')}
                disabled={canceling}
                className="flex-1"
              >
                {canceling ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Canceling...
                  </>
                ) : (
                  <>
                    <X className="h-4 w-4 mr-2" />
                    Cancel Subscription
                  </>
                )}
              </Button>
            )}
          </div>

          {/* Billing History Link */}
          {subscription!.planId !== 'free' && (
            <div className="text-center">
              <Button variant="link" className="text-sm">
                View Billing History
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  function renderPlanSelection() {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Crown className="h-5 w-5 mr-2" />
            Subscription Plans
          </CardTitle>
          <CardDescription>
            Choose a plan to unlock premium features and nodes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(subscriptionPlans).map(([planId, plan]) => (
              <Card
                key={planId}
                className={`border-2 relative ${
                  (plan as any).popular ? 'border-primary shadow-lg' : 'border-border'
                }`}
              >
                {(plan as any).popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground">
                      Most Popular
                    </Badge>
                  </div>
                )}
                <CardHeader>
                  <CardTitle className="text-lg">{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="text-2xl font-bold">
                    {plan.price === 0 ? 'Free' : `$${plan.price}/month`}
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 mb-4">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button
                    className="w-full"
                    variant={plan.price === 0 ? 'outline' : 'default'}
                    onClick={() => handleSelectPlan(planId)}
                  >
                    {plan.price === 0 ? 'Get Started Free' : `Subscribe to ${plan.name}`}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
}
