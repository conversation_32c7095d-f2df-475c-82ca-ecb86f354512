"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Settings, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  CreditCard,
  Store,
  Crown,
  Code,
  ExternalLink
} from "lucide-react";
import { 
  useSettings,
  usePaymentStatus,
  useMarketplaceStatus,
  useSubscriptionStatus,
  useDeveloperSettings
} from "@/lib/settings/settings-context";
import Link from "next/link";

export function SettingsStatusCard() {
  const { loading, error } = useSettings();
  const paymentStatus = usePaymentStatus();
  const marketplaceStatus = useMarketplaceStatus();
  const subscriptionStatus = useSubscriptionStatus();
  const developerSettings = useDeveloperSettings();

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>Loading system configuration...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>Error loading system configuration</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load settings: {error}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = (isReady: boolean, isEnabled: boolean) => {
    if (isReady) return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (isEnabled) return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    return <XCircle className="h-4 w-4 text-gray-400" />;
  };

  const getStatusBadge = (isReady: boolean, isEnabled: boolean) => {
    if (isReady) return <Badge variant="default" className="bg-green-500">Ready</Badge>;
    if (isEnabled) return <Badge variant="secondary">Needs Config</Badge>;
    return <Badge variant="outline">Disabled</Badge>;
  };

  const systemFeatures = [
    {
      name: "Payment Processing",
      icon: CreditCard,
      enabled: paymentStatus.isEnabled,
      ready: paymentStatus.isReady,
      description: paymentStatus.isReady 
        ? `${paymentStatus.provider} configured (${paymentStatus.testMode ? 'Test' : 'Live'} mode)`
        : paymentStatus.isEnabled 
          ? "Payment provider needs configuration"
          : "Payment processing is disabled",
      configPath: "/settings#payments"
    },
    {
      name: "Marketplace",
      icon: Store,
      enabled: marketplaceStatus.isEnabled,
      ready: marketplaceStatus.isEnabled && (marketplaceStatus.freeNodesAvailable || marketplaceStatus.paidNodesAvailable),
      description: marketplaceStatus.isEnabled
        ? `Free nodes: ${marketplaceStatus.freeNodesAvailable ? 'Yes' : 'No'}, Paid nodes: ${marketplaceStatus.paidNodesAvailable ? 'Yes' : 'No'}`
        : "Marketplace is disabled",
      configPath: "/settings#marketplace"
    },
    {
      name: "Subscriptions",
      icon: Crown,
      enabled: subscriptionStatus.isEnabled,
      ready: subscriptionStatus.isReady,
      description: subscriptionStatus.isEnabled
        ? `Free plan: ${subscriptionStatus.allowFreePlan ? 'Yes' : 'No'}, Upgrades: ${subscriptionStatus.allowUpgrades ? 'Yes' : 'No'}`
        : "Subscription system is disabled",
      configPath: "/settings#subscriptions"
    },
    {
      name: "Developer Tools",
      icon: Code,
      enabled: developerSettings.enabled,
      ready: developerSettings.enabled && developerSettings.nodePublishingEnabled,
      description: developerSettings.enabled
        ? `Publishing: ${developerSettings.nodePublishingEnabled ? 'Enabled' : 'Disabled'}, Analytics: ${developerSettings.analyticsEnabled ? 'Yes' : 'No'}`
        : "Developer tools are disabled",
      configPath: "/settings#developer"
    }
  ];

  const readyCount = systemFeatures.filter(f => f.ready).length;
  const enabledCount = systemFeatures.filter(f => f.enabled).length;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          System Status
        </CardTitle>
        <CardDescription>
          {readyCount} of {systemFeatures.length} features fully configured
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Status */}
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div>
            <p className="font-medium">System Health</p>
            <p className="text-sm text-muted-foreground">
              {enabledCount} enabled, {readyCount} ready
            </p>
          </div>
          <div className="flex items-center gap-2">
            {readyCount === systemFeatures.length ? (
              <Badge variant="default" className="bg-green-500">All Systems Ready</Badge>
            ) : enabledCount > 0 ? (
              <Badge variant="secondary">Partial Configuration</Badge>
            ) : (
              <Badge variant="outline">Minimal Setup</Badge>
            )}
          </div>
        </div>

        {/* Feature Status List */}
        <div className="space-y-3">
          {systemFeatures.map((feature) => {
            const IconComponent = feature.icon;
            return (
              <div key={feature.name} className="flex items-center justify-between p-2 border rounded-lg">
                <div className="flex items-center gap-3">
                  <IconComponent className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-sm">{feature.name}</p>
                    <p className="text-xs text-muted-foreground">{feature.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(feature.ready, feature.enabled)}
                  {getStatusBadge(feature.ready, feature.enabled)}
                </div>
              </div>
            );
          })}
        </div>

        {/* Configuration Recommendations */}
        {readyCount < systemFeatures.length && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Configuration Needed:</strong> Some features require additional setup to work properly.
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button asChild size="sm" className="flex-1">
            <Link href="/settings">
              <Settings className="h-4 w-4 mr-2" />
              Configure Settings
            </Link>
          </Button>
          {readyCount < systemFeatures.length && (
            <Button asChild variant="outline" size="sm">
              <Link href="/docs/setup" target="_blank">
                <ExternalLink className="h-4 w-4 mr-2" />
                Setup Guide
              </Link>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
