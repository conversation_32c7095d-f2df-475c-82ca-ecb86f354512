"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Info,
  AlertTriangle,
  CheckCircle,
  Database,
  Activity,
  Shield,
  Settings,
  Archive,
  Zap
} from "lucide-react";
import { SettingCategory, AppSettings } from "@/lib/settings/types";
import { settingFields } from "@/lib/settings/settings-config";

interface DatabaseSettingsPanelProps {
  category: SettingCategory;
  settings: AppSettings | null;
  pendingChanges?: any;
  onChange: (categorySettings: any) => void;
}

export function DatabaseSettingsPanel({
  category,
  settings,
  pendingChanges,
  onChange
}: DatabaseSettingsPanelProps) {
  const [localSettings, setLocalSettings] = useState<any>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const fields = settingFields[category.id] || [];
  const currentSettings = settings?.[category.id as keyof AppSettings] || {};

  // Initialize local settings
  useEffect(() => {
    if (pendingChanges) {
      setLocalSettings(pendingChanges);
    } else if (currentSettings) {
      setLocalSettings(currentSettings);
    }
  }, [currentSettings, pendingChanges]);

  const handleFieldChange = (key: string, value: any) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    onChange(newSettings);
  };

  const renderField = (field: any) => {
    const value = localSettings[field.key] ?? field.defaultValue;

    return (
      <div key={field.key} className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor={field.key} className="text-sm font-medium">
              {field.name}
            </Label>
            <p className="text-xs text-muted-foreground">
              {field.description}
            </p>
          </div>
          
          {field.type === 'boolean' ? (
            <Switch
              id={field.key}
              checked={value}
              onCheckedChange={(checked) => handleFieldChange(field.key, checked)}
            />
          ) : field.type === 'select' ? (
            <Select
              value={value}
              onValueChange={(newValue) => handleFieldChange(field.key, newValue)}
            >
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input
              id={field.key}
              type={field.type === 'number' ? 'number' : field.key.includes('password') ? 'password' : 'text'}
              value={value}
              onChange={(e) => {
                const newValue = field.type === 'number' ? 
                  Number(e.target.value) : e.target.value;
                handleFieldChange(field.key, newValue);
              }}
              className="w-48"
              min={field.validation?.min}
              max={field.validation?.max}
            />
          )}
        </div>
      </div>
    );
  };

  // Group fields by section
  const connectionFields = fields.filter(f => 
    ['provider', 'host', 'port', 'database'].includes(f.key)
  );
  
  const poolFields = fields.filter(f => 
    ['maxConnections', 'connectionTimeout'].includes(f.key)
  );
  
  const performanceFields = fields.filter(f => 
    ['queryTimeout', 'slowQueryThreshold', 'enableQueryLogging', 'enableSlowQueryLogging', 'cacheEnabled', 'cacheTTL'].includes(f.key)
  );
  
  const backupFields = fields.filter(f => 
    ['backupEnabled', 'backupFrequency', 'backupRetentionDays'].includes(f.key)
  );
  
  const monitoringFields = fields.filter(f => 
    ['performanceMonitoringEnabled', 'alertsEnabled', 'diskSpaceThreshold'].includes(f.key)
  );

  const provider = localSettings.provider || 'sqlite';
  const isProduction = provider !== 'sqlite';

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-primary/10">
            <Database className="h-5 w-5 text-primary" />
          </div>
          <div>
            <CardTitle>{category.name}</CardTitle>
            <CardDescription>{category.description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <span className="text-blue-600">
              📊 Currently using {provider.toUpperCase()} database provider.
              {provider === 'sqlite' && ' (Development mode)'}
            </span>
          </AlertDescription>
        </Alert>

        {/* Connection Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            <h3 className="font-semibold">Connection Settings</h3>
          </div>
          <div className="space-y-4 pl-6">
            {connectionFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Connection Pool Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <h3 className="font-semibold">Connection Pool</h3>
          </div>
          <div className="space-y-4 pl-6">
            {poolFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Performance Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            <h3 className="font-semibold">Performance</h3>
          </div>
          <div className="space-y-4 pl-6">
            {performanceFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Backup Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Archive className="h-4 w-4" />
            <h3 className="font-semibold">Backup & Recovery</h3>
          </div>
          <div className="space-y-4 pl-6">
            {backupFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Monitoring Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            <h3 className="font-semibold">Monitoring & Alerts</h3>
          </div>
          <div className="space-y-4 pl-6">
            {monitoringFields.map(renderField)}
          </div>
        </div>

        {/* Production Warning */}
        {isProduction && (
          <>
            <Separator />
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Production Database Configuration</strong><br />
                You are configuring a production database. Make sure to:
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Use strong authentication credentials</li>
                  <li>Enable SSL/TLS encryption</li>
                  <li>Configure proper backup schedules</li>
                  <li>Set up monitoring and alerts</li>
                </ul>
              </AlertDescription>
            </Alert>
          </>
        )}

        {/* Database Information */}
        <div className="bg-muted/50 rounded-lg p-4">
          <h4 className="font-medium mb-2">Database Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Provider:</span>
              <span className="ml-2 font-medium uppercase">{provider}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Max Connections:</span>
              <span className="ml-2 font-medium">{localSettings.maxConnections || 10}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Query Timeout:</span>
              <span className="ml-2 font-medium">{localSettings.queryTimeout || 30}s</span>
            </div>
            <div>
              <span className="text-muted-foreground">Backup:</span>
              <span className="ml-2 font-medium">
                {localSettings.backupEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Query Cache:</span>
              <span className="ml-2 font-medium">
                {localSettings.cacheEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Monitoring:</span>
              <span className="ml-2 font-medium">
                {localSettings.performanceMonitoringEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
