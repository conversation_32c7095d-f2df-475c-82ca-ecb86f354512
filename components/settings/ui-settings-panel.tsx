"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Info,
  Palette,
  Layout,
  Zap,
  Bell,
  Monitor,
  Accessibility,
  Globe,
  Gauge,
  Code,
  Paintbrush
} from "lucide-react";
import { SettingCategory, AppSettings } from "@/lib/settings/types";
import { settingFields } from "@/lib/settings/settings-config";

interface UISettingsPanelProps {
  category: SettingCategory;
  settings: AppSettings | null;
  pendingChanges?: any;
  onChange: (categorySettings: any) => void;
}

export function UISettingsPanel({
  category,
  settings,
  pendingChanges,
  onChange
}: UISettingsPanelProps) {
  const [localSettings, setLocalSettings] = useState<any>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const fields = settingFields[category.id] || [];
  const currentSettings = settings?.[category.id as keyof AppSettings] || {};

  // Initialize local settings
  useEffect(() => {
    if (pendingChanges) {
      setLocalSettings(pendingChanges);
    } else if (currentSettings) {
      setLocalSettings(currentSettings);
    }
  }, [currentSettings, pendingChanges]);

  const handleFieldChange = (key: string, value: any) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    onChange(newSettings);
  };

  const isFieldVisible = (field: any): boolean => {
    if (!field.dependencies) return true;
    
    return field.dependencies.every((dep: any) => {
      return localSettings[dep.field] === dep.value;
    });
  };

  const renderField = (field: any) => {
    if (!isFieldVisible(field)) return null;

    const value = localSettings[field.key] ?? field.defaultValue;

    return (
      <div key={field.key} className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor={field.key} className="text-sm font-medium">
              {field.name}
            </Label>
            <p className="text-xs text-muted-foreground">
              {field.description}
            </p>
          </div>
          
          {field.type === 'boolean' ? (
            <Switch
              id={field.key}
              checked={value}
              onCheckedChange={(checked) => handleFieldChange(field.key, checked)}
            />
          ) : field.type === 'select' ? (
            <Select
              value={value}
              onValueChange={(newValue) => handleFieldChange(field.key, newValue)}
            >
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : field.key === 'customCSS' ? (
            <Textarea
              id={field.key}
              value={value}
              onChange={(e) => handleFieldChange(field.key, e.target.value)}
              className="w-48 h-20 font-mono text-xs"
              placeholder="/* Custom CSS styles */"
            />
          ) : (
            <Input
              id={field.key}
              type={field.key.includes('Color') ? 'color' : field.type === 'number' ? 'number' : 'text'}
              value={value}
              onChange={(e) => {
                const newValue = field.type === 'number' ? 
                  Number(e.target.value) : e.target.value;
                handleFieldChange(field.key, newValue);
              }}
              className={field.key.includes('Color') ? "w-20 h-10" : "w-48"}
              min={field.validation?.min}
              max={field.validation?.max}
              placeholder={field.key.includes('Url') ? 'https://example.com/image.png' : ''}
            />
          )}
        </div>
      </div>
    );
  };

  // Group fields by section
  const themeFields = fields.filter(f => 
    ['darkModeEnabled', 'themeMode', 'colorScheme', 'accentColor', 'borderRadius', 'fontFamily', 'fontSize'].includes(f.key)
  );
  
  const layoutFields = fields.filter(f => 
    ['compactModeEnabled', 'sidebarCollapsed', 'sidebarPosition', 'navigationStyle', 'breadcrumbsEnabled', 'pageTransitions'].includes(f.key)
  );
  
  const animationFields = fields.filter(f => 
    ['animationsEnabled', 'animationSpeed', 'reducedMotion', 'hoverEffects', 'loadingAnimations'].includes(f.key)
  );
  
  const notificationFields = fields.filter(f => 
    ['notificationsEnabled', 'notificationPosition', 'notificationDuration', 'soundEnabled', 'hapticFeedback'].includes(f.key)
  );
  
  const contentFields = fields.filter(f => 
    ['density', 'showTooltips', 'showKeyboardShortcuts', 'autoSaveEnabled', 'confirmDialogs'].includes(f.key)
  );
  
  const accessibilityFields = fields.filter(f => 
    ['highContrastMode', 'focusIndicators', 'screenReaderOptimized', 'keyboardNavigationEnabled'].includes(f.key)
  );
  
  const localizationFields = fields.filter(f => 
    ['language', 'dateFormat', 'timeFormat', 'timezone', 'currency', 'numberFormat'].includes(f.key)
  );
  
  const performanceFields = fields.filter(f => 
    ['lazyLoadingEnabled', 'imageOptimization', 'cacheEnabled', 'offlineMode'].includes(f.key)
  );
  
  const developerFields = fields.filter(f => 
    ['debugMode', 'showPerformanceMetrics', 'enableDevTools'].includes(f.key)
  );
  
  const customizationFields = fields.filter(f => 
    ['customCSS', 'logoUrl', 'faviconUrl'].includes(f.key)
  );

  const themeMode = localSettings.themeMode || 'system';
  const colorScheme = localSettings.colorScheme || 'default';

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-primary/10">
            <Palette className="h-5 w-5 text-primary" />
          </div>
          <div>
            <CardTitle>{category.name}</CardTitle>
            <CardDescription>{category.description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <span className="text-blue-600">
              🎨 Current theme: {themeMode} mode with {colorScheme} color scheme.
            </span>
          </AlertDescription>
        </Alert>

        {/* Theme & Appearance */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <h3 className="font-semibold">Theme & Appearance</h3>
          </div>
          <div className="space-y-4 pl-6">
            {themeFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Layout & Navigation */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Layout className="h-4 w-4" />
            <h3 className="font-semibold">Layout & Navigation</h3>
          </div>
          <div className="space-y-4 pl-6">
            {layoutFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Animations & Effects */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            <h3 className="font-semibold">Animations & Effects</h3>
          </div>
          <div className="space-y-4 pl-6">
            {animationFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Notifications & Feedback */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            <h3 className="font-semibold">Notifications & Feedback</h3>
          </div>
          <div className="space-y-4 pl-6">
            {notificationFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Content & Display */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Monitor className="h-4 w-4" />
            <h3 className="font-semibold">Content & Display</h3>
          </div>
          <div className="space-y-4 pl-6">
            {contentFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Accessibility */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Accessibility className="h-4 w-4" />
            <h3 className="font-semibold">Accessibility</h3>
          </div>
          <div className="space-y-4 pl-6">
            {accessibilityFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Localization */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            <h3 className="font-semibold">Localization</h3>
          </div>
          <div className="space-y-4 pl-6">
            {localizationFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Performance & Data */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Gauge className="h-4 w-4" />
            <h3 className="font-semibold">Performance & Data</h3>
          </div>
          <div className="space-y-4 pl-6">
            {performanceFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Developer & Debug */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            <h3 className="font-semibold">Developer & Debug</h3>
          </div>
          <div className="space-y-4 pl-6">
            {developerFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Customization */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Paintbrush className="h-4 w-4" />
            <h3 className="font-semibold">Customization</h3>
          </div>
          <div className="space-y-4 pl-6">
            {customizationFields.map(renderField)}
          </div>
        </div>

        {/* UI Information */}
        <div className="bg-muted/50 rounded-lg p-4">
          <h4 className="font-medium mb-2">Interface Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Theme Mode:</span>
              <span className="ml-2 font-medium capitalize">{themeMode}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Color Scheme:</span>
              <span className="ml-2 font-medium capitalize">{colorScheme}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Font Size:</span>
              <span className="ml-2 font-medium capitalize">{localSettings.fontSize || 'medium'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Density:</span>
              <span className="ml-2 font-medium capitalize">{localSettings.density || 'comfortable'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Animations:</span>
              <span className="ml-2 font-medium">
                {localSettings.animationsEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Language:</span>
              <span className="ml-2 font-medium uppercase">{localSettings.language || 'en'}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
