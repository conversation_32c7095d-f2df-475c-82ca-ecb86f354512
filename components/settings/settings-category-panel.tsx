"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Info,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  HelpCircle
} from "lucide-react";
import { SettingCategory, AppSettings } from "@/lib/settings/types";
import { settingFields, getCategoryIcon } from "@/lib/settings/settings-config";
import { Button } from "@/components/ui/button";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface SettingsCategoryPanelProps {
  category: SettingCategory;
  settings: AppSettings | null;
  pendingChanges?: any;
  onChange: (categorySettings: any) => void;
}

export function SettingsCategoryPanel({
  category,
  settings,
  pendingChanges,
  onChange
}: SettingsCategoryPanelProps) {
  const [localSettings, setLocalSettings] = useState<any>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const IconComponent = getCategoryIcon(category.id);
  const fields = settingFields[category.id] || [];
  const currentSettings = settings?.[category.id as keyof AppSettings] || {};

  // Initialize local settings
  useEffect(() => {
    if (pendingChanges) {
      setLocalSettings(pendingChanges);
    } else if (currentSettings) {
      setLocalSettings(currentSettings);
    }
  }, [currentSettings, pendingChanges]);

  const handleFieldChange = (fieldKey: string, value: any) => {
    const newSettings = {
      ...localSettings,
      [fieldKey]: value
    };

    // Validate the field
    const field = fields.find(f => f.key === fieldKey);
    if (field?.validation) {
      const error = validateField(field, value);
      setValidationErrors(prev => ({
        ...prev,
        [fieldKey]: error || ''
      }));
    }

    setLocalSettings(newSettings);
    onChange(newSettings);
  };

  const validateField = (field: any, value: any): string | null => {
    if (field.validation?.required && (!value || value === '')) {
      return `${field.name} is required`;
    }

    if (field.type === 'number' && field.validation) {
      const numValue = parseFloat(value);
      if (field.validation.min !== undefined && numValue < field.validation.min) {
        return `${field.name} must be at least ${field.validation.min}`;
      }
      if (field.validation.max !== undefined && numValue > field.validation.max) {
        return `${field.name} must be at most ${field.validation.max}`;
      }
    }

    if ((field.type === 'string' || field.type === 'password') && field.validation) {
      if (field.validation.minLength && value && value.length < field.validation.minLength) {
        return `${field.name} must be at least ${field.validation.minLength} characters`;
      }

      if (field.validation.pattern && value && !field.validation.pattern.test(value)) {
        return field.validation.patternMessage || `${field.name} format is invalid`;
      }
    }

    return null;
  };

  const isFieldEnabled = (field: any): boolean => {
    if (!field.dependencies) return true;

    return field.dependencies.every((dep: any) => {
      const depValue = localSettings[dep.field];
      return depValue === dep.value;
    });
  };

  const getFieldStatus = (fieldKey: string) => {
    const currentValue = (currentSettings as Record<string, any>)[fieldKey];
    const pendingValue = localSettings[fieldKey];

    if (pendingValue !== currentValue) {
      return 'changed';
    }

    if (fieldKey === 'enabled' && pendingValue) {
      return 'enabled';
    }

    return 'default';
  };

  const renderField = (field: any) => {
    const fieldValue = localSettings[field.key] ?? field.defaultValue;
    const isEnabled = isFieldEnabled(field);
    const hasError = validationErrors[field.key];
    const status = getFieldStatus(field.key);

    return (
      <div key={field.key} className="space-y-3 p-4 border rounded-lg bg-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label
              htmlFor={field.key}
              className={`text-sm font-medium ${!isEnabled ? 'text-muted-foreground' : ''}`}
            >
              {field.name}
            </Label>
            {status === 'changed' && (
              <Badge variant="secondary" className="text-xs">
                Modified
              </Badge>
            )}
            {status === 'enabled' && field.key === 'enabled' && (
              <Badge variant="default" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Enabled
              </Badge>
            )}
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">{field.description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {field.type === 'boolean' && (
          <div className="flex items-center space-x-2">
            <Switch
              id={field.key}
              checked={fieldValue}
              onCheckedChange={(checked) => handleFieldChange(field.key, checked)}
              disabled={!isEnabled}
            />
            <Label
              htmlFor={field.key}
              className={`text-sm ${!isEnabled ? 'text-muted-foreground' : ''}`}
            >
              {fieldValue ? 'Enabled' : 'Disabled'}
            </Label>
          </div>
        )}

        {field.type === 'string' && (
          <Input
            id={field.key}
            value={fieldValue || ''}
            onChange={(e) => handleFieldChange(field.key, e.target.value)}
            disabled={!isEnabled}
            placeholder={field.placeholder || field.defaultValue}
            className={hasError ? 'border-destructive' : ''}
          />
        )}

        {field.type === 'password' && (
          <Input
            id={field.key}
            type="password"
            value={fieldValue || ''}
            onChange={(e) => handleFieldChange(field.key, e.target.value)}
            disabled={!isEnabled}
            placeholder={field.placeholder || '••••••••'}
            className={hasError ? 'border-destructive' : ''}
          />
        )}

        {field.type === 'number' && (
          <Input
            id={field.key}
            type="number"
            value={fieldValue || ''}
            onChange={(e) => handleFieldChange(field.key, parseFloat(e.target.value) || 0)}
            disabled={!isEnabled}
            placeholder={String(field.defaultValue)}
            min={field.validation?.min}
            max={field.validation?.max}
            className={hasError ? 'border-destructive' : ''}
          />
        )}

        {field.type === 'select' && (
          <Select
            value={fieldValue || field.defaultValue}
            onValueChange={(value) => handleFieldChange(field.key, value)}
            disabled={!isEnabled}
          >
            <SelectTrigger className={hasError ? 'border-destructive' : ''}>
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {hasError && (
          <p className="text-sm text-destructive flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            {hasError}
          </p>
        )}

        {!isEnabled && (
          <p className="text-xs text-muted-foreground italic">
            This setting is disabled because required dependencies are not met.
          </p>
        )}
      </div>
    );
  };

  const getCategoryStatusInfo = () => {
    const isEnabled = localSettings.enabled;
    const hasRequiredConfig = category.id === 'payments' ?
      localSettings.provider !== 'disabled' : true;

    if (!isEnabled) {
      return {
        type: 'warning',
        message: `${category.name} is currently disabled. Enable it to use these features.`
      };
    }

    if (!hasRequiredConfig) {
      return {
        type: 'error',
        message: 'Additional configuration required before this feature can be used.'
      };
    }

    return {
      type: 'success',
      message: `${category.name} is enabled and configured.`
    };
  };

  const statusInfo = getCategoryStatusInfo();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-lg bg-primary/10">
            <IconComponent className="h-6 w-6 text-primary" />
          </div>
          <div>
            <CardTitle className="text-xl">{category.name}</CardTitle>
            <CardDescription className="mt-1">
              {category.description}
            </CardDescription>
          </div>
        </div>

        {/* Status Alert */}
        <Alert className={`mt-4 ${
          statusInfo.type === 'error' ? 'border-destructive bg-destructive/5' :
          statusInfo.type === 'warning' ? 'border-orange-500 bg-orange-50 dark:bg-orange-950/20' :
          'border-green-500 bg-green-50 dark:bg-green-950/20'
        }`}>
          {statusInfo.type === 'error' && <AlertTriangle className="h-4 w-4 text-destructive" />}
          {statusInfo.type === 'warning' && <Info className="h-4 w-4 text-orange-600" />}
          {statusInfo.type === 'success' && <CheckCircle className="h-4 w-4 text-green-600" />}
          <AlertDescription className={
            statusInfo.type === 'error' ? 'text-destructive' :
            statusInfo.type === 'warning' ? 'text-orange-700 dark:text-orange-300' :
            'text-green-700 dark:text-green-300'
          }>
            {statusInfo.message}
          </AlertDescription>
        </Alert>
      </CardHeader>

      <CardContent className="space-y-4">
        {fields.length > 0 ? (
          fields.map((field) => renderField(field))
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Info className="h-8 w-8 mx-auto mb-2" />
            <p>No configurable settings available for this category.</p>
          </div>
        )}

        {/* Category-specific help */}
        {category.id === 'payments' && (
          <>
            <Alert>
              <ExternalLink className="h-4 w-4" />
              <AlertDescription>
                <strong>Doku Payment Setup:</strong> You'll need to configure your Doku payment gateway
                with Client ID and Secret Key from your Doku dashboard.
                <div className="mt-2 space-y-1">
                  <Button variant="link" className="p-0 h-auto" asChild>
                    <a href="https://sandbox.doku.com/bo/login" target="_blank">
                      Sandbox Dashboard
                    </a>
                  </Button>
                  <span className="mx-2">•</span>
                  <Button variant="link" className="p-0 h-auto" asChild>
                    <a href="https://dashboard.doku.com/bo/login" target="_blank">
                      Production Dashboard
                    </a>
                  </Button>
                </div>
              </AlertDescription>
            </Alert>

            {/* Configuration Status */}
            {localSettings.provider === 'doku' && (
              <div className="p-4 border rounded-lg bg-muted/50">
                <h4 className="font-medium mb-3">Configuration Status</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>Client ID:</span>
                    <span className={localSettings.dokuClientId ? 'text-green-600' : 'text-orange-600'}>
                      {localSettings.dokuClientId ? '✓ Configured' : '⚠ Not set'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Secret Key:</span>
                    <span className={localSettings.dokuSecretKey ? 'text-green-600' : 'text-orange-600'}>
                      {localSettings.dokuSecretKey ? '✓ Configured' : '⚠ Not set'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Environment:</span>
                    <span className="text-blue-600">
                      {localSettings.dokuEnvironment === 'sandbox' ? '🧪 Sandbox' : '🚀 Production'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Webhook URL:</span>
                    <span className={localSettings.dokuNotificationUrl ? 'text-green-600' : 'text-orange-600'}>
                      {localSettings.dokuNotificationUrl ? '✓ Configured' : '⚠ Not set'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
