"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Info,
  AlertTriangle,
  CheckCircle,
  Settings,
  Zap,
  Clock,
  Shield,
  Database,
  Activity
} from "lucide-react";
import { SettingCategory, AppSettings } from "@/lib/settings/types";
import { settingFields } from "@/lib/settings/settings-config";

interface WorkflowSettingsPanelProps {
  category: SettingCategory;
  settings: AppSettings | null;
  pendingChanges?: any;
  onChange: (categorySettings: any) => void;
}

export function WorkflowSettingsPanel({
  category,
  settings,
  pendingChanges,
  onChange
}: WorkflowSettingsPanelProps) {
  const [localSettings, setLocalSettings] = useState<any>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const fields = settingFields[category.id] || [];
  const currentSettings = settings?.[category.id as keyof AppSettings] || {};

  // Initialize local settings
  useEffect(() => {
    if (pendingChanges) {
      setLocalSettings(pendingChanges);
    } else if (currentSettings) {
      setLocalSettings(currentSettings);
    }
  }, [currentSettings, pendingChanges]);

  const handleFieldChange = (key: string, value: any) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    onChange(newSettings);
  };

  const validateField = (field: any, value: any): string | null => {
    if (!field.validation) return null;

    const validation = field.validation;
    
    if (validation.required && (!value || value === '')) {
      return `${field.name} is required`;
    }

    if (field.type === 'number') {
      const numValue = Number(value);
      if (validation.min !== undefined && numValue < validation.min) {
        return `${field.name} must be at least ${validation.min}`;
      }
      if (validation.max !== undefined && numValue > validation.max) {
        return `${field.name} must be at most ${validation.max}`;
      }
    }

    return null;
  };

  const isFieldVisible = (field: any): boolean => {
    if (!field.dependencies) return true;
    
    return field.dependencies.every((dep: any) => {
      return localSettings[dep.field] === dep.value;
    });
  };

  const renderField = (field: any) => {
    if (!isFieldVisible(field)) return null;

    const value = localSettings[field.key] ?? field.defaultValue;
    const error = validationErrors[field.key];

    return (
      <div key={field.key} className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor={field.key} className="text-sm font-medium">
              {field.name}
            </Label>
            <p className="text-xs text-muted-foreground">
              {field.description}
            </p>
          </div>
          
          {field.type === 'boolean' ? (
            <Switch
              id={field.key}
              checked={value}
              onCheckedChange={(checked) => handleFieldChange(field.key, checked)}
            />
          ) : field.type === 'select' ? (
            <Select
              value={value}
              onValueChange={(newValue) => handleFieldChange(field.key, newValue)}
            >
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input
              id={field.key}
              type={field.type === 'number' ? 'number' : 'text'}
              value={value}
              onChange={(e) => {
                const newValue = field.type === 'number' ? 
                  Number(e.target.value) : e.target.value;
                handleFieldChange(field.key, newValue);
              }}
              className="w-48"
              min={field.validation?.min}
              max={field.validation?.max}
            />
          )}
        </div>
        
        {error && (
          <p className="text-xs text-destructive flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            {error}
          </p>
        )}
      </div>
    );
  };

  // Group fields by section
  const basicFields = fields.filter(f => 
    ['enabled', 'maxWorkflowsPerUser', 'maxNodesPerWorkflow', 'sharingEnabled', 'exportEnabled', 'importEnabled'].includes(f.key)
  );
  
  const executionFields = fields.filter(f => 
    ['executionTimeoutMinutes', 'maxConcurrentExecutions', 'defaultExecutionMode', 'retryAttempts', 'continueOnError', 'debugModeEnabled'].includes(f.key)
  );
  
  const schedulingFields = fields.filter(f => 
    ['schedulingEnabled', 'maxScheduledWorkflows', 'schedulingIntervalMinutes'].includes(f.key)
  );
  
  const performanceFields = fields.filter(f => 
    ['maxConcurrentNodes', 'nodeExecutionTimeoutSeconds', 'memoryLimitMB', 'cpuLimitPercent'].includes(f.key)
  );
  
  const loggingFields = fields.filter(f => 
    ['executionHistoryRetentionDays', 'logLevel', 'maxLogSizeMB', 'enableExecutionMetrics'].includes(f.key)
  );
  
  const securityFields = fields.filter(f => 
    ['sandboxEnabled', 'allowExternalConnections', 'allowFileSystemAccess', 'allowNetworkAccess'].includes(f.key)
  );
  
  const advancedFields = fields.filter(f => 
    ['webhooksEnabled', 'apiIntegrationEnabled', 'customNodeUploadEnabled', 'workflowTemplatesEnabled'].includes(f.key)
  );

  const isEnabled = localSettings.enabled;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-primary/10">
            <Settings className="h-5 w-5 text-primary" />
          </div>
          <div>
            <CardTitle>{category.name}</CardTitle>
            <CardDescription>{category.description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {isEnabled ? (
              <span className="text-green-600">
                ✅ Workflow Engine is enabled and ready for use.
              </span>
            ) : (
              <span className="text-amber-600">
                ⚠️ Workflow Engine is disabled. Enable it to use workflow features.
              </span>
            )}
          </AlertDescription>
        </Alert>

        {/* Basic Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <h3 className="font-semibold">Basic Settings</h3>
          </div>
          <div className="space-y-4 pl-6">
            {basicFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Execution Engine Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            <h3 className="font-semibold">Execution Engine</h3>
          </div>
          <div className="space-y-4 pl-6">
            {executionFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Scheduling Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <h3 className="font-semibold">Scheduling</h3>
          </div>
          <div className="space-y-4 pl-6">
            {schedulingFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Performance Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            <h3 className="font-semibold">Performance</h3>
          </div>
          <div className="space-y-4 pl-6">
            {performanceFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Storage & Logging */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            <h3 className="font-semibold">Storage & Logging</h3>
          </div>
          <div className="space-y-4 pl-6">
            {loggingFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Security Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <h3 className="font-semibold">Security</h3>
          </div>
          <div className="space-y-4 pl-6">
            {securityFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Advanced Features */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <h3 className="font-semibold">Advanced Features</h3>
          </div>
          <div className="space-y-4 pl-6">
            {advancedFields.map(renderField)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
