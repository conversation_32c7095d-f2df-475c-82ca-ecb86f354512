"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Info,
  AlertTriangle,
  CheckCircle,
  HardDrive,
  Cloud,
  Shield,
  Settings,
  Zap,
  Archive
} from "lucide-react";
import { SettingCategory, AppSettings } from "@/lib/settings/types";
import { settingFields } from "@/lib/settings/settings-config";

interface StorageSettingsPanelProps {
  category: SettingCategory;
  settings: AppSettings | null;
  pendingChanges?: any;
  onChange: (categorySettings: any) => void;
}

export function StorageSettingsPanel({
  category,
  settings,
  pendingChanges,
  onChange
}: StorageSettingsPanelProps) {
  const [localSettings, setLocalSettings] = useState<any>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const fields = settingFields[category.id] || [];
  const currentSettings = settings?.[category.id as keyof AppSettings] || {};

  // Initialize local settings
  useEffect(() => {
    if (pendingChanges) {
      setLocalSettings(pendingChanges);
    } else if (currentSettings) {
      setLocalSettings(currentSettings);
    }
  }, [currentSettings, pendingChanges]);

  const handleFieldChange = (key: string, value: any) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    onChange(newSettings);
  };

  const renderField = (field: any) => {
    const value = localSettings[field.key] ?? field.defaultValue;

    return (
      <div key={field.key} className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor={field.key} className="text-sm font-medium">
              {field.name}
            </Label>
            <p className="text-xs text-muted-foreground">
              {field.description}
            </p>
          </div>
          
          {field.type === 'boolean' ? (
            <Switch
              id={field.key}
              checked={value}
              onCheckedChange={(checked) => handleFieldChange(field.key, checked)}
            />
          ) : field.type === 'select' ? (
            <Select
              value={value}
              onValueChange={(newValue) => handleFieldChange(field.key, newValue)}
            >
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input
              id={field.key}
              type={field.type === 'number' ? 'number' : 'text'}
              value={value}
              onChange={(e) => {
                const newValue = field.type === 'number' ? 
                  Number(e.target.value) : e.target.value;
                handleFieldChange(field.key, newValue);
              }}
              className="w-48"
              min={field.validation?.min}
              max={field.validation?.max}
            />
          )}
        </div>
      </div>
    );
  };

  // Group fields by section
  const basicFields = fields.filter(f => 
    ['enabled', 'provider', 'maxFileSize', 'maxTotalStorage'].includes(f.key)
  );
  
  const optimizationFields = fields.filter(f => 
    ['compressionEnabled', 'compressionQuality', 'autoOptimizeImages', 'generateThumbnails'].includes(f.key)
  );
  
  const backupFields = fields.filter(f => 
    ['backupEnabled', 'backupFrequency', 'retentionDays'].includes(f.key)
  );
  
  const securityFields = fields.filter(f => 
    ['encryptionEnabled', 'accessLoggingEnabled', 'publicAccessEnabled'].includes(f.key)
  );

  const isEnabled = localSettings.enabled;
  const provider = localSettings.provider || 'local';

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-primary/10">
            <HardDrive className="h-5 w-5 text-primary" />
          </div>
          <div>
            <CardTitle>{category.name}</CardTitle>
            <CardDescription>{category.description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {isEnabled ? (
              <span className="text-green-600">
                ✅ File Storage is enabled using {provider.toUpperCase()} provider.
              </span>
            ) : (
              <span className="text-amber-600">
                ⚠️ File Storage is disabled. Enable it to allow file uploads.
              </span>
            )}
          </AlertDescription>
        </Alert>

        {/* Basic Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <h3 className="font-semibold">Basic Configuration</h3>
          </div>
          <div className="space-y-4 pl-6">
            {basicFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Storage Optimization */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            <h3 className="font-semibold">Storage Optimization</h3>
          </div>
          <div className="space-y-4 pl-6">
            {optimizationFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Backup & Retention */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Archive className="h-4 w-4" />
            <h3 className="font-semibold">Backup & Retention</h3>
          </div>
          <div className="space-y-4 pl-6">
            {backupFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Security Settings */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <h3 className="font-semibold">Security</h3>
          </div>
          <div className="space-y-4 pl-6">
            {securityFields.map(renderField)}
          </div>
        </div>

        {/* Provider-specific Settings */}
        {provider !== 'local' && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Cloud className="h-4 w-4" />
                <h3 className="font-semibold">{provider.toUpperCase()} Configuration</h3>
                <Badge variant="secondary">{provider}</Badge>
              </div>
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Configure your {provider.toUpperCase()} credentials and settings below. 
                  These settings are required for cloud storage to work properly.
                </AlertDescription>
              </Alert>
              <div className="text-sm text-muted-foreground">
                Cloud provider configuration will be available in the next update.
              </div>
            </div>
          </>
        )}

        {/* Storage Usage Info */}
        <div className="bg-muted/50 rounded-lg p-4">
          <h4 className="font-medium mb-2">Storage Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Max File Size:</span>
              <span className="ml-2 font-medium">{localSettings.maxFileSize || 50} MB</span>
            </div>
            <div>
              <span className="text-muted-foreground">Max Total Storage:</span>
              <span className="ml-2 font-medium">{localSettings.maxTotalStorage || 10} GB</span>
            </div>
            <div>
              <span className="text-muted-foreground">Provider:</span>
              <span className="ml-2 font-medium capitalize">{provider}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Compression:</span>
              <span className="ml-2 font-medium">
                {localSettings.compressionEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
