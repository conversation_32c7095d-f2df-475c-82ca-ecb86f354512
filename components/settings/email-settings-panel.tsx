"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Info,
  AlertTriangle,
  CheckCircle,
  Mail,
  Settings,
  Shield,
  Activity,
  Clock,
  Zap,
  FileText,
  TestTube
} from "lucide-react";
import { SettingCategory, AppSettings } from "@/lib/settings/types";
import { settingFields } from "@/lib/settings/settings-config";

interface EmailSettingsPanelProps {
  category: SettingCategory;
  settings: AppSettings | null;
  pendingChanges?: any;
  onChange: (categorySettings: any) => void;
}

export function EmailSettingsPanel({
  category,
  settings,
  pendingChanges,
  onChange
}: EmailSettingsPanelProps) {
  const [localSettings, setLocalSettings] = useState<any>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const fields = settingFields[category.id] || [];
  const currentSettings = settings?.[category.id as keyof AppSettings] || {};

  // Initialize local settings
  useEffect(() => {
    if (pendingChanges) {
      setLocalSettings(pendingChanges);
    } else if (currentSettings) {
      setLocalSettings(currentSettings);
    }
  }, [currentSettings, pendingChanges]);

  const handleFieldChange = (key: string, value: any) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    onChange(newSettings);
  };

  const isFieldVisible = (field: any): boolean => {
    if (!field.dependencies) return true;
    
    return field.dependencies.every((dep: any) => {
      return localSettings[dep.field] === dep.value;
    });
  };

  const renderField = (field: any) => {
    if (!isFieldVisible(field)) return null;

    const value = localSettings[field.key] ?? field.defaultValue;

    return (
      <div key={field.key} className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor={field.key} className="text-sm font-medium">
              {field.name}
            </Label>
            <p className="text-xs text-muted-foreground">
              {field.description}
            </p>
          </div>
          
          {field.type === 'boolean' ? (
            <Switch
              id={field.key}
              checked={value}
              onCheckedChange={(checked) => handleFieldChange(field.key, checked)}
            />
          ) : field.type === 'select' ? (
            <Select
              value={value}
              onValueChange={(newValue) => handleFieldChange(field.key, newValue)}
            >
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input
              id={field.key}
              type={field.key.includes('password') || field.key.includes('key') || field.key.includes('secret') ? 'password' : field.type === 'number' ? 'number' : 'text'}
              value={value}
              onChange={(e) => {
                const newValue = field.type === 'number' ? 
                  Number(e.target.value) : e.target.value;
                handleFieldChange(field.key, newValue);
              }}
              className="w-48"
              min={field.validation?.min}
              max={field.validation?.max}
              placeholder={field.key.includes('email') ? '<EMAIL>' : ''}
            />
          )}
        </div>
      </div>
    );
  };

  // Group fields by section
  const basicFields = fields.filter(f => 
    ['enabled', 'provider', 'fromEmail', 'fromName', 'replyToEmail'].includes(f.key)
  );
  
  const featureFields = fields.filter(f => 
    ['verificationEnabled', 'notificationsEnabled', 'marketingEmailsEnabled', 'transactionalEmailsEnabled'].includes(f.key)
  );
  
  const smtpFields = fields.filter(f => 
    ['smtpHost', 'smtpPort', 'smtpSecure', 'smtpUsername', 'smtpPassword'].includes(f.key)
  );
  
  const sendgridFields = fields.filter(f => 
    ['sendgridApiKey', 'sendgridWebhookEnabled'].includes(f.key)
  );
  
  const mailgunFields = fields.filter(f => 
    ['mailgunApiKey', 'mailgunDomain', 'mailgunRegion'].includes(f.key)
  );
  
  const sesFields = fields.filter(f => 
    ['sesAccessKeyId', 'sesSecretAccessKey', 'sesRegion'].includes(f.key)
  );
  
  const rateLimitFields = fields.filter(f => 
    ['rateLimitEnabled', 'maxEmailsPerHour', 'maxEmailsPerDay'].includes(f.key)
  );
  
  const trackingFields = fields.filter(f => 
    ['trackOpens', 'trackClicks', 'trackUnsubscribes'].includes(f.key)
  );
  
  const queueFields = fields.filter(f => 
    ['queueEnabled', 'queueMaxSize', 'queueProcessingInterval'].includes(f.key)
  );
  
  const templateFields = fields.filter(f => 
    ['welcomeEmailEnabled', 'passwordResetEmailEnabled', 'emailVerificationEnabled'].includes(f.key)
  );
  
  const testingFields = fields.filter(f => 
    ['testModeEnabled', 'testEmailAddress', 'logEmailsEnabled'].includes(f.key)
  );

  const isEnabled = localSettings.enabled;
  const provider = localSettings.provider || 'disabled';

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-primary/10">
            <Mail className="h-5 w-5 text-primary" />
          </div>
          <div>
            <CardTitle>{category.name}</CardTitle>
            <CardDescription>{category.description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {isEnabled ? (
              <span className="text-green-600">
                ✅ Email System is enabled using {provider.toUpperCase()} provider.
              </span>
            ) : (
              <span className="text-amber-600">
                ⚠️ Email System is disabled. Enable it to send emails.
              </span>
            )}
          </AlertDescription>
        </Alert>

        {/* Basic Configuration */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <h3 className="font-semibold">Basic Configuration</h3>
          </div>
          <div className="space-y-4 pl-6">
            {basicFields.map(renderField)}
          </div>
        </div>

        <Separator />

        {/* Email Features */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            <h3 className="font-semibold">Email Features</h3>
          </div>
          <div className="space-y-4 pl-6">
            {featureFields.map(renderField)}
          </div>
        </div>

        {/* Provider-specific Settings */}
        {provider === 'smtp' && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <h3 className="font-semibold">SMTP Configuration</h3>
                <Badge variant="secondary">SMTP</Badge>
              </div>
              <div className="space-y-4 pl-6">
                {smtpFields.map(renderField)}
              </div>
            </div>
          </>
        )}

        {provider === 'sendgrid' && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <h3 className="font-semibold">SendGrid Configuration</h3>
                <Badge variant="secondary">SendGrid</Badge>
              </div>
              <div className="space-y-4 pl-6">
                {sendgridFields.map(renderField)}
              </div>
            </div>
          </>
        )}

        {provider === 'mailgun' && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <h3 className="font-semibold">Mailgun Configuration</h3>
                <Badge variant="secondary">Mailgun</Badge>
              </div>
              <div className="space-y-4 pl-6">
                {mailgunFields.map(renderField)}
              </div>
            </div>
          </>
        )}

        {provider === 'ses' && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <h3 className="font-semibold">Amazon SES Configuration</h3>
                <Badge variant="secondary">AWS SES</Badge>
              </div>
              <div className="space-y-4 pl-6">
                {sesFields.map(renderField)}
              </div>
            </div>
          </>
        )}

        {isEnabled && (
          <>
            <Separator />

            {/* Rate Limiting */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <h3 className="font-semibold">Rate Limiting</h3>
              </div>
              <div className="space-y-4 pl-6">
                {rateLimitFields.map(renderField)}
              </div>
            </div>

            <Separator />

            {/* Email Tracking */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                <h3 className="font-semibold">Email Tracking</h3>
              </div>
              <div className="space-y-4 pl-6">
                {trackingFields.map(renderField)}
              </div>
            </div>

            <Separator />

            {/* Email Queue */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <h3 className="font-semibold">Email Queue</h3>
              </div>
              <div className="space-y-4 pl-6">
                {queueFields.map(renderField)}
              </div>
            </div>

            <Separator />

            {/* Email Templates */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <h3 className="font-semibold">Email Templates</h3>
              </div>
              <div className="space-y-4 pl-6">
                {templateFields.map(renderField)}
              </div>
            </div>

            <Separator />

            {/* Testing & Development */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <TestTube className="h-4 w-4" />
                <h3 className="font-semibold">Testing & Development</h3>
              </div>
              <div className="space-y-4 pl-6">
                {testingFields.map(renderField)}
              </div>
            </div>
          </>
        )}

        {/* Email System Information */}
        <div className="bg-muted/50 rounded-lg p-4">
          <h4 className="font-medium mb-2">Email System Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Provider:</span>
              <span className="ml-2 font-medium uppercase">{provider}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Status:</span>
              <span className="ml-2 font-medium">
                {isEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Rate Limit:</span>
              <span className="ml-2 font-medium">
                {localSettings.maxEmailsPerHour || 100}/hour
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Queue:</span>
              <span className="ml-2 font-medium">
                {localSettings.queueEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Tracking:</span>
              <span className="ml-2 font-medium">
                {localSettings.trackOpens ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Test Mode:</span>
              <span className="ml-2 font-medium">
                {localSettings.testModeEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
