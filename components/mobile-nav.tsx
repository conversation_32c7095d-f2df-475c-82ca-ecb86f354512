'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { MenuIcon, ShieldIcon, HomeIcon, UserIcon, SettingsIcon, Network } from 'lucide-react';
import { useState } from 'react';
import Link from 'next/link';
import { useSession, signOut } from 'next-auth/react';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';

export function MobileNav() {
  const [open, setOpen] = useState(false);
  const { data: session, status } = useSession();
  const pathname = usePathname();

  const isAuthenticated = status === 'authenticated';

  const routes = [
    {
      href: '/',
      label: 'Overview',
      active: pathname === '/',
      icon: HomeIcon,
    },
    {
      href: '/profile',
      label: 'Profile',
      active: pathname === '/profile',
      requiresAuth: true,
      icon: UserIcon,
    },
    {
      href: '/workflow-manager',
      label: 'Workflows',
      active: pathname === '/workflow-manager' || (pathname && pathname.startsWith('/workflow-canvas')),
      requiresAuth: true,
      icon: Network,
    },
  ];

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <MenuIcon className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="pr-0">
        <div className="px-7 mt-2">
          <Link
            href="/"
            className="flex items-center gap-2 font-sans"
            onClick={() => setOpen(false)}
          >
            <ShieldIcon className="h-5 w-5 flex-shrink-0" />
            <span className="font-semibold text-lg font-sans">Auth Demo</span>
          </Link>
        </div>
        <div className="mt-8 flex flex-col gap-4">
          {routes.map((route) => {
            if (route.requiresAuth && !isAuthenticated) return null;

            const Icon = route.icon;

            return (
              <Link
                key={route.href}
                href={route.href}
                onClick={() => setOpen(false)}
                className={cn(
                  "px-7 py-2 text-sm transition-colors hover:text-foreground/80 flex items-center gap-3",
                  route.active ? "text-foreground font-medium" : "text-foreground/60"
                )}
              >
                {Icon && <Icon className="h-4 w-4" />}
                {route.label}
              </Link>
            );
          })}

          {!isAuthenticated && (
            <>
              <Link
                href="/login"
                onClick={() => setOpen(false)}
                className="px-7 py-2 text-sm transition-colors hover:text-foreground/80 flex items-center gap-3 text-foreground/60"
              >
                <UserIcon className="h-4 w-4" />
                Login
              </Link>
              <Link
                href="/register"
                onClick={() => setOpen(false)}
                className="px-7 py-2 text-sm transition-colors hover:text-foreground/80 flex items-center gap-3 text-foreground/60"
              >
                <UserIcon className="h-4 w-4" />
                Register
              </Link>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
