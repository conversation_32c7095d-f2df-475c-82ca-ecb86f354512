"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Upload, X, FileCode, Image, Package, AlertCircle, CheckCircle } from "lucide-react";
import { NodeCategory, NodeTier } from "@/lib/marketplace/types";

const nodeUploadSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  version: z.string().regex(/^\d+\.\d+\.\d+$/, "Version must be in format x.y.z"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  longDescription: z.string().optional(),
  category: z.nativeEnum(NodeCategory),
  tier: z.nativeEnum(NodeTier),
  price: z.number().min(0).optional(),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
  repositoryUrl: z.string().url().optional().or(z.literal("")),
  documentationUrl: z.string().url().optional().or(z.literal("")),
});

type NodeUploadFormData = z.infer<typeof nodeUploadSchema>;

export function NodeUploadForm() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
  const [errorMessage, setErrorMessage] = useState("");
  const [tagInput, setTagInput] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<{
    package?: File;
    icon?: File;
    screenshots: File[];
  }>({ screenshots: [] });

  const form = useForm<NodeUploadFormData>({
    resolver: zodResolver(nodeUploadSchema),
    defaultValues: {
      name: "",
      version: "1.0.0",
      description: "",
      longDescription: "",
      category: NodeCategory.UTILITY,
      tier: NodeTier.FREE,
      price: 0,
      tags: [],
      repositoryUrl: "",
      documentationUrl: "",
    },
  });

  const watchedTier = form.watch("tier");
  const watchedTags = form.watch("tags");

  const handleFileSelect = (type: "package" | "icon" | "screenshots", files: FileList | null) => {
    if (!files) return;

    if (type === "screenshots") {
      const newScreenshots = Array.from(files).filter(file => file.type.startsWith("image/"));
      setSelectedFiles(prev => ({
        ...prev,
        screenshots: [...prev.screenshots, ...newScreenshots].slice(0, 5) // Max 5 screenshots
      }));
    } else {
      const file = files[0];
      if (type === "package" && file.name.endsWith(".zip")) {
        setSelectedFiles(prev => ({ ...prev, package: file }));
      } else if (type === "icon" && file.type.startsWith("image/")) {
        setSelectedFiles(prev => ({ ...prev, icon: file }));
      }
    }
  };

  const removeScreenshot = (index: number) => {
    setSelectedFiles(prev => ({
      ...prev,
      screenshots: prev.screenshots.filter((_, i) => i !== index)
    }));
  };

  const addTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      form.setValue("tags", [...watchedTags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    form.setValue("tags", watchedTags.filter(tag => tag !== tagToRemove));
  };

  const onSubmit = async (data: NodeUploadFormData) => {
    if (!selectedFiles.package) {
      setErrorMessage("Please select a node package file (.zip)");
      return;
    }

    setIsUploading(true);
    setUploadStatus("uploading");
    setUploadProgress(0);
    setErrorMessage("");

    try {
      const formData = new FormData();

      // Add form data
      Object.entries(data).forEach(([key, value]) => {
        if (key === "tags") {
          formData.append(key, JSON.stringify(value));
        } else if (value !== undefined && value !== "") {
          formData.append(key, value.toString());
        }
      });

      // Add files
      formData.append("package", selectedFiles.package);
      if (selectedFiles.icon) {
        formData.append("icon", selectedFiles.icon);
      }
      selectedFiles.screenshots.forEach((screenshot, index) => {
        formData.append(`screenshot_${index}`, screenshot);
      });

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch("/api/developer/nodes", {
        method: "POST",
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Upload failed");
      }

      setUploadStatus("success");
      form.reset();
      setSelectedFiles({ screenshots: [] });

    } catch (error) {
      setUploadStatus("error");
      setErrorMessage(error instanceof Error ? error.message : "Upload failed");
    } finally {
      setIsUploading(false);
      setTimeout(() => {
        setUploadProgress(0);
        setUploadStatus("idle");
      }, 3000);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload New Node
        </CardTitle>
        <CardDescription>
          Create and publish a new workflow node to the marketplace
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {uploadStatus === "success" && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Node uploaded successfully! Your node is now pending approval and will be reviewed by our team within 1-3 business days.
              You can track the approval status on your <a href="/developer/nodes/status" className="underline font-medium">Node Status</a> page.
            </AlertDescription>
          </Alert>
        )}

        {uploadStatus === "error" && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}

        {isUploading && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Uploading node...</span>
              <span>{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} />
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Node Name</FormLabel>
                    <FormControl>
                      <Input placeholder="My Awesome Node" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="version"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Version</FormLabel>
                    <FormControl>
                      <Input placeholder="1.0.0" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Short Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of what your node does..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="longDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Detailed Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Detailed description, features, usage examples..."
                      className="min-h-[120px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category and Pricing */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(NodeCategory).map((category) => (
                          <SelectItem key={category} value={category}>
                            {category.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tier</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select tier" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(NodeTier).map((tier) => (
                          <SelectItem key={tier} value={tier}>
                            {tier.charAt(0).toUpperCase() + tier.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {watchedTier !== NodeTier.FREE && (
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price (IDR)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="50000"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Add a tag..."
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
                />
                <Button type="button" onClick={addTag} variant="outline">
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {watchedTags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            {/* File Uploads */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Files</h3>

              {/* Package Upload */}
              <div className="space-y-2">
                <Label>Node Package (.zip) *</Label>
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                  <div className="flex flex-col items-center gap-2">
                    <FileCode className="h-8 w-8 text-muted-foreground" />
                    <div className="text-center">
                      <p className="text-sm font-medium">
                        {selectedFiles.package ? selectedFiles.package.name : "Upload node package"}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        ZIP file containing your node code and dependencies
                      </p>
                    </div>
                    <Input
                      type="file"
                      accept=".zip"
                      onChange={(e) => handleFileSelect("package", e.target.files)}
                      className="hidden"
                      id="package-upload"
                    />
                    <Button type="button" variant="outline" asChild>
                      <label htmlFor="package-upload" className="cursor-pointer">
                        Choose File
                      </label>
                    </Button>
                  </div>
                </div>
              </div>

              {/* Icon Upload */}
              <div className="space-y-2">
                <Label>Icon (Optional)</Label>
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div className="flex items-center gap-4">
                    {selectedFiles.icon ? (
                      <img
                        src={URL.createObjectURL(selectedFiles.icon)}
                        alt="Icon preview"
                        className="w-12 h-12 rounded object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-muted rounded flex items-center justify-center">
                        <Image className="h-6 w-6 text-muted-foreground" />
                      </div>
                    )}
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        {selectedFiles.icon ? selectedFiles.icon.name : "Upload icon"}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        PNG, JPG, or SVG (recommended: 64x64px)
                      </p>
                    </div>
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleFileSelect("icon", e.target.files)}
                      className="hidden"
                      id="icon-upload"
                    />
                    <Button type="button" variant="outline" size="sm" asChild>
                      <label htmlFor="icon-upload" className="cursor-pointer">
                        Choose
                      </label>
                    </Button>
                  </div>
                </div>
              </div>

              {/* Screenshots Upload */}
              <div className="space-y-2">
                <Label>Screenshots (Optional)</Label>
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Upload screenshots</p>
                        <p className="text-xs text-muted-foreground">
                          Show your node in action (max 5 images)
                        </p>
                      </div>
                      <div>
                        <Input
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={(e) => handleFileSelect("screenshots", e.target.files)}
                          className="hidden"
                          id="screenshots-upload"
                        />
                        <Button type="button" variant="outline" size="sm" asChild>
                          <label htmlFor="screenshots-upload" className="cursor-pointer">
                            Add Images
                          </label>
                        </Button>
                      </div>
                    </div>

                    {selectedFiles.screenshots.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {selectedFiles.screenshots.map((file, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Screenshot ${index + 1}`}
                              className="w-full h-24 object-cover rounded border"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => removeScreenshot(index)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* URLs */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="repositoryUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Repository URL (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="https://github.com/user/repo" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="documentationUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Documentation URL (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="https://docs.example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => form.reset()}>
                Reset
              </Button>
              <Button type="submit" disabled={isUploading}>
                {isUploading ? "Uploading..." : "Upload Node"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
