"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Loader2, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface BankAccount {
  id: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  isDefault: boolean;
  isVerified: boolean;
}

interface WithdrawalRequestFormProps {
  availableBalance: number;
  bankAccounts: BankAccount[];
  onSuccess: () => void;
}

const withdrawalSchema = z.object({
  amount: z.number().min(50000, "Minimum withdrawal amount is IDR 50,000").max(********, "Maximum withdrawal amount is IDR 10,000,000"),
  bankAccountId: z.string().min(1, "Please select a bank account"),
});

type WithdrawalFormData = z.infer<typeof withdrawalSchema>;

export function WithdrawalRequestForm({ availableBalance, bankAccounts, onSuccess }: WithdrawalRequestFormProps) {
  const [loading, setLoading] = useState(false);

  const form = useForm<WithdrawalFormData>({
    resolver: zodResolver(withdrawalSchema),
    defaultValues: {
      amount: 0,
      bankAccountId: bankAccounts.find(acc => acc.isDefault)?.id || "",
    },
  });

  const watchedAmount = form.watch("amount");

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const onSubmit = async (data: WithdrawalFormData) => {
    if (data.amount > availableBalance) {
      toast.error("Withdrawal amount exceeds available balance");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/developer/withdrawal-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        toast.success("Withdrawal request submitted successfully!");
        form.reset();
        onSuccess();
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to submit withdrawal request");
      }
    } catch (error) {
      console.error('Error submitting withdrawal request:', error);
      toast.error("Failed to submit withdrawal request");
    } finally {
      setLoading(false);
    }
  };

  const setMaxAmount = () => {
    form.setValue("amount", availableBalance);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Available Balance Info */}
        <div className="p-4 bg-muted rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Available Balance:</span>
            <span className="text-lg font-bold text-green-600">
              {formatCurrency(availableBalance)}
            </span>
          </div>
        </div>

        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Withdrawal Amount (IDR)</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input 
                    type="number" 
                    placeholder="Enter amount"
                    min="50000"
                    max={availableBalance}
                    step="1000"
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-6 px-2 text-xs"
                    onClick={setMaxAmount}
                  >
                    Max
                  </Button>
                </div>
              </FormControl>
              <FormDescription>
                Minimum: {formatCurrency(50000)} • Maximum: {formatCurrency(Math.min(availableBalance, ********))}
              </FormDescription>
              {watchedAmount > 0 && (
                <div className="text-sm text-muted-foreground">
                  You will receive: {formatCurrency(watchedAmount)}
                </div>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="bankAccountId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bank Account</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select bank account" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bankAccounts.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>
                          {account.bankName} • ****{account.accountNumber.slice(-4)}
                        </span>
                        <div className="flex gap-1 ml-2">
                          {account.isDefault && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">Default</span>
                          )}
                          {account.isVerified ? (
                            <span className="text-xs bg-green-100 text-green-800 px-1 rounded">Verified</span>
                          ) : (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-1 rounded">Pending</span>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Warning for unverified accounts */}
        {form.watch("bankAccountId") && (
          (() => {
            const selectedAccount = bankAccounts.find(acc => acc.id === form.watch("bankAccountId"));
            if (selectedAccount && !selectedAccount.isVerified) {
              return (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    This bank account is not yet verified. Withdrawal processing may be delayed until verification is complete.
                  </AlertDescription>
                </Alert>
              );
            }
            return null;
          })()
        )}

        {/* Processing Info */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-sm mb-2">Processing Information:</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• Withdrawals are processed within 1-3 business days</li>
            <li>• Bank transfers are made during Indonesian banking hours</li>
            <li>• You will receive an email confirmation once processed</li>
            <li>• Minimum withdrawal amount is IDR 50,000</li>
          </ul>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button type="submit" disabled={loading || availableBalance < 50000}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Submit Withdrawal Request
          </Button>
        </div>
      </form>
    </Form>
  );
}
