"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

const bankAccountSchema = z.object({
  bankName: z.string().min(1, "Bank name is required"),
  accountNumber: z.string().min(8, "Account number must be at least 8 digits").max(20, "Account number too long"),
  accountHolderName: z.string().min(1, "Account holder name is required"),
  branchCode: z.string().optional(),
  isDefault: z.boolean().default(false),
});

type BankAccountFormData = {
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  branchCode?: string;
  isDefault: boolean;
};

interface BankAccountFormProps {
  onSuccess: () => void;
}

// Major Indonesian banks
const indonesianBanks = [
  "Bank Central Asia (BCA)",
  "Bank Mandiri",
  "Bank Rakyat Indonesia (BRI)",
  "Bank Negara Indonesia (BNI)",
  "Bank Tabungan Negara (BTN)",
  "Bank Danamon",
  "Bank CIMB Niaga",
  "Bank Permata",
  "Bank Maybank Indonesia",
  "Bank OCBC NISP",
  "Bank Panin",
  "Bank UOB Indonesia",
  "Bank Mega",
  "Bank Bukopin",
  "Bank Sinarmas",
  "Bank Commonwealth",
  "Bank Muamalat",
  "Bank Syariah Indonesia (BSI)",
  "Bank Jago",
  "Bank Neo Commerce",
  "Other"
];

export function BankAccountForm({ onSuccess }: BankAccountFormProps) {
  const [loading, setLoading] = useState(false);

  const form = useForm<BankAccountFormData>({
    defaultValues: {
      bankName: "",
      accountNumber: "",
      accountHolderName: "",
      branchCode: "",
      isDefault: false,
    },
  });

  const onSubmit = async (data: BankAccountFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/developer/bank-accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        toast.success("Bank account added successfully!");
        form.reset();
        onSuccess();
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to add bank account");
      }
    } catch (error) {
      console.error('Error adding bank account:', error);
      toast.error("Failed to add bank account");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="bankName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bank Name</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your bank" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {indonesianBanks.map((bank) => (
                    <SelectItem key={bank} value={bank}>
                      {bank}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="accountNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Account Number</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter your account number" 
                  {...field}
                  onChange={(e) => {
                    // Only allow numbers
                    const value = e.target.value.replace(/\D/g, '');
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormDescription>
                Enter your bank account number (numbers only)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="accountHolderName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Account Holder Name</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter account holder name" 
                  {...field}
                  onChange={(e) => {
                    // Convert to uppercase for consistency
                    field.onChange(e.target.value.toUpperCase());
                  }}
                />
              </FormControl>
              <FormDescription>
                Name as it appears on your bank account (will be converted to uppercase)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="branchCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Branch Code (Optional)</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter branch code if required" 
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Some banks require a branch code for transfers
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="isDefault"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  Set as default account
                </FormLabel>
                <FormDescription>
                  Use this account as the default for withdrawals
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2 pt-4">
          <Button type="submit" disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Add Bank Account
          </Button>
        </div>
      </form>
    </Form>
  );
}
