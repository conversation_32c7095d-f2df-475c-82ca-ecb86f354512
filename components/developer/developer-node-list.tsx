"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  Package, 
  Search, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Eye, 
  Download,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Star
} from "lucide-react";
import { NodePlugin, NodeTier } from "@/lib/marketplace/types";
import { marketplaceHelpers } from "@/lib/marketplace/helpers";

interface DeveloperNode extends NodePlugin {
  status: "pending" | "approved" | "rejected";
  rejectionReason?: string;
}

export function DeveloperNodeList() {
  const [nodes, setNodes] = useState<DeveloperNode[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [error, setError] = useState("");

  useEffect(() => {
    loadDeveloperNodes();
  }, []);

  const loadDeveloperNodes = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/developer/nodes");
      
      if (!response.ok) {
        throw new Error("Failed to load nodes");
      }

      const data = await response.json();
      setNodes(data);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load nodes");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteNode = async (nodeId: string) => {
    if (!confirm("Are you sure you want to delete this node? This action cannot be undone.")) {
      return;
    }

    try {
      const response = await fetch(`/api/developer/nodes/${nodeId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete node");
      }

      setNodes(prev => prev.filter(node => node.id !== nodeId));
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to delete node");
    }
  };

  const filteredNodes = nodes.filter(node => {
    const matchesSearch = !searchQuery || 
      node.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      node.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || node.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>;
      case "pending":
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
      case "rejected":
        return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTierBadge = (tier: NodeTier) => {
    switch (tier) {
      case NodeTier.FREE:
        return <Badge variant="outline">Free</Badge>;
      case NodeTier.PREMIUM:
        return <Badge className="bg-blue-100 text-blue-800">Premium</Badge>;
      case NodeTier.ENTERPRISE:
        return <Badge className="bg-purple-100 text-purple-800">Enterprise</Badge>;
      default:
        return <Badge variant="outline">{tier}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Package className="h-8 w-8 mx-auto mb-2 text-muted-foreground animate-pulse" />
            <p className="text-muted-foreground">Loading your nodes...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            My Published Nodes
          </CardTitle>
          <CardDescription>
            Manage your published workflow nodes and track their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Filters */}
          <div className="flex gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search nodes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Node List */}
          {filteredNodes.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">No nodes found</h3>
              <p className="text-muted-foreground mb-4">
                {nodes.length === 0 
                  ? "You haven't published any nodes yet. Upload your first node to get started!"
                  : "No nodes match your current filters."
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredNodes.map((node) => (
                <Card key={node.id} className="border">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="flex-shrink-0">
                          <img
                            src={node.icon}
                            alt={node.name}
                            className="w-12 h-12 rounded-lg object-cover border"
                          />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-lg">{node.name}</h3>
                            <Badge variant="outline" className="text-xs">
                              v{node.version}
                            </Badge>
                            {getTierBadge(node.tier)}
                            {getStatusBadge(node.status)}
                          </div>
                          
                          <p className="text-muted-foreground text-sm mb-2 line-clamp-2">
                            {node.description}
                          </p>
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Download className="h-4 w-4" />
                              {marketplaceHelpers.formatDownloads(node.downloads)}
                            </div>
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4" />
                              {node.rating.toFixed(1)} ({node.reviewCount})
                            </div>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-4 w-4" />
                              {marketplaceHelpers.formatDownloads(node.weeklyDownloads)} this week
                            </div>
                            {node.tier !== NodeTier.FREE && node.price && (
                              <div className="font-medium text-primary">
                                {marketplaceHelpers.formatPrice(node.price)}
                              </div>
                            )}
                          </div>

                          {node.status === "rejected" && node.rejectionReason && (
                            <Alert variant="destructive" className="mt-3">
                              <AlertCircle className="h-4 w-4" />
                              <AlertDescription>
                                <strong>Rejection Reason:</strong> {node.rejectionReason}
                              </AlertDescription>
                            </Alert>
                          )}
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Node
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-destructive"
                            onClick={() => handleDeleteNode(node.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Node
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
