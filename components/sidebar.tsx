'use client';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  HomeIcon,
  UserIcon,
  LogOutIcon,
  GitBranchIcon
} from 'lucide-react';
import { useSession, signOut } from 'next-auth/react';

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Sidebar({ className, ...props }: SidebarProps) {
  const pathname = usePathname();
  const { status } = useSession();
  const isAuthenticated = status === 'authenticated';

  const routes = [
    {
      href: '/',
      label: 'Home',
      icon: HomeIcon,
      active: pathname === '/',
    },
    {
      href: '/profile',
      label: 'Profile',
      icon: UserIcon,
      active: pathname === '/profile',
      requiresAuth: true,
    },
    {
      href: '/workflow-manager',
      label: 'Workflows',
      icon: GitBranchIcon,
      active: pathname?.startsWith('/workflow') || false,
      requiresAuth: true,
    },
  ];

  return (
    <div className={cn('pb-12', className)} {...props}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            Navigation
          </h2>
          <div className="space-y-1">
            {routes.map((route) => {
              if (route.requiresAuth && !isAuthenticated) return null;

              return (
                <Button
                  key={route.href}
                  variant={route.active ? 'secondary' : 'ghost'}
                  size="sm"
                  className="w-full justify-start"
                  asChild
                >
                  <Link href={route.href}>
                    <route.icon className="mr-2 h-4 w-4" />
                    {route.label}
                  </Link>
                </Button>
              );
            })}

            {isAuthenticated && (
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
                onClick={() => signOut({ callbackUrl: '/' })}
              >
                <LogOutIcon className="mr-2 h-4 w-4" />
                Logout
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
