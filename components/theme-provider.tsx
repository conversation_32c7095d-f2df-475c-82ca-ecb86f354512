"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { ThemeProvider as NextThemesProvider, useTheme as useNextTheme } from "next-themes";

type ThemeProviderProps = React.ComponentProps<typeof NextThemesProvider>;

// Create a context to expose theme values and state
export const ThemeContext = createContext<{
  theme: string | undefined;
  isDarkTheme: boolean;
  setTheme: (theme: string) => void;
}>({
  theme: undefined,
  isDarkTheme: false,
  setTheme: () => {},
});

// Custom hook to use our enhanced theme context
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider {...props}>
      <ThemeProviderContent>{children}</ThemeProviderContent>
    </NextThemesProvider>
  );
}

// Internal component to handle theme state
function ThemeProviderContent({ children }: { children: React.ReactNode }) {
  const { theme, setTheme } = useNextTheme();
  const [isDarkTheme, setIsDarkTheme] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Handle initial mount and theme changes
  useEffect(() => {
    setMounted(true);

    // Function to check if dark mode is active
    const checkDarkMode = () => {
      const isDark = document.documentElement.classList.contains('dark');
      setIsDarkTheme(isDark);
    };

    // Check immediately after mount
    checkDarkMode();

    // Set up a mutation observer to detect theme class changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === 'attributes' &&
          mutation.attributeName === 'class'
        ) {
          checkDarkMode();
        }
      });
    });

    // Start observing the document element for class changes
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    });

    // Clean up observer on unmount
    return () => observer.disconnect();
  }, [theme]);

  // Only render children after mounting to avoid hydration mismatch
  if (!mounted) {
    return null;
  }

  return (
    <ThemeContext.Provider value={{ theme, isDarkTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}