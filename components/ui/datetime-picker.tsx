"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Clock } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface DateTimePickerProps {
  date?: Date
  onDateChange?: (date: Date | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DateTimePicker({
  date,
  onDateChange,
  placeholder = "Pick a date and time",
  className,
  disabled = false
}: DateTimePickerProps) {
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(date)
  const [timeValue, setTimeValue] = React.useState<string>(
    date ? format(date, "HH:mm:ss") : "12:00:00"
  )
  const [isOpen, setIsOpen] = React.useState(false)

  // Update internal state when external date changes
  React.useEffect(() => {
    setSelectedDate(date)
    if (date) {
      setTimeValue(format(date, "HH:mm:ss"))
    }
  }, [date])

  const handleDateSelect = (newDate: Date | undefined) => {
    if (!newDate) {
      setSelectedDate(undefined)
      onDateChange?.(undefined)
      return
    }

    // If we have a time value, combine it with the new date
    if (timeValue) {
      const [hours, minutes, seconds] = timeValue.split(':').map(Number)
      const combinedDate = new Date(newDate)
      combinedDate.setHours(hours, minutes, seconds || 0)
      setSelectedDate(combinedDate)
      onDateChange?.(combinedDate)
    } else {
      setSelectedDate(newDate)
      onDateChange?.(newDate)
    }
  }

  const handleTimeChange = (newTime: string) => {
    setTimeValue(newTime)
    
    if (selectedDate && newTime) {
      const [hours, minutes, seconds] = newTime.split(':').map(Number)
      const combinedDate = new Date(selectedDate)
      combinedDate.setHours(hours, minutes, seconds || 0)
      setSelectedDate(combinedDate)
      onDateChange?.(combinedDate)
    }
  }

  const handleTimeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove any AM/PM from the value
    const cleanValue = e.target.value.replace(/\s*(AM|PM)\s*/gi, '')
    handleTimeChange(cleanValue)
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal",
              !selectedDate && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {selectedDate ? (
              format(selectedDate, "PPP 'at' HH:mm:ss")
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3 border-b">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Date</Label>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={(date) =>
                  date < new Date(new Date().setHours(0, 0, 0, 0))
                }
                initialFocus
              />
            </div>
          </div>
          <div className="p-3 space-y-2">
            <Label className="text-sm font-medium">Time (24-hour format)</Label>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <Input
                type="time"
                value={timeValue}
                onChange={handleTimeInputChange}
                className="font-mono time-24hour"
                step="1"
                placeholder="HH:MM:SS"
                pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}"
                title="Enter time in 24-hour format (HH:MM:SS)"
                data-format="24"
                style={{
                  colorScheme: 'dark light',
                  WebkitAppearance: 'none',
                  MozAppearance: 'textfield'
                }}
              />
            </div>
            <p className="text-xs text-muted-foreground">
              Format: HH:MM:SS (24-hour) • No AM/PM
            </p>
            <div className="flex justify-end space-x-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={() => setIsOpen(false)}
                disabled={!selectedDate}
              >
                Done
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
