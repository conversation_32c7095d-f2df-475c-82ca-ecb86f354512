"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Search, 
  Plus, 
  Package, 
  Zap,
  Database,
  Globe,
  Bot,
  Settings
} from "lucide-react";
import { useInstalledNodes } from "@/hooks/use-installed-nodes";
import { NodeDefinition } from "@/lib/node-loader";

interface NodePaletteProps {
  onAddNode?: (nodeType: string, nodeDefinition: NodeDefinition) => void;
  className?: string;
}

export function NodePalette({ onAddNode, className }: NodePaletteProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  
  const { 
    nodeDefinitions, 
    loading, 
    error,
    fetchInstalledNodes 
  } = useInstalledNodes();

  // Filter nodes based on search and category
  const filteredNodes = nodeDefinitions.filter(node => {
    const matchesSearch = !searchQuery || 
      node.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      node.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === "all" || 
      node.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Get unique categories
  const categories = [
    { id: "all", label: "All", icon: Package },
    { id: "text", label: "Text", icon: Zap },
    { id: "data", label: "Data", icon: Database },
    { id: "api", label: "API", icon: Globe },
    { id: "ai", label: "AI", icon: Bot },
    { id: "utility", label: "Utility", icon: Settings },
  ];

  const handleAddNode = (nodeDefinition: NodeDefinition) => {
    if (onAddNode) {
      onAddNode(nodeDefinition.type, nodeDefinition);
    }
  };

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(c => c.id === category);
    const IconComponent = categoryData?.icon || Package;
    return <IconComponent className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Node Palette
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Node Palette
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-sm text-muted-foreground mb-4">
              Failed to load installed nodes
            </p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchInstalledNodes}
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Node Palette
        </CardTitle>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search nodes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-8"
          />
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {/* Category Filters */}
        <div className="px-4 pb-3">
          <div className="flex flex-wrap gap-1">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "ghost"}
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => setSelectedCategory(category.id)}
              >
                <category.icon className="h-3 w-3 mr-1" />
                {category.label}
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Node List */}
        <ScrollArea className="h-[400px]">
          <div className="p-4 space-y-2">
            {filteredNodes.length === 0 ? (
              <div className="text-center py-8">
                <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm text-muted-foreground">
                  {nodeDefinitions.length === 0 
                    ? "No nodes installed. Visit the marketplace to install nodes."
                    : "No nodes match your search criteria."
                  }
                </p>
              </div>
            ) : (
              filteredNodes.map((node) => (
                <div
                  key={node.id}
                  className="group relative border rounded-lg p-3 hover:bg-accent/50 transition-colors cursor-pointer"
                  onClick={() => handleAddNode(node)}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                      {node.icon ? (
                        <span className="text-sm">{node.icon}</span>
                      ) : (
                        getCategoryIcon(node.category)
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm truncate">
                          {node.name}
                        </h4>
                        <Badge variant="outline" className="text-xs">
                          v{node.version}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                        {node.description}
                      </p>
                      <div className="flex items-center gap-1">
                        <Badge variant="secondary" className="text-xs">
                          {node.category}
                        </Badge>
                        {node.inputs.length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {node.inputs.length} inputs
                          </Badge>
                        )}
                        {node.outputs.length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {node.outputs.length} outputs
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddNode(node);
                      }}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Footer */}
        {nodeDefinitions.length > 0 && (
          <>
            <Separator />
            <div className="p-4">
              <p className="text-xs text-muted-foreground text-center">
                {nodeDefinitions.length} node{nodeDefinitions.length !== 1 ? 's' : ''} installed
              </p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
