"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { TextIcon, ArrowLeftIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

/**
 * Text Output Node - Standardized Structure
 * Displays text data received from connected nodes
 */
const TextOutputNode = memo(({ data, id }: StandardNodeProps) => {
  // Track if we have content to display
  const [hasContent, setHasContent] = useState(false);
  // Track if we just received new content for animation
  const [isNewContent, setIsNewContent] = useState(false);

  // Update content state when input value changes
  useEffect(() => {
    console.log(`TextOutputNode ${id}: useEffect triggered with data.inputValue:`, data.inputValue);
    console.log(`TextOutputNode ${id}: Full data object:`, data);

    if (data.inputValue) {
      console.log(`TextOutputNode ${id}: Setting hasContent to true`);
      setHasContent(true);
      // Trigger animation when new content arrives
      setIsNewContent(true);
      const timer = setTimeout(() => setIsNewContent(false), 500);
      return () => clearTimeout(timer);
    } else {
      console.log(`TextOutputNode ${id}: Setting hasContent to false`);
      setHasContent(false);
    }
  }, [data.inputValue, id]);

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <TextIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Text Output"}
          </Label>
          {hasContent && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Has content" />
          )}
        </div>

        {/* Input Indicator */}
        {hasContent && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className={`h-3 w-3 ${isNewContent ? 'animate-pulse' : ''}`} />
            <span>Receiving data</span>
          </div>
        )}

        {/* Content Display */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">
            Output Content
          </Label>
          {hasContent ? (
            <Textarea
              id={`text-output-${id}`}
              value={data.inputValue || ''}
              readOnly
              className="min-h-[120px] max-h-[200px] resize-none text-sm bg-muted/50"
              placeholder="Output will appear here..."
            />
          ) : (
            <div className="flex items-center justify-center h-[120px] border border-dashed rounded-md bg-muted/50 text-muted-foreground text-sm">
              <div className="text-center">
                <TextIcon className="h-6 w-6 mx-auto mb-2 opacity-50" />
                <p>Waiting for text input...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

TextOutputNode.displayName = "TextOutputNode";

// Export execution definition for the workflow engine
export const textOutputExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    // Get input text from connected nodes
    const inputText = inputs.text || inputs.data || inputs.value || inputs.result || "";

    // Log execution
    context.log(`Text Output Node displaying: "${inputText}"`, 'info');

    // Format the text for display
    const displayText = String(inputText);
    const wordCount = displayText.split(/\s+/).filter(word => word.length > 0).length;
    const charCount = displayText.length;
    const lineCount = displayText.split('\n').length;

    // Return display information and metadata
    return {
      success: true,
      displayed: true,
      text: displayText,
      data: displayText,
      value: displayText,
      wordCount: wordCount,
      characterCount: charCount,
      lineCount: lineCount,
      isEmpty: displayText.length === 0,
      hasContent: displayText.length > 0,
      displayedAt: new Date().toISOString(),
      formattedText: displayText,
      preview: displayText.length > 100 ? displayText.substring(0, 100) + '...' : displayText
    };
  },
  inputs: [
    {
      id: 'text',
      name: 'Text Input',
      type: 'string' as const,
      required: false,
      description: 'Text to display'
    },
    {
      id: 'data',
      name: 'Data Input',
      type: 'string' as const,
      required: false,
      description: 'Data to display (alternative input)'
    },
    {
      id: 'value',
      name: 'Value Input',
      type: 'string' as const,
      required: false,
      description: 'Value to display (alternative input)'
    },
    {
      id: 'result',
      name: 'Result Input',
      type: 'string' as const,
      required: false,
      description: 'Result to display (alternative input)'
    }
  ],
  outputs: [
    {
      id: 'displayed',
      name: 'Display Status',
      type: 'boolean' as const,
      description: 'Whether the text was successfully displayed'
    },
    {
      id: 'text',
      name: 'Displayed Text',
      type: 'string' as const,
      description: 'The text that was displayed'
    },
    {
      id: 'data',
      name: 'Data Output',
      type: 'string' as const,
      description: 'Same as displayed text, for compatibility'
    },
    {
      id: 'value',
      name: 'Value Output',
      type: 'string' as const,
      description: 'Same as displayed text, for compatibility'
    },
    {
      id: 'wordCount',
      name: 'Word Count',
      type: 'number' as const,
      description: 'Number of words in the displayed text'
    },
    {
      id: 'characterCount',
      name: 'Character Count',
      type: 'number' as const,
      description: 'Number of characters in the displayed text'
    },
    {
      id: 'lineCount',
      name: 'Line Count',
      type: 'number' as const,
      description: 'Number of lines in the displayed text'
    },
    {
      id: 'isEmpty',
      name: 'Is Empty',
      type: 'boolean' as const,
      description: 'Whether the displayed text is empty'
    },
    {
      id: 'hasContent',
      name: 'Has Content',
      type: 'boolean' as const,
      description: 'Whether the displayed text has content'
    },
    {
      id: 'displayedAt',
      name: 'Displayed At',
      type: 'string' as const,
      description: 'Timestamp when the text was displayed'
    },
    {
      id: 'preview',
      name: 'Text Preview',
      type: 'string' as const,
      description: 'Preview of the displayed text (first 100 characters)'
    }
  ],
  timeout: 2000, // 2 second timeout
  retryable: false // No need to retry text output
};

export default TextOutputNode;
