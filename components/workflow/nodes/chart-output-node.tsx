"use client";

import { useState, useEffect, memo, useMemo, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, NodeResizer } from "reactflow";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Pie<PERSON>hart, AlertCircle, Maximize2, Minimize2,
  CircleDot, Activity, SlidersHorizontal, ArrowLeftIcon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useTheme } from "@/components/theme-provider";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type ChartType = "bar" | "line" | "pie" | "scatter" | "area" | "radar" | "bubble";

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[] | string;
    borderColor?: string;
    borderWidth?: number;
  }[];
}

/**
 * Chart Output Node - Standardized Structure
 * Display data as interactive charts with multiple visualization types
 */
const ChartOutputNode = memo(({ data, id, selected }: StandardNodeProps) => {
  const [chartType, setChartType] = useState<ChartType>("bar");
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [error, setError] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [chartHeight, setChartHeight] = useState(120);
  const [nodeWidth, setNodeWidth] = useState(300);

  // Get theme information using our enhanced hook
  const { isDarkTheme } = useTheme();

  // Generate colors for chart - memoized for performance
  const generateColors = useCallback((count: number): string[] => {
    const baseColors = isDarkTheme ? [
      'rgba(180, 173, 168, 0.8)', // Lighter Stone for dark mode
      'rgba(200, 194, 190, 0.8)',
      'rgba(220, 217, 215, 0.8)',
      'rgba(240, 238, 237, 0.8)',
      'rgba(250, 250, 249, 0.8)',
      'rgba(255, 255, 255, 0.7)',
    ] : [
      'rgba(120, 113, 108, 0.8)', // Stone-500
      'rgba(168, 162, 158, 0.8)', // Stone-400
      'rgba(214, 211, 209, 0.8)', // Stone-300
      'rgba(231, 229, 228, 0.8)', // Stone-200
      'rgba(245, 245, 244, 0.8)', // Stone-100
      'rgba(250, 250, 249, 0.8)', // Stone-50
    ];

    return Array(count).fill(0).map((_, i: number) => baseColors[i % baseColors.length]);
  }, [isDarkTheme]);

  // Check if data is in valid chart format - memoized for performance
  const isValidChartData = useCallback((inputData: any): inputData is ChartData => {
    return (
      inputData &&
      Array.isArray(inputData.labels) &&
      Array.isArray(inputData.datasets) &&
      inputData.datasets.every((dataset: any) =>
        dataset.label && Array.isArray(dataset.data)
      )
    );
  }, []);

  // Convert array data to chart format - memoized for performance
  const convertArrayToChartData = useCallback((arr: any[]): ChartData => {
    // For array of objects, use first object keys as labels
    if (arr.length > 0 && typeof arr[0] === 'object') {
      const firstItem = arr[0];
      const keys = Object.keys(firstItem);

      // Use first string/text property as label, and first number property as value
      const labelKey = keys.find(key => typeof firstItem[key] === 'string') || keys[0];
      const valueKey = keys.find(key => typeof firstItem[key] === 'number') || keys[1] || keys[0];

      return {
        labels: arr.map(item => String(item[labelKey])),
        datasets: [{
          label: valueKey,
          data: arr.map(item => Number(item[valueKey]) || 0),
          backgroundColor: generateColors(arr.length),
          borderColor: isDarkTheme
            ? 'rgba(220, 217, 215, 1)' // Lighter for dark mode
            : 'rgba(120, 113, 108, 1)', // Stone-500
          borderWidth: 1
        }]
      };
    }

    // For simple array of numbers
    if (arr.every(item => typeof item === 'number')) {
      return {
        labels: arr.map((_: any, i: number) => `Item ${i+1}`),
        datasets: [{
          label: 'Values',
          data: arr as number[],
          backgroundColor: generateColors(arr.length),
          borderColor: isDarkTheme
            ? 'rgba(220, 217, 215, 1)' // Lighter for dark mode
            : 'rgba(120, 113, 108, 1)', // Stone-500
          borderWidth: 1
        }]
      };
    }

    // Default fallback
    return {
      labels: arr.map((_: any, i: number) => `Item ${i+1}`),
      datasets: [{
        label: 'Values',
        data: arr.map(item => typeof item === 'number' ? item : 0),
        backgroundColor: generateColors(arr.length),
        borderColor: isDarkTheme
          ? 'rgba(220, 217, 215, 1)' // Lighter for dark mode
          : 'rgba(120, 113, 108, 1)', // Stone-500
        borderWidth: 1
      }]
    };
  }, [generateColors, isDarkTheme]);

  // Process input data when it changes - optimized with useMemo and useCallback
  useEffect(() => {
    // Clear chart data when input is empty, undefined, or just whitespace
    if (!data.inputValue || data.inputValue.trim() === "") {
      setChartData(null);
      setError("");
      setIsProcessing(false);

      // Reset all filters when data is cleared
      setMinValue(0);
      setMaxValue(100);
      setSearchTerm("");
      setExcludedItems([]);
      setSortOrder("none");
      setSelectedHeaders([]);
      setShowOnlySelectedHeaders(false);

      return;
    }

    // Debug the input data
    console.log("Chart node ID:", id, "received data:", data.inputValue.substring(0, 100) + (data.inputValue.length > 100 ? "..." : ""));

    // Use a flag to prevent multiple processing attempts
    setIsProcessing(true);

    // Use setTimeout to avoid blocking the main thread
    const timeoutId = setTimeout(() => {
      try {
        // First, check if inputValue exists and clean the input data
        if (!data.inputValue) {
          setError("No input data provided");
          setChartData(null);
          setIsProcessing(false);
          return;
        }

        const cleanedInput = data.inputValue.trim();

        // Try to detect if the input is CSV format
        if (cleanedInput.includes(',') && !cleanedInput.includes('{') && !cleanedInput.includes('[')) {
          // CSV parsing
          try {
            // Parse CSV data
            const parseCSV = (csvText: string): any[] => {
              // Split into lines, handling potential quoted newlines
              const lines: string[] = [];
              let currentLine = '';
              let inQuotes = false;

              for (let i = 0; i < csvText.length; i++) {
                const char = csvText[i];

                if (char === '"') {
                  inQuotes = !inQuotes;
                  currentLine += char;
                } else if ((char === '\n' || char === '\r') && !inQuotes) {
                  if (currentLine.trim()) {
                    lines.push(currentLine);
                  }
                  currentLine = '';
                  // Skip the \n in \r\n
                  if (char === '\r' && i + 1 < csvText.length && csvText[i + 1] === '\n') {
                    i++;
                  }
                } else {
                  currentLine += char;
                }
              }

              // Add the last line if there's content
              if (currentLine.trim()) {
                lines.push(currentLine);
              }

              if (lines.length < 1) {
                throw new Error("CSV must have at least a header row");
              }

              // Parse header row
              const headerLine = lines[0];
              const headers: string[] = [];
              let headerStart = 0;
              inQuotes = false;

              for (let i = 0; i <= headerLine.length; i++) {
                const char = headerLine[i];

                if (char === '"') {
                  inQuotes = !inQuotes;
                } else if ((char === ',' || i === headerLine.length) && !inQuotes) {
                  let header = headerLine.substring(headerStart, i).trim();

                  // Remove surrounding quotes if present
                  if (header.startsWith('"') && header.endsWith('"')) {
                    header = header.substring(1, header.length - 1).replace(/""/g, '"');
                  }

                  headers.push(header);
                  headerStart = i + 1;
                }
              }

              // Parse data rows
              const rows = lines.slice(1).map(line => {
                const values: string[] = [];
                let valueStart = 0;
                inQuotes = false;

                for (let i = 0; i <= line.length; i++) {
                  const char = line[i];

                  if (char === '"') {
                    inQuotes = !inQuotes;
                  } else if ((char === ',' || i === line.length) && !inQuotes) {
                    let value = line.substring(valueStart, i).trim();

                    // Remove surrounding quotes if present
                    if (value.startsWith('"') && value.endsWith('"')) {
                      value = value.substring(1, value.length - 1).replace(/""/g, '"');
                    }

                    values.push(value);
                    valueStart = i + 1;
                  }
                }

                // Create object from headers and values
                return headers.reduce((obj: Record<string, string>, header, i) => {
                  obj[header] = values[i] || '';
                  return obj;
                }, {});
              });

              return rows;
            };

            // Parse the CSV data into an array of objects
            const parsedData = parseCSV(cleanedInput);

            if (parsedData.length > 0) {
              // Get all column headers
              const headers = Object.keys(parsedData[0]);

              // Find columns with numeric values
              const numericColumns = headers.filter(header => {
                // Check if at least one row has a numeric value for this header
                return parsedData.some(row => {
                  const value = row[header];
                  return !isNaN(parseFloat(value)) && isFinite(Number(value));
                });
              });

              // If no numeric columns found, try to use all columns
              const columnsToUse = numericColumns.length > 0 ? numericColumns : headers;

              // Create datasets for each numeric column
              const datasets = columnsToUse.map((header, index) => {
                return {
                  label: header,
                  data: parsedData.map(row => {
                    const value = row[header];
                    return !isNaN(parseFloat(value)) ? parseFloat(value) : 0;
                  }),
                  backgroundColor: generateColors(parsedData.length)[index % parsedData.length],
                  borderColor: isDarkTheme
                    ? 'rgba(220, 217, 215, 1)' // Lighter for dark mode
                    : 'rgba(120, 113, 108, 1)', // Stone-500
                  borderWidth: 1
                };
              });

              // Find a suitable column for labels (prefer non-numeric)
              let labelColumn = headers.find(header => !numericColumns.includes(header));

              // If no non-numeric column found, use the first column
              if (!labelColumn && headers.length > 0) {
                labelColumn = headers[0];
              }

              // Create chart data
              const convertedData: ChartData = {
                labels: labelColumn
                  ? parsedData.map(row => String(row[labelColumn] || ''))
                  : parsedData.map((_, i) => `Row ${i+1}`),
                datasets: datasets
              };

              setChartData(convertedData);
              setError("");
              setIsProcessing(false);
              return;
            }
          } catch (csvErr) {
            // If CSV parsing fails, continue to JSON parsing
            console.log("CSV parsing failed, trying JSON", csvErr);
          }
        }

        // Try to parse as JSON
        let parsedData;
        try {
          parsedData = JSON.parse(cleanedInput);

          // Check if this is already in chart format with labels and datasets
          if (parsedData && parsedData.labels && parsedData.datasets) {
            console.log("Data is already in chart format:", parsedData);

            // Add colors if they're missing
            if (parsedData.datasets[0] && !parsedData.datasets[0].backgroundColor) {
              parsedData.datasets[0].backgroundColor = generateColors(parsedData.datasets[0].data.length);
              parsedData.datasets[0].borderColor = isDarkTheme
                ? 'rgba(220, 217, 215, 1)' // Lighter for dark mode
                : 'rgba(120, 113, 108, 1)'; // Stone-500
              parsedData.datasets[0].borderWidth = 1;
            }

            setChartData(parsedData);
            setError("");
            setIsProcessing(false);
            return;
          }
        } catch (jsonErr) {
          // If it's not valid JSON, try to extract numbers from the text
          const numbers = cleanedInput.match(/[-+]?[0-9]*\.?[0-9]+/g);
          if (numbers && numbers.length > 0) {
            const numericValues = numbers.map((n: string) => parseFloat(n));
            const convertedData = {
              labels: numericValues.map((_: number, i: number) => `Value ${i+1}`),
              datasets: [{
                label: 'Extracted Numbers',
                data: numericValues,
                backgroundColor: generateColors(numericValues.length),
                borderColor: isDarkTheme
                  ? 'rgba(220, 217, 215, 1)' // Lighter for dark mode
                  : 'rgba(120, 113, 108, 1)', // Stone-500
                borderWidth: 1
              }]
            };
            setChartData(convertedData);
            setError("");
            setIsProcessing(false);
            return;
          } else {
            throw new Error("Could not parse input as JSON or extract numeric values");
          }
        }

        // Check if the data is in the expected format or try to convert it
        if (isValidChartData(parsedData)) {
          setChartData(parsedData);
          setError("");
        } else if (Array.isArray(parsedData)) {
          // Try to convert array data to chart format
          const convertedData = convertArrayToChartData(parsedData);
          setChartData(convertedData);
          setError("");
        } else if (typeof parsedData === 'object' && parsedData !== null) {
          // Try to convert object data to chart format
          const labels = Object.keys(parsedData);
          const values = Object.values(parsedData).map(v =>
            typeof v === 'number' ? v : (parseFloat(v as string) || 0)
          );

          const convertedData: ChartData = {
            labels,
            datasets: [{
              label: 'Values',
              data: values,
              backgroundColor: generateColors(values.length),
              borderColor: isDarkTheme
                ? 'rgba(220, 217, 215, 1)' // Lighter for dark mode
                : 'rgba(120, 113, 108, 1)', // Stone-500
              borderWidth: 1
            }]
          };

          setChartData(convertedData);
          setError("");
        } else {
          setError("Invalid data format for chart. Expected JSON array, object, or CSV data with numeric values.");
          setChartData(null);
        }
      } catch (err) {
        console.error("Chart data processing error:", err);
        setError(`Failed to process data: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setChartData(null);

        // Reset all filters when there's an error
        setMinValue(0);
        setMaxValue(100);
        setSearchTerm("");
        setExcludedItems([]);
        setSortOrder("none");
        setSelectedHeaders([]);
        setShowOnlySelectedHeaders(false);
      } finally {
        setIsProcessing(false);
      }
    }, 0);

    return () => clearTimeout(timeoutId);
  }, [
    data.inputValue,
    generateColors,
    isDarkTheme,
    id
    // State setters are stable and don't need to be in the dependency array
  ]);



  // Render chart based on type and data
  const renderChart = () => {
    if (!chartData) return null;

    // Use filtered data if available, otherwise use original data
    const data = filteredChartData || chartData;

    // Check if data is empty or invalid
    if (!data || !data.labels || !data.datasets || data.labels.length === 0 || data.datasets.length === 0) {
      return (
        <div className="flex items-center justify-center h-[120px] text-muted-foreground text-sm">
          <span>No valid data to display</span>
        </div>
      );
    }

    const { labels, datasets } = data;

    // Calculate max value once for normalization
    const maxValue = Math.max(...datasets[0].data);

    // Simple chart rendering with divs (in a real app, you'd use a chart library)
    switch (chartType) {
      case 'bar':
        return (
          <div className="flex items-end gap-1 pt-2" style={{ height: `${chartHeight}px` }}>
            {datasets[0].data.map((value, i) => {
              // Calculate height percentage
              const heightPercentage = maxValue > 0
                ? Math.min(100, (value / maxValue) * 100)
                : 0;

              return (
                <div
                  key={i}
                  className="rounded-t w-full relative group transition-all hover:opacity-90"
                  style={{
                    height: `${heightPercentage}%`,
                    backgroundColor: datasets[0].backgroundColor?.[i],
                    minHeight: value > 0 ? '4px' : '0'
                  }}
                >
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-stone-800 text-white text-xs px-1 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                    {labels[i]}: {value}
                  </div>
                </div>
              );
            })}
          </div>
        );

      case 'line':
        // Simple line chart representation with improved styling
        return (
          <div className="relative pt-2" style={{ height: `${chartHeight}px` }}>
            <div className="absolute inset-0 flex items-end">
              {datasets[0].data.map((_, i) => (
                <div key={i} className="h-full flex-1 border-r border-stone-200" />
              ))}
            </div>
            <svg className="w-full h-full" viewBox={`0 0 ${datasets[0].data.length - 1} 100`} preserveAspectRatio="none">
              {/* Background area under the line */}
              <defs>
                <linearGradient id="lineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor={isDarkTheme
                    ? "rgba(220, 217, 215, 0.2)"
                    : "rgba(120, 113, 108, 0.2)"} />
                  <stop offset="100%" stopColor={isDarkTheme
                    ? "rgba(220, 217, 215, 0)"
                    : "rgba(120, 113, 108, 0)"} />
                </linearGradient>
              </defs>
              <polygon
                points={`0,100 ${datasets[0].data.map((value, i) => {
                  const x = i;
                  const y = maxValue > 0 ? 100 - (value / maxValue) * 100 : 100;
                  return `${x},${y}`;
                }).join(' ')} ${datasets[0].data.length - 1},100`}
                fill="url(#lineGradient)"
              />
              <polyline
                points={datasets[0].data.map((value, i) => {
                  const x = i;
                  const y = maxValue > 0 ? 100 - (value / maxValue) * 100 : 100;
                  return `${x},${y}`;
                }).join(' ')}
                fill="none"
                stroke={isDarkTheme
                  ? "rgba(220, 217, 215, 1)"
                  : "rgba(120, 113, 108, 1)"}
                strokeWidth="2"
                strokeLinejoin="round"
              />
              {/* Data points */}
              {datasets[0].data.map((value, i) => {
                const x = i;
                const y = maxValue > 0 ? 100 - (value / maxValue) * 100 : 100;
                return (
                  <circle
                    key={i}
                    cx={x}
                    cy={y}
                    r="3"
                    fill="white"
                    stroke={isDarkTheme
                      ? "rgba(220, 217, 215, 1)"
                      : "rgba(120, 113, 108, 1)"}
                    strokeWidth="1.5"
                  />
                );
              })}
            </svg>
          </div>
        );

      case 'pie':
        // Simple pie chart representation with improved styling
        const total = datasets[0].data.reduce((sum, value) => sum + value, 0);
        let cumulativeAngle = 0;

        return (
          <div className="flex justify-center pt-2">
            <div className="relative" style={{ width: `${chartHeight}px`, height: `${chartHeight}px` }}>
              <svg viewBox="0 0 100 100">
                {/* Add a subtle shadow for depth */}
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                  <feDropShadow dx="0" dy="1" stdDeviation="2" floodColor="rgba(0,0,0,0.2)" />
                </filter>
                <circle cx="50" cy="50" r="48" fill={isDarkTheme
                  ? "#1c1917" // Dark background for dark mode
                  : "white"} filter="url(#shadow)" />

                {datasets[0].data.map((value, i) => {
                  if (value <= 0) return null; // Skip zero values

                  const percentage = value / total;
                  const startAngle = cumulativeAngle;
                  const endAngle = cumulativeAngle + percentage * 360;
                  cumulativeAngle = endAngle;

                  const startRad = (startAngle - 90) * Math.PI / 180;
                  const endRad = (endAngle - 90) * Math.PI / 180;

                  const x1 = 50 + 48 * Math.cos(startRad);
                  const y1 = 50 + 48 * Math.sin(startRad);
                  const x2 = 50 + 48 * Math.cos(endRad);
                  const y2 = 50 + 48 * Math.sin(endRad);

                  const largeArcFlag = percentage > 0.5 ? 1 : 0;

                  // Calculate midpoint angle for label positioning
                  const midAngle = startAngle + (endAngle - startAngle) / 2;
                  const midRad = (midAngle - 90) * Math.PI / 180;
                  const labelX = 50 + 30 * Math.cos(midRad);
                  const labelY = 50 + 30 * Math.sin(midRad);

                  return (
                    <g key={i} className="hover:opacity-90 transition-opacity">
                      <path
                        d={`M 50 50 L ${x1} ${y1} A 48 48 0 ${largeArcFlag} 1 ${x2} ${y2} Z`}
                        fill={datasets[0].backgroundColor?.[i]}
                        stroke="white"
                        strokeWidth="1"
                      />
                      {percentage > 0.05 && (
                        <text
                          x={labelX}
                          y={labelY}
                          textAnchor="middle"
                          dominantBaseline="middle"
                          fill="white"
                          fontSize="8"
                          fontWeight="bold"
                        >
                          {Math.round(percentage * 100)}%
                        </text>
                      )}
                    </g>
                  );
                })}
              </svg>
            </div>
          </div>
        );

      case 'scatter':
        // Scatter plot representation
        return (
          <div className="relative pt-2" style={{ height: `${chartHeight}px` }}>
            <div className="absolute inset-0 flex items-end">
              {datasets[0].data.map((_, i) => (
                <div key={i} className="h-full flex-1 border-r border-stone-200" />
              ))}
            </div>
            <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              {/* Grid lines */}
              <g>
                {[0, 25, 50, 75, 100].map(y => (
                  <line
                    key={`grid-y-${y}`}
                    x1="0"
                    y1={y}
                    x2="100"
                    y2={y}
                    stroke={isDarkTheme ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)"}
                    strokeWidth="0.5"
                  />
                ))}
                {[0, 25, 50, 75, 100].map(x => (
                  <line
                    key={`grid-x-${x}`}
                    x1={x}
                    y1="0"
                    x2={x}
                    y2="100"
                    stroke={isDarkTheme ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)"}
                    strokeWidth="0.5"
                  />
                ))}
              </g>

              {/* Data points */}
              {datasets[0].data.map((value, i) => {
                // Use index as x-coordinate (normalized to 0-100)
                const x = (i / (datasets[0].data.length - 1 || 1)) * 100;
                const y = maxValue > 0 ? 100 - (value / maxValue) * 100 : 50;

                return (
                  <g key={i} className="hover:opacity-80 transition-opacity">
                    <circle
                      cx={x}
                      cy={y}
                      r="4"
                      fill={datasets[0].backgroundColor?.[i] || "#888"}
                      stroke="white"
                      strokeWidth="1"
                    />
                    <title>{labels[i]}: {value}</title>
                  </g>
                );
              })}
            </svg>
          </div>
        );

      case 'area':
        // Area chart representation
        return (
          <div className="relative pt-2" style={{ height: `${chartHeight}px` }}>
            <div className="absolute inset-0 flex items-end">
              {datasets[0].data.map((_, i) => (
                <div key={i} className="h-full flex-1 border-r border-stone-200" />
              ))}
            </div>
            <svg className="w-full h-full" viewBox={`0 0 ${datasets[0].data.length - 1} 100`} preserveAspectRatio="none">
              {/* Background area under the line */}
              <defs>
                <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor={isDarkTheme
                    ? "rgba(220, 217, 215, 0.4)"
                    : "rgba(120, 113, 108, 0.4)"} />
                  <stop offset="100%" stopColor={isDarkTheme
                    ? "rgba(220, 217, 215, 0)"
                    : "rgba(120, 113, 108, 0)"} />
                </linearGradient>
              </defs>
              <polygon
                points={`0,100 ${datasets[0].data.map((value, i) => {
                  const x = i;
                  const y = maxValue > 0 ? 100 - (value / maxValue) * 100 : 100;
                  return `${x},${y}`;
                }).join(' ')} ${datasets[0].data.length - 1},100`}
                fill="url(#areaGradient)"
              />
              <polyline
                points={datasets[0].data.map((value, i) => {
                  const x = i;
                  const y = maxValue > 0 ? 100 - (value / maxValue) * 100 : 100;
                  return `${x},${y}`;
                }).join(' ')}
                fill="none"
                stroke={isDarkTheme
                  ? "rgba(220, 217, 215, 1)"
                  : "rgba(120, 113, 108, 1)"}
                strokeWidth="3"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        );

      case 'radar':
        // Radar chart representation
        const sides = datasets[0].data.length;
        const angleStep = (Math.PI * 2) / sides;

        // Calculate coordinates for each point on the radar
        const points = datasets[0].data.map((value, i) => {
          const normalizedValue = maxValue > 0 ? value / maxValue : 0;
          const angle = i * angleStep - Math.PI / 2; // Start from top
          const x = 50 + 40 * normalizedValue * Math.cos(angle);
          const y = 50 + 40 * normalizedValue * Math.sin(angle);
          return { x, y, value, label: labels[i] };
        });

        // Create polygon points string
        const polygonPoints = points.map(p => `${p.x},${p.y}`).join(' ');

        return (
          <div className="flex justify-center pt-2">
            <div className="relative" style={{ width: `${chartHeight}px`, height: `${chartHeight}px` }}>
              <svg viewBox="0 0 100 100">
                {/* Background circles */}
                {[0.2, 0.4, 0.6, 0.8, 1].map((r, i) => (
                  <circle
                    key={`circle-${i}`}
                    cx="50"
                    cy="50"
                    r={40 * r}
                    fill="none"
                    stroke={isDarkTheme ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)"}
                    strokeWidth="0.5"
                  />
                ))}

                {/* Axis lines */}
                {Array.from({ length: sides }).map((_, i) => {
                  const angle = i * angleStep - Math.PI / 2;
                  const x = 50 + 40 * Math.cos(angle);
                  const y = 50 + 40 * Math.sin(angle);
                  return (
                    <line
                      key={`axis-${i}`}
                      x1="50"
                      y1="50"
                      x2={x}
                      y2={y}
                      stroke={isDarkTheme ? "rgba(255,255,255,0.2)" : "rgba(0,0,0,0.2)"}
                      strokeWidth="0.5"
                    />
                  );
                })}

                {/* Data polygon */}
                <polygon
                  points={polygonPoints}
                  fill={isDarkTheme ? "rgba(220, 217, 215, 0.3)" : "rgba(120, 113, 108, 0.3)"}
                  stroke={isDarkTheme ? "rgba(220, 217, 215, 0.8)" : "rgba(120, 113, 108, 0.8)"}
                  strokeWidth="2"
                />

                {/* Data points */}
                {points.map((point, i) => (
                  <g key={i}>
                    <circle
                      cx={point.x}
                      cy={point.y}
                      r="3"
                      fill="white"
                      stroke={datasets[0].backgroundColor?.[i] || "#888"}
                      strokeWidth="2"
                    />
                    <title>{point.label}: {point.value}</title>
                  </g>
                ))}

                {/* Labels */}
                {points.map((_, i) => {
                  const angle = i * angleStep - Math.PI / 2;
                  const labelX = 50 + 45 * Math.cos(angle);
                  const labelY = 50 + 45 * Math.sin(angle);
                  const textAnchor =
                    angle < -Math.PI/4 && angle > -3*Math.PI/4 ? "middle" :
                    angle < Math.PI/4 && angle > -Math.PI/4 ? "start" :
                    angle < 3*Math.PI/4 && angle > Math.PI/4 ? "middle" : "end";

                  return (
                    <text
                      key={`label-${i}`}
                      x={labelX}
                      y={labelY}
                      fontSize="6"
                      textAnchor={textAnchor}
                      dominantBaseline="middle"
                      fill={isDarkTheme ? "rgba(255,255,255,0.8)" : "rgba(0,0,0,0.8)"}
                    >
                      {labels[i]}
                    </text>
                  );
                })}
              </svg>
            </div>
          </div>
        );

      case 'bubble':
        // Bubble chart representation (size based on value)
        return (
          <div className="relative pt-2" style={{ height: `${chartHeight}px` }}>
            <div className="absolute inset-0 flex items-end">
              {datasets[0].data.map((_, i) => (
                <div key={i} className="h-full flex-1 border-r border-stone-200" />
              ))}
            </div>
            <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              {/* Grid lines */}
              <g>
                {[0, 25, 50, 75, 100].map(y => (
                  <line
                    key={`grid-y-${y}`}
                    x1="0"
                    y1={y}
                    x2="100"
                    y2={y}
                    stroke={isDarkTheme ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)"}
                    strokeWidth="0.5"
                  />
                ))}
                {[0, 25, 50, 75, 100].map(x => (
                  <line
                    key={`grid-x-${x}`}
                    x1={x}
                    y1="0"
                    x2={x}
                    y2="100"
                    stroke={isDarkTheme ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)"}
                    strokeWidth="0.5"
                  />
                ))}
              </g>

              {/* Data points as bubbles */}
              {datasets[0].data.map((value, i) => {
                // Use index as x-coordinate (normalized to 0-100)
                const x = (i / (datasets[0].data.length - 1 || 1)) * 100;
                const y = maxValue > 0 ? 100 - (value / maxValue) * 100 : 50;

                // Bubble size based on value (min 3, max 15)
                const size = maxValue > 0 ? 3 + (value / maxValue) * 12 : 5;

                return (
                  <g key={i} className="hover:opacity-80 transition-opacity">
                    <circle
                      cx={x}
                      cy={y}
                      r={size}
                      fill={datasets[0].backgroundColor?.[i] || "#888"}
                      fillOpacity="0.7"
                      stroke="white"
                      strokeWidth="1"
                    />
                    <title>{labels[i]}: {value}</title>
                  </g>
                );
              })}
            </svg>
          </div>
        );

      default:
        return null;
    }
  };

  // State for data filtering
  const [minValue, setMinValue] = useState(0);
  const [maxValue, setMaxValue] = useState(100);
  const [searchTerm, setSearchTerm] = useState("");
  const [excludedItems, setExcludedItems] = useState<number[]>([]);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc" | "none">("none");
  const [selectedHeaders, setSelectedHeaders] = useState<string[]>([]);
  const [showOnlySelectedHeaders, setShowOnlySelectedHeaders] = useState(false);

  // Apply filters to chart data
  const filteredChartData = useMemo(() => {
    if (!chartData) return null;

    // Create a deep copy of the chart data
    const filtered = {
      labels: [...chartData.labels],
      datasets: chartData.datasets.map(dataset => ({
        ...dataset,
        data: [...dataset.data],
        backgroundColor: dataset.backgroundColor ?
          (Array.isArray(dataset.backgroundColor) ? [...dataset.backgroundColor] : dataset.backgroundColor) : undefined
      }))
    };

    // Two different filtering approaches based on whether we're filtering by dataset (column headers) or by data points
    if (showOnlySelectedHeaders && selectedHeaders.length > 0) {
      // FILTERING BY DATASET (COLUMN HEADERS)
      // Keep only the datasets whose labels (column headers) are in selectedHeaders
      filtered.datasets = filtered.datasets.filter(dataset =>
        selectedHeaders.includes(dataset.label)
      );

      console.log("Filtering by column headers:", selectedHeaders);
      console.log("Filtered datasets:", filtered.datasets.map(d => d.label));
    } else {
      // FILTERING BY DATA POINTS
      // Get indices to keep after filtering
      const indicesToKeep: number[] = [];

      for (let i = 0; i < filtered.labels.length; i++) {
        // For each data point (row)
        const label = filtered.labels[i];

        // Check if any dataset has a value for this point that passes the min/max filter
        const passesMinMax = filtered.datasets.some(dataset => {
          const value = dataset.data[i];
          return value >= minValue && value <= maxValue;
        });

        // Apply search filter to the label
        const passesSearch = searchTerm === "" ||
          label.toLowerCase().includes(searchTerm.toLowerCase());

        // Check if excluded
        const isNotExcluded = !excludedItems.includes(i);

        if (passesMinMax && passesSearch && isNotExcluded) {
          indicesToKeep.push(i);
        }
      }

      // Create new filtered arrays
      filtered.labels = indicesToKeep.map(i => chartData.labels[i]);

      // Filter each dataset
      filtered.datasets = filtered.datasets.map(dataset => {
        const newDataset = { ...dataset };
        newDataset.data = indicesToKeep.map(i => dataset.data[i]);

        if (newDataset.backgroundColor && Array.isArray(newDataset.backgroundColor)) {
          newDataset.backgroundColor = indicesToKeep.map(
            i => dataset.backgroundColor?.[i] || ""
          );
        }

        return newDataset;
      });

      // Apply sorting if needed
      if (sortOrder !== "none" && filtered.datasets.length > 0) {
        // Use the first dataset for sorting
        const primaryDataset = filtered.datasets[0];

        // Create pairs of [index, value] for sorting
        const pairs = primaryDataset.data.map((value, i) => [i, value]);

        // Sort pairs by value
        pairs.sort((a, b) => {
          const [, valueA] = a;
          const [, valueB] = b;
          return sortOrder === "asc" ? valueA - valueB : valueB - valueA;
        });

        // Reorder arrays based on sorted indices
        const sortedIndices = pairs.map(pair => pair[0] as number);

        filtered.labels = sortedIndices.map(i => filtered.labels[i]);

        // Sort each dataset
        filtered.datasets = filtered.datasets.map(dataset => {
          const newDataset = { ...dataset };
          newDataset.data = sortedIndices.map(i => dataset.data[i]);

          if (newDataset.backgroundColor && Array.isArray(newDataset.backgroundColor)) {
            newDataset.backgroundColor = sortedIndices.map(
              i => dataset.backgroundColor?.[i] || ""
            );
          }

          return newDataset;
        });
      }
    }

    return filtered;
  }, [chartData, minValue, maxValue, searchTerm, excludedItems, sortOrder, selectedHeaders, showOnlySelectedHeaders]);

  // Get the icon for the current chart type
  const getChartIcon = useCallback((type: ChartType) => {
    switch (type) {
      case 'bar':
        return <BarChart className="h-4 w-4" />;
      case 'line':
        return <LineChart className="h-4 w-4" />;
      case 'pie':
        return <PieChart className="h-4 w-4" />;
      case 'scatter':
        return <CircleDot className="h-4 w-4" />;
      case 'area':
        return <Activity className="h-4 w-4" />;
      case 'radar':
        return <Activity className="h-4 w-4 rotate-45" />;
      case 'bubble':
        return <CircleDot className="h-4 w-4" />;
      default:
        return <BarChart className="h-4 w-4" />;
    }
  }, []);

  // Memoize the chart type selector to prevent unnecessary re-renders
  const chartTypeSelector = useMemo(() => (
    <Select
      value={chartType}
      onValueChange={(value) => setChartType(value as ChartType)}
    >
      <SelectTrigger className="w-[110px] h-8 pl-2 pr-2">
        <div className="flex items-center gap-1.5 truncate">
          {getChartIcon(chartType)}
          <span className="truncate">{chartType.charAt(0).toUpperCase() + chartType.slice(1)}</span>
        </div>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="bar" className="flex items-center">
          <BarChart className="mr-2 h-4 w-4" />
          <span>Bar</span>
        </SelectItem>
        <SelectItem value="line" className="flex items-center">
          <LineChart className=" h-4 w-4" />
          <span>Line</span>
        </SelectItem>
        <SelectItem value="pie" className="flex items-center">
          <PieChart className=" h-4 w-4" />
          <span>Pie</span>
        </SelectItem>
        <SelectItem value="scatter" className="flex items-center">
          <CircleDot className=" h-4 w-4" />
          <span>Scatter</span>
        </SelectItem>
        <SelectItem value="area" className="flex items-center">
          <Activity className="h-4 w-4" />
          <span>Area</span>
        </SelectItem>
        <SelectItem value="radar" className="flex items-center">
          <Activity className=" h-4 w-4 rotate-45" />
          <span>Radar</span>
        </SelectItem>
        <SelectItem value="bubble" className="flex items-center">
          <CircleDot className="h-4 w-4" />
          <span>Bubble</span>
        </SelectItem>
      </SelectContent>
    </Select>
  ), [chartType, getChartIcon]);



  // Memoize the legend to prevent unnecessary re-renders
  const chartLegend = useMemo(() => {
    if (!chartData) return null;

    // Use filtered data if available, otherwise use original data
    const data = filteredChartData || chartData;

    // Check if data is empty or invalid
    if (!data || !data.labels || !data.datasets || data.labels.length === 0 || data.datasets.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 flex flex-wrap gap-1.5 text-xs">
        {/* Display dataset legend items (column headers) */}
        {data.datasets.map((dataset, i) => {
          const isSelected = selectedHeaders.includes(dataset.label);
          const isHeaderFilterActive = showOnlySelectedHeaders && selectedHeaders.length > 0;

          // Calculate average value for this dataset for display
          const avgValue = dataset.data.length > 0
            ? Math.round((dataset.data.reduce((sum, val) => sum + val, 0) / dataset.data.length) * 100) / 100
            : 0;

          // Get color for this dataset
          const datasetColor = Array.isArray(dataset.backgroundColor)
            ? dataset.backgroundColor[0]
            : (dataset.backgroundColor || dataset.borderColor || '#888');

          return (
            <div
              key={`dataset-${i}`}
              className={cn(
                "flex items-center gap-1 px-1.5 py-0.5 rounded-sm cursor-pointer transition-colors",
                isHeaderFilterActive && isSelected ? "bg-primary/20 font-medium" : "bg-muted/70",
                isHeaderFilterActive && !isSelected && "hover:bg-muted"
              )}
              onClick={() => {
                // Toggle this dataset in the selected headers list
                if (selectedHeaders.includes(dataset.label)) {
                  setSelectedHeaders(selectedHeaders.filter(h => h !== dataset.label));
                } else {
                  setSelectedHeaders([...selectedHeaders, dataset.label]);
                }

                // If we're selecting our first header, activate the filter
                if (!showOnlySelectedHeaders && !selectedHeaders.length) {
                  setShowOnlySelectedHeaders(true);
                }
              }}
              title={`Column: ${dataset.label} (avg: ${avgValue}) - Click to ${isSelected ? 'deselect' : 'select'}`}
            >
              <div
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: datasetColor }}
              />
              <span className="truncate max-w-[100px]">
                {dataset.label}
              </span>
              {isHeaderFilterActive && isSelected && (
                <span className="ml-0.5 text-xs">✓</span>
              )}
            </div>
          );
        })}

        {/* Show a reset button when filtering is active */}
        {showOnlySelectedHeaders && selectedHeaders.length > 0 && (
          <button
            className="text-xs px-1.5 py-0.5 rounded-sm bg-muted hover:bg-muted/80 transition-colors"
            onClick={() => {
              setSelectedHeaders([]);
              setShowOnlySelectedHeaders(false);
            }}
            title="Clear header selection"
          >
            Reset
          </button>
        )}
      </div>
    );
  }, [chartData, filteredChartData, selectedHeaders, showOnlySelectedHeaders, setSelectedHeaders, setShowOnlySelectedHeaders]);

  // Toggle between expanded and compact view
  const toggleExpanded = () => {
    if (isExpanded) {
      setIsExpanded(false);
      setChartHeight(120);
      setNodeWidth(300);
    } else {
      setIsExpanded(true);
      setChartHeight(240);
      setNodeWidth(400);
    }
  };

  // Handle resize
  const onResize = (_: any, { width, height }: { width: number; height: number }) => {
    // Minimum sizes
    const minWidth = 250;

    // Calculate new chart height based on container height
    const newChartHeight = Math.max(80, height - 120);

    setNodeWidth(Math.max(minWidth, width));
    setChartHeight(newChartHeight);
  };

  return (
    <div
      className="p-4 border rounded-md shadow-sm bg-background text-foreground transition-shadow hover:shadow-md relative"
      style={{ width: `${nodeWidth}px` }}
    >
      {selected && (
        <NodeResizer
          minWidth={250}
          minHeight={200}
          onResize={onResize}
          lineClassName="border-primary"
          handleClassName="h-3 w-3 bg-primary border-2 border-background"
        />
      )}

      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <BarChart className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Chart Output"}
          </Label>
          {isProcessing && (
            <div className="animate-pulse h-2 w-2 rounded-full bg-amber-500 ml-auto" title="Processing data..." />
          )}
          {chartData && !isProcessing && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Chart ready" />
          )}
        </div>

        {/* Input Indicator */}
        {chartData && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Displaying chart data</span>
          </div>
        )}

        {/* Chart Controls */}
        <div className="flex items-center justify-between">
          <Label className="text-xs text-muted-foreground">
            Chart Type
          </Label>

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={toggleExpanded}
              title={isExpanded ? "Compact view" : "Expanded view"}
            >
              {isExpanded ? (
                <Minimize2 className="h-3.5 w-3.5" />
              ) : (
                <Maximize2 className="h-3.5 w-3.5" />
              )}
            </Button>
            {chartData && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    title="Filter data"
                  >
                    <SlidersHorizontal className="h-3.5 w-3.5" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-3" align="end">
                  <div className="space-y-4">
                    <h4 className="font-medium text-sm">Filter Data</h4>

                    <div className="space-y-2">
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Value Range</span>
                        <span>{minValue} - {maxValue}</span>
                      </div>
                      <Slider
                        min={Math.min(...chartData.datasets[0].data)}
                        max={Math.max(...chartData.datasets[0].data)}
                        step={1}
                        value={[minValue, maxValue]}
                        onValueChange={(value) => {
                          setMinValue(value[0]);
                          setMaxValue(value[1]);
                        }}
                        className="my-2"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs text-muted-foreground">Search Labels</label>
                      <Input
                        placeholder="Search..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="h-8"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs text-muted-foreground">Sort Order</label>
                      <div className="flex gap-2">
                        <Button
                          variant={sortOrder === "asc" ? "default" : "outline"}
                          size="sm"
                          className="text-xs h-7"
                          onClick={() => setSortOrder(sortOrder === "asc" ? "none" : "asc")}
                        >
                          Ascending
                        </Button>
                        <Button
                          variant={sortOrder === "desc" ? "default" : "outline"}
                          size="sm"
                          className="text-xs h-7"
                          onClick={() => setSortOrder(sortOrder === "desc" ? "none" : "desc")}
                        >
                          Descending
                        </Button>
                      </div>
                    </div>

                    {/* Column Header Selection */}
                    {chartData.labels.length > 0 && (
                      <div className="space-y-2 border-t pt-2 mt-2">
                        <div className="flex justify-between items-center">
                          <label className="text-xs text-muted-foreground font-medium">Filter by Column Headers</label>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={cn(
                              "h-6 px-2 text-xs",
                              showOnlySelectedHeaders ? "bg-primary/20" : ""
                            )}
                            onClick={() => setShowOnlySelectedHeaders(!showOnlySelectedHeaders)}
                          >
                            {showOnlySelectedHeaders ? "Filtering Active" : "Filter Off"}
                          </Button>
                        </div>
                        <div className="max-h-32 overflow-y-auto space-y-1 mt-1">
                          {/* Display dataset labels (column headers) for selection */}
                          {chartData?.datasets?.map((dataset, index) => (
                            <div key={`header-${index}`} className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className={cn(
                                  "h-6 w-6 p-0 mr-1",
                                  selectedHeaders.includes(dataset.label) ? "bg-primary/20" : ""
                                )}
                                onClick={() => {
                                  if (selectedHeaders.includes(dataset.label)) {
                                    setSelectedHeaders(selectedHeaders.filter(h => h !== dataset.label));
                                  } else {
                                    setSelectedHeaders([...selectedHeaders, dataset.label]);
                                  }
                                }}
                              >
                                {selectedHeaders.includes(dataset.label) ? "✓" : "○"}
                              </Button>
                              <span
                                className={cn(
                                  "text-xs truncate max-w-[200px] cursor-pointer",
                                  selectedHeaders.includes(dataset.label) ? "font-medium" : ""
                                )}
                                onClick={() => {
                                  if (selectedHeaders.includes(dataset.label)) {
                                    setSelectedHeaders(selectedHeaders.filter(h => h !== dataset.label));
                                  } else {
                                    setSelectedHeaders([...selectedHeaders, dataset.label]);
                                  }
                                }}
                              >
                                {dataset.label}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {chartData?.labels?.length > 0 && (
                      <div className="space-y-2">
                        <label className="text-xs text-muted-foreground">Exclude Data Points</label>
                        <div className="max-h-32 overflow-y-auto space-y-1">
                          {chartData.labels.map((label, i) => {
                            // Calculate average value across all datasets for this label
                            const values = chartData.datasets.map(dataset => dataset.data[i]);
                            const avgValue = values.length > 0
                              ? Math.round((values.reduce((sum, val) => sum + val, 0) / values.length) * 100) / 100
                              : 0;

                            return (
                              <div key={i} className="flex items-center space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={cn(
                                    "h-6 w-6 p-0 mr-1",
                                    excludedItems.includes(i) ? "opacity-30" : "opacity-100"
                                  )}
                                  onClick={() => {
                                    if (excludedItems.includes(i)) {
                                      setExcludedItems(excludedItems.filter(idx => idx !== i));
                                    } else {
                                      setExcludedItems([...excludedItems, i]);
                                    }
                                  }}
                                >
                                  {excludedItems.includes(i) ? "☐" : "☑"}
                                </Button>
                                <span className="text-xs truncate max-w-[200px]">
                                  {label} {avgValue !== 0 ? `(avg: ${avgValue})` : ''}
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full text-xs"
                      onClick={() => {
                        setMinValue(Math.min(...chartData.datasets[0].data));
                        setMaxValue(Math.max(...chartData.datasets[0].data));
                        setSearchTerm("");
                        setExcludedItems([]);
                        setSortOrder("none");
                        setSelectedHeaders([]);
                        setShowOnlySelectedHeaders(false);
                      }}
                    >
                      Reset All Filters
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            )}
            {chartTypeSelector}
          </div>
        </div>

        {error ? (
          <div className="p-3 bg-destructive/10 text-destructive rounded-md text-xs mt-2 flex items-center gap-2">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <span>{error}</span>
          </div>
        ) : chartData ? (
          <div className="mt-2 border border-border rounded-md p-3 bg-muted/50">
            {renderChart()}
            {chartLegend}
          </div>
        ) : (
          <div className="p-6 border border-dashed border-border rounded-md text-center text-muted-foreground text-sm flex flex-col items-center justify-center gap-2">
            <BarChart className="h-8 w-8 text-muted-foreground/50" />
            <span>Waiting for data...</span>
          </div>
        )}
      </div>

      {/* Input handle on the left side */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

ChartOutputNode.displayName = "ChartOutputNode";

export default ChartOutputNode;
