"use client";

import { useState, memo, useEffect } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TextIcon, ArrowRightIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

/**
 * Text Input Node - Standardized Structure
 * Allows users to input text data manually
 */
const TextInputNode = memo(({ data, id }: StandardNodeProps) => {
  // Use data.value from props as the source of truth, fallback to empty string
  const [value, setValue] = useState(data.value || "");
  const [isAnimating, setIsAnimating] = useState(false);
  const [isReceivingInput, setIsReceivingInput] = useState(false);
  const [inputSource, setInputSource] = useState<'manual' | 'connected'>('manual');

  // Sync local state with data.value when it changes from external sources
  useEffect(() => {
    if (data.value !== undefined && data.value !== value) {
      setValue(data.value);
    }
  }, [data.value]);

  // Handle input from connected nodes (target handle)
  useEffect(() => {
    if (data.inputValue !== undefined && data.inputValue !== value && inputSource !== 'manual') {
      console.log(`TextInputNode ${id}: Received input from connected node:`, data.inputValue);
      setValue(data.inputValue);
      setInputSource('connected');
      setIsReceivingInput(true);

      // Show receiving animation
      setTimeout(() => setIsReceivingInput(false), 1000);

      // Also trigger the onChange to propagate the received data
      if (data.onChange) {
        data.onChange(data.inputValue);
      }
    }
  }, [data.inputValue, value, data.onChange, id, inputSource]);

  // Find all connected nodes and update them
  const updateConnectedNodes = (newValue: string) => {
    // This is handled in the parent component
    console.log(`TextInputNode ${id}: updateConnectedNodes called with value:`, newValue);
    console.log(`TextInputNode ${id}: data.onChange exists:`, !!data.onChange);

    if (data.onChange) {
      console.log(`TextInputNode ${id}: Calling data.onChange with value:`, newValue);
      data.onChange(newValue);

      // Add animation effect when data is sent
      setIsAnimating(true);
      setTimeout(() => setIsAnimating(false), 500);
    } else {
      console.warn(`TextInputNode ${id}: No onChange handler found in data`);
    }

    // Also update the node data for workflow execution
    if (data.onDataUpdate) {
      data.onDataUpdate({ value: newValue, text: newValue, data: newValue });
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    console.log(`TextInputNode ${id}: handleChange called with value:`, newValue);

    // Always allow manual input
    setValue(newValue);

    // If user starts typing, switch to manual mode
    if (inputSource === 'connected') {
      console.log(`TextInputNode ${id}: Switching from connected to manual input`);
      setInputSource('manual');
    }

    updateConnectedNodes(newValue);
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <TextIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Text Input"}
          </Label>
          <div className="flex items-center gap-1 ml-auto">
            {inputSource === 'connected' && (
              <div
                className={`h-2 w-2 rounded-full bg-blue-500 ${isReceivingInput ? 'animate-pulse' : ''}`}
                title="Input from connected node - you can still type to override"
              />
            )}
            {value && (
              <div className="h-2 w-2 rounded-full bg-primary" title="Has value" />
            )}
            <div
              className="h-2 w-2 rounded-full bg-green-400"
              title="Manual typing always enabled"
            />
          </div>
        </div>

        {/* Input Field */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor={`text-input-${id}`} className="text-xs text-muted-foreground">
              {inputSource === 'connected' ? 'Text from connected node (editable)' : 'Enter Text'}
            </Label>
            {inputSource === 'connected' && (
              <span className="text-xs text-blue-600 font-medium">✏️ Manual edit allowed</span>
            )}
          </div>
          <Input
            id={`text-input-${id}`}
            value={value}
            onChange={handleChange}
            placeholder={inputSource === 'connected' ? 'Edit connected text or type new text...' : 'Type something...'}
            className={`text-sm ${inputSource === 'connected' ? 'border-blue-300 bg-blue-50/50' : ''}`}
            title={inputSource === 'connected' ? 'You can edit this text even though it came from a connected node' : 'Type your text here'}
          />
        </div>

        {/* Output Indicator */}
        {value && (
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-1 text-muted-foreground">
              <span>Output ready</span>
              <ArrowRightIcon className={`h-3 w-3 ${isAnimating ? 'animate-pulse text-primary' : ''}`} />
            </div>
            <div className="text-muted-foreground">
              {value.length} chars
            </div>
          </div>
        )}

        {/* Connection Status */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <span>Mode:</span>
            <span className={inputSource === 'connected' ? 'text-blue-600' : 'text-muted-foreground'}>
              {inputSource === 'connected' ? 'Connected + Manual' : 'Manual Only'}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <div className={`h-2 w-2 rounded-full ${value ? 'bg-green-500' : 'bg-gray-300'}`} />
            <span>{value ? 'Ready' : 'Empty'}</span>
          </div>
        </div>
      </div>

      {/* Target Handle - Input from other nodes */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className={`w-4 h-4 border-2 border-background transition-colors ${
          inputSource === 'connected'
            ? 'bg-blue-500 hover:bg-blue-600'
            : 'bg-muted hover:bg-muted-foreground/20'
        }`}
        title={inputSource === 'connected' ? 'Receiving input from connected node' : 'Connect a node to provide input'}
      />

      {/* Source Handle - Output to other nodes */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className={`w-4 h-4 border-2 border-background transition-colors ${
          value
            ? 'bg-primary hover:bg-primary/80'
            : 'bg-muted hover:bg-muted-foreground/20'
        }`}
        title={value ? `Output: "${value.substring(0, 50)}${value.length > 50 ? '...' : ''}"` : 'No output yet'}
      />
    </div>
  );
});

TextInputNode.displayName = "TextInputNode";

// Export execution definition for the workflow engine
export const textInputExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    // Priority order: inputs from connected nodes > config values
    let textValue = "";
    let inputSource = "config";

    // Check for input from connected nodes first (target handle)
    if (inputs && Object.keys(inputs).length > 0) {
      // Try common input keys from connected nodes
      textValue = inputs.text || inputs.data || inputs.value || inputs.content || inputs.string || "";
      if (textValue) {
        inputSource = "connected";
        context.log(`Text Input Node receiving input from connected node: "${textValue}"`);
      }
    }

    // Fallback to config values if no connected input
    if (!textValue) {
      textValue = config.value || config.inputValue || config.text || config.data || "";
      inputSource = "config";
    }

    // Log execution with more detail
    context.log(`Text Input Node executing with value: "${textValue}" (length: ${textValue.length}) from ${inputSource}`, 'info');

    // Validate that we have a text value
    if (typeof textValue !== 'string') {
      context.log(`Warning: Text value is not a string, converting from ${typeof textValue}`, 'warn');
    }

    const finalTextValue = String(textValue);

    // Return comprehensive output for connected nodes
    const output = {
      success: true,
      text: finalTextValue,
      data: finalTextValue,
      value: finalTextValue,
      content: finalTextValue,
      string: finalTextValue,
      length: finalTextValue.length,
      isEmpty: finalTextValue.length === 0,
      isNotEmpty: finalTextValue.length > 0,
      wordCount: finalTextValue.trim().split(/\s+/).filter(word => word.length > 0).length,
      lineCount: finalTextValue.split('\n').length,
      trimmed: finalTextValue.trim(),
      uppercase: finalTextValue.toUpperCase(),
      lowercase: finalTextValue.toLowerCase()
    };

    context.log(`Text Input Node output generated with ${Object.keys(output).length} properties`, 'info');

    return output;
  },
  inputs: [
    {
      id: 'text',
      name: 'Text Input',
      type: 'string' as const,
      description: 'Text input from connected nodes',
      required: false
    },
    {
      id: 'data',
      name: 'Data Input',
      type: 'string' as const,
      description: 'Data input from connected nodes',
      required: false
    },
    {
      id: 'value',
      name: 'Value Input',
      type: 'string' as const,
      description: 'Value input from connected nodes',
      required: false
    },
    {
      id: 'content',
      name: 'Content Input',
      type: 'string' as const,
      description: 'Content input from connected nodes',
      required: false
    },
    {
      id: 'string',
      name: 'String Input',
      type: 'string' as const,
      description: 'String input from connected nodes',
      required: false
    }
  ],
  outputs: [
    {
      id: 'text',
      name: 'Text Output',
      type: 'string' as const,
      description: 'The text value entered by the user'
    },
    {
      id: 'data',
      name: 'Data Output',
      type: 'string' as const,
      description: 'Same as text output, for compatibility'
    },
    {
      id: 'value',
      name: 'Value Output',
      type: 'string' as const,
      description: 'Same as text output, for compatibility'
    },
    {
      id: 'content',
      name: 'Content Output',
      type: 'string' as const,
      description: 'Text content for content-based nodes'
    },
    {
      id: 'string',
      name: 'String Output',
      type: 'string' as const,
      description: 'String representation of the text'
    },
    {
      id: 'length',
      name: 'Text Length',
      type: 'number' as const,
      description: 'Length of the text in characters'
    },
    {
      id: 'wordCount',
      name: 'Word Count',
      type: 'number' as const,
      description: 'Number of words in the text'
    },
    {
      id: 'lineCount',
      name: 'Line Count',
      type: 'number' as const,
      description: 'Number of lines in the text'
    },
    {
      id: 'isEmpty',
      name: 'Is Empty',
      type: 'boolean' as const,
      description: 'Whether the text is empty'
    },
    {
      id: 'isNotEmpty',
      name: 'Is Not Empty',
      type: 'boolean' as const,
      description: 'Whether the text is not empty'
    },
    {
      id: 'trimmed',
      name: 'Trimmed Text',
      type: 'string' as const,
      description: 'Text with leading and trailing whitespace removed'
    },
    {
      id: 'uppercase',
      name: 'Uppercase Text',
      type: 'string' as const,
      description: 'Text converted to uppercase'
    },
    {
      id: 'lowercase',
      name: 'Lowercase Text',
      type: 'string' as const,
      description: 'Text converted to lowercase'
    }
  ],
  timeout: 1000, // 1 second timeout
  retryable: false // No need to retry text input
};

export default TextInputNode;
