"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Posi<PERSON> } from "reactflow";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Upload, File, X, FileJson, FileSpreadsheet, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

// Define file types
type FileType = 'unknown' | 'json' | 'csv' | 'text';

// Interface for parsed data
interface ParsedData {
  type: FileType;
  content: string;
  parsed?: any;
  error?: string;
}

/**
 * File Input Node - Standardized Structure
 * Upload and read files with automatic parsing
 */
const FileInputNode = memo(({ data, id }: StandardNodeProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [fileContent, setFileContent] = useState<string>("");
  const [isDragging, setIsDragging] = useState(false);
  const [fileType, setFileType] = useState<FileType>('unknown');
  const [parsedData, setParsedData] = useState<ParsedData | null>(null);
  const [activeTab, setActiveTab] = useState<string>("preview");
  const [error, setError] = useState<string>("");

  // Update connected nodes
  const updateConnectedNodes = (content: string, type: FileType, parsed?: any) => {
    if (data.onChange) {
      // For chart nodes, send the parsed data if it's JSON or CSV
      if (type === 'json' || type === 'csv') {
        if (parsed) {
          // Format the data specifically for chart nodes
          const dataForChart = Array.isArray(parsed)
            ? formatArrayForChart(parsed)
            : JSON.stringify(parsed);

          console.log("File Input Node ID:", id, "Sending to chart:", dataForChart);
          data.onChange(dataForChart);
        } else {
          console.log("File Input Node ID:", id, "Sending content as is:", content);
          data.onChange(content);
        }
      } else {
        console.log("File Input Node ID:", id, "Sending text content:", content);
        data.onChange(content);
      }
    } else {
      console.error("File Input Node ID:", id, "No onChange handler available!");
    }
  };

  // Format array data specifically for chart nodes
  const formatArrayForChart = (data: any[]): string => {
    if (data.length === 0) return "[]";

    // If it's an array of objects, convert to a format the chart can understand
    if (typeof data[0] === 'object') {
      // Get all keys from the first object
      const keys = Object.keys(data[0]);

      // Find a key that might be a label (string) and a key that might be a value (number)
      const labelKey = keys.find(k => typeof data[0][k] === 'string') || keys[0];
      const valueKey = keys.find(k => typeof data[0][k] === 'number') || keys[1] || keys[0];

      // Create a chart-friendly structure
      const chartData = {
        labels: data.map(item => String(item[labelKey])),
        datasets: [{
          label: valueKey,
          data: data.map(item => {
            const val = item[valueKey];
            return typeof val === 'number' ? val : parseFloat(val) || 0;
          })
        }]
      };

      return JSON.stringify(chartData);
    }

    // If it's a simple array of numbers, format it for the chart
    if (data.every(item => typeof item === 'number' || !isNaN(Number(item)))) {
      const chartData = {
        labels: data.map((_, i) => `Item ${i+1}`),
        datasets: [{
          label: 'Values',
          data: data.map(item => typeof item === 'number' ? item : parseFloat(String(item)) || 0)
        }]
      };

      return JSON.stringify(chartData);
    }

    // Default fallback
    return JSON.stringify(data);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      processFile(selectedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) {
      processFile(droppedFile);
    }
  };

  // Detect file type based on extension and content
  const detectFileType = (fileName: string, content: string): FileType => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    // Check by extension
    if (extension === 'json') return 'json';
    if (extension === 'csv') return 'csv';

    // Check by content
    if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
      try {
        JSON.parse(content);
        return 'json';
      } catch (e) {
        // Not valid JSON
      }
    }

    // Check if it looks like CSV (contains commas and newlines)
    if (content.includes(',') && content.includes('\n')) {
      const lines = content.trim().split('\n');
      if (lines.length > 1) {
        // Check if all lines have roughly the same number of commas
        const commasInFirstLine = (lines[0].match(/,/g) || []).length;
        const allLinesHaveSimilarCommas = lines.every(line => {
          const commasInLine = (line.match(/,/g) || []).length;
          return Math.abs(commasInLine - commasInFirstLine) <= 1;
        });

        if (allLinesHaveSimilarCommas) return 'csv';
      }
    }

    return 'text';
  };

  // Parse content based on file type
  const parseContent = (content: string, type: FileType): ParsedData => {
    let parsed = null;
    let error = '';

    try {
      if (type === 'json') {
        parsed = JSON.parse(content);
      } else if (type === 'csv') {
        // Simple CSV parsing
        const lines = content.trim().split('\n');
        const headers = lines[0].split(',').map(h => h.trim());

        if (lines.length > 1) {
          // If there are data rows
          const dataRows = lines.slice(1).map(line => {
            const values = line.split(',').map(v => v.trim());
            const row: Record<string, any> = {};

            headers.forEach((header, index) => {
              // Try to convert to number if possible
              const value = values[index];
              const numValue = Number(value);
              row[header] = isNaN(numValue) ? value : numValue;
            });

            return row;
          });

          parsed = dataRows;
        } else {
          // Only headers, no data
          parsed = [];
        }
      }
    } catch (e) {
      error = e instanceof Error ? e.message : 'Failed to parse file';
    }

    return {
      type,
      content,
      parsed,
      error
    };
  };

  const processFile = (selectedFile: File) => {
    setFile(selectedFile);
    setError('');

    const reader = new FileReader();
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setFileContent(content);

      // Store the content in the node data for connections
      if (data.onChange) {
        // This will update the node's data in the workflow container
        // @ts-ignore - We know this property exists in the workflow container
        data.fileContent = content;
      }

      // Detect file type
      const type = detectFileType(selectedFile.name, content);
      setFileType(type);

      // Parse content
      const parsedData = parseContent(content, type);
      setParsedData(parsedData);

      // Update connected nodes
      if (parsedData.error) {
        setError(parsedData.error);
        updateConnectedNodes(content, 'text');
      } else {
        updateConnectedNodes(content, type, parsedData.parsed);
      }

      // Set active tab based on file type
      if (type === 'json' || type === 'csv') {
        setActiveTab('parsed');
      } else {
        setActiveTab('preview');
      }
    };

    reader.onerror = () => {
      setError('Failed to read file');
    };

    reader.readAsText(selectedFile);
  };

  const clearFile = () => {
    setFile(null);
    setFileContent('');
    setFileType('unknown');
    setParsedData(null);
    setError('');
    updateConnectedNodes('', 'unknown');
  };

  // Add a useEffect to add file accept attribute for better file selection
  useEffect(() => {
    const fileInput = document.getElementById(`file-input-${id}`) as HTMLInputElement;
    if (fileInput) {
      fileInput.accept = ".json,.csv,.txt,text/plain,application/json,text/csv";
    }
  }, [id]);

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[320px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <Upload className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "File Input"}
          </Label>
          {file && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="File loaded" />
          )}
        </div>

        {!file ? (
          <div
            className={cn(
              "border-2 border-dashed rounded-md p-4 text-center cursor-pointer transition-colors",
              isDragging ? "border-primary bg-primary/10" : "border-muted-foreground/20"
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => document.getElementById(`file-input-${id}`)?.click()}
          >
            <input
              id={`file-input-${id}`}
              type="file"
              onChange={handleFileChange}
              className="hidden"
            />
            <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Drag & drop a file here, or click to select
            </p>
          </div>
        ) : (
          <div className="border rounded-md p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                {fileType === 'json' ? (
                  <FileJson className="h-4 w-4 text-blue-500" />
                ) : fileType === 'csv' ? (
                  <FileSpreadsheet className="h-4 w-4 text-green-500" />
                ) : (
                  <File className="h-4 w-4 text-primary" />
                )}
                <span className="text-sm font-medium truncate max-w-[120px]">
                  {file.name}
                </span>
                {fileType !== 'unknown' && (
                  <Badge
                    variant={fileType === 'json' ? 'info' : fileType === 'csv' ? 'success' : 'default'}
                    className="text-[10px] h-4"
                  >
                    {fileType.toUpperCase()}
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={(e) => {
                  e.stopPropagation();
                  clearFile();
                }}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Remove file</span>
              </Button>
            </div>

            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{(file.size / 1024).toFixed(1)} KB</span>
              {parsedData?.parsed && (
                <span>
                  {Array.isArray(parsedData.parsed)
                    ? `${parsedData.parsed.length} rows`
                    : 'Object'}
                </span>
              )}
            </div>

            {error && (
              <div className="mt-2 p-2 bg-destructive/10 text-destructive rounded-md text-xs flex items-center gap-1">
                <AlertCircle className="h-3 w-3 flex-shrink-0" />
                <span>{error}</span>
              </div>
            )}

            {fileContent && (
              <div className="mt-2">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2 h-7">
                    <TabsTrigger value="preview" className="text-xs">Preview</TabsTrigger>
                    <TabsTrigger value="parsed" className="text-xs" disabled={!parsedData?.parsed}>Parsed</TabsTrigger>
                  </TabsList>

                  <TabsContent value="preview" className="mt-1">
                    <div className="p-2 bg-muted rounded-md text-xs max-h-[100px] overflow-y-auto break-words">
                      {fileContent.length > 200
                        ? `${fileContent.substring(0, 200)}...`
                        : fileContent}
                    </div>
                  </TabsContent>

                  <TabsContent value="parsed" className="mt-1">
                    <div className="p-2 bg-muted rounded-md text-xs max-h-[100px] overflow-y-auto">
                      {parsedData?.parsed && (
                        <pre className="text-xs whitespace-pre-wrap">
                          {JSON.stringify(parsedData.parsed, null, 2)}
                        </pre>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

FileInputNode.displayName = "FileInputNode";

// Export execution definition for the workflow engine
export const fileInputExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    // Get file content from config (node data)
    const fileContent = config.fileContent || config.value || "";
    const fileName = config.fileName || "unknown";
    const fileSize = config.fileSize || 0;

    // Log execution
    context.log(`File Input Node executing with file: "${fileName}"`);

    if (!fileContent) {
      throw new Error('No file content available. Please upload a file first.');
    }

    // Detect file type
    const detectFileType = (fileName: string, content: string): string => {
      const extension = fileName.split('.').pop()?.toLowerCase();

      if (extension === 'json') return 'json';
      if (extension === 'csv') return 'csv';

      // Check by content
      if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
        try {
          JSON.parse(content);
          return 'json';
        } catch (e) {
          // Not valid JSON
        }
      }

      // Check if it looks like CSV
      if (content.includes(',') && content.includes('\n')) {
        return 'csv';
      }

      return 'text';
    };

    // Parse content based on type
    const parseContent = (content: string, type: string): any => {
      try {
        if (type === 'json') {
          return JSON.parse(content);
        } else if (type === 'csv') {
          const lines = content.trim().split('\n');
          const headers = lines[0].split(',').map(h => h.trim());

          if (lines.length > 1) {
            return lines.slice(1).map(line => {
              const values = line.split(',').map(v => v.trim());
              const row: Record<string, any> = {};

              headers.forEach((header, index) => {
                const value = values[index];
                const numValue = Number(value);
                row[header] = isNaN(numValue) ? value : numValue;
              });

              return row;
            });
          }
          return [];
        }
        return content;
      } catch (error) {
        context.log(`Failed to parse ${type} content: ${error}`, 'warn');
        return content;
      }
    };

    const fileType = detectFileType(fileName, fileContent);
    const parsedData = parseContent(fileContent, fileType);

    // Calculate file statistics
    const wordCount = fileContent.split(/\s+/).filter((word: string) => word.length > 0).length;
    const lineCount = fileContent.split('\n').length;
    const charCount = fileContent.length;

    // Return comprehensive file data
    return {
      content: fileContent,
      data: fileContent,
      text: fileContent,
      value: fileContent,
      fileName: fileName,
      fileSize: fileSize,
      fileType: fileType,
      parsedData: parsedData,
      isJson: fileType === 'json',
      isCsv: fileType === 'csv',
      isText: fileType === 'text',
      wordCount: wordCount,
      lineCount: lineCount,
      characterCount: charCount,
      isEmpty: fileContent.length === 0,
      hasContent: fileContent.length > 0,
      readAt: new Date().toISOString(),
      preview: fileContent.length > 200 ? fileContent.substring(0, 200) + '...' : fileContent,
      metadata: {
        originalFileName: fileName,
        sizeInBytes: fileSize,
        sizeInKB: Math.round(fileSize / 1024 * 100) / 100,
        encoding: 'utf-8',
        mimeType: fileType === 'json' ? 'application/json' :
                  fileType === 'csv' ? 'text/csv' : 'text/plain'
      }
    };
  },
  inputs: [], // File input nodes don't take inputs from other nodes
  outputs: [
    {
      id: 'content',
      name: 'File Content',
      type: 'string' as const,
      description: 'The raw content of the uploaded file'
    },
    {
      id: 'data',
      name: 'Data Output',
      type: 'string' as const,
      description: 'Same as content, for compatibility'
    },
    {
      id: 'text',
      name: 'Text Output',
      type: 'string' as const,
      description: 'Text representation of the file content'
    },
    {
      id: 'value',
      name: 'Value Output',
      type: 'string' as const,
      description: 'Same as content, for compatibility'
    },
    {
      id: 'fileName',
      name: 'File Name',
      type: 'string' as const,
      description: 'Name of the uploaded file'
    },
    {
      id: 'fileSize',
      name: 'File Size',
      type: 'number' as const,
      description: 'Size of the file in bytes'
    },
    {
      id: 'fileType',
      name: 'File Type',
      type: 'string' as const,
      description: 'Detected type of the file (json, csv, text)'
    },
    {
      id: 'parsedData',
      name: 'Parsed Data',
      type: 'object' as const,
      description: 'Parsed data for JSON/CSV files'
    },
    {
      id: 'isJson',
      name: 'Is JSON',
      type: 'boolean' as const,
      description: 'Whether the file is JSON format'
    },
    {
      id: 'isCsv',
      name: 'Is CSV',
      type: 'boolean' as const,
      description: 'Whether the file is CSV format'
    },
    {
      id: 'isText',
      name: 'Is Text',
      type: 'boolean' as const,
      description: 'Whether the file is plain text'
    },
    {
      id: 'wordCount',
      name: 'Word Count',
      type: 'number' as const,
      description: 'Number of words in the file'
    },
    {
      id: 'lineCount',
      name: 'Line Count',
      type: 'number' as const,
      description: 'Number of lines in the file'
    },
    {
      id: 'characterCount',
      name: 'Character Count',
      type: 'number' as const,
      description: 'Number of characters in the file'
    },
    {
      id: 'preview',
      name: 'Content Preview',
      type: 'string' as const,
      description: 'Preview of the file content (first 200 characters)'
    },
    {
      id: 'metadata',
      name: 'File Metadata',
      type: 'object' as const,
      description: 'Additional metadata about the file'
    }
  ],
  timeout: 10000, // 10 second timeout for file processing
  retryable: false // No need to retry file input
};

export default FileInputNode;
