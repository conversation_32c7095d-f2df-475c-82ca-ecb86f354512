"use client";

import { useState, useEffect, useRef, memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Image, Upload, Sliders, Download, RefreshCw, ArrowLeftIcon, ArrowRightIcon, AlertCircleIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type ImageOperation = "resize" | "crop" | "rotate" | "grayscale" | "blur" | "brightness" | "contrast";

/**
 * Image Processing Node - Standardized Structure
 * Process images with various operations like resize, crop, filters
 */
const ImageProcessingNode = memo(({ data, id }: StandardNodeProps) => {
  const [operation, setOperation] = useState<ImageOperation>("resize");
  const [imageUrl, setImageUrl] = useState<string>("");
  const [processedImageUrl, setProcessedImageUrl] = useState<string>("");
  const [width, setWidth] = useState<number>(300);
  const [height, setHeight] = useState<number>(200);
  const [rotation, setRotation] = useState<number>(0);
  const [intensity, setIntensity] = useState<number>(50);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("input");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Update connected nodes
  const updateConnectedNodes = (content: string) => {
    if (data.onChange) {
      data.onChange(content);
    }
  };

  // Handle input changes from connected nodes
  useEffect(() => {
    if (data.inputValue) {
      try {
        // Check if input is a data URL
        if (data.inputValue.startsWith('data:image')) {
          setImageUrl(data.inputValue);
        } else {
          // Try to parse as JSON with image URL
          const parsedData = JSON.parse(data.inputValue);
          if (parsedData.imageUrl) {
            setImageUrl(parsedData.imageUrl);
          }
        }
      } catch (err) {
        // If not valid JSON or data URL, try as direct URL
        if (data.inputValue.startsWith('http')) {
          setImageUrl(data.inputValue);
        }
      }
    }
  }, [data.inputValue]);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event.target?.result as string;
        setImageUrl(result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Process the image
  const processImage = () => {
    if (!imageUrl) {
      setError("No image to process");
      return;
    }

    setIsProcessing(true);
    setError("");

    try {
      const img = new (window as any).Image();
      img.crossOrigin = "anonymous";

      img.onload = () => {
        if (!canvasRef.current) return;

        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Set canvas dimensions
        let targetWidth = width;
        let targetHeight = height;

        if (operation === "resize") {
          canvas.width = width;
          canvas.height = height;
        } else {
          canvas.width = img.width;
          canvas.height = img.height;
          targetWidth = img.width;
          targetHeight = img.height;
        }

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Apply operations
        switch (operation) {
          case "resize":
            ctx.drawImage(img, 0, 0, width, height);
            break;

          case "crop":
            const cropSize = Math.min(img.width, img.height) * (intensity / 100);
            const cropX = (img.width - cropSize) / 2;
            const cropY = (img.height - cropSize) / 2;
            ctx.drawImage(
              img,
              cropX, cropY, cropSize, cropSize,
              0, 0, canvas.width, canvas.height
            );
            break;

          case "rotate":
            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.rotate((rotation * Math.PI) / 180);
            ctx.drawImage(img, -img.width / 2, -img.height / 2);
            break;

          case "grayscale":
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
              const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
              data[i] = avg;
              data[i + 1] = avg;
              data[i + 2] = avg;
            }

            ctx.putImageData(imageData, 0, 0);
            break;

          case "blur":
            // Simple blur simulation (in a real app, use proper blur algorithm)
            ctx.filter = `blur(${intensity / 10}px)`;
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            ctx.filter = 'none';
            break;

          case "brightness":
            ctx.filter = `brightness(${intensity * 2}%)`;
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            ctx.filter = 'none';
            break;

          case "contrast":
            ctx.filter = `contrast(${intensity * 2}%)`;
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            ctx.filter = 'none';
            break;

          default:
            ctx.drawImage(img, 0, 0);
        }

        // Get processed image as data URL
        const processedDataUrl = canvas.toDataURL('image/jpeg');
        setProcessedImageUrl(processedDataUrl);

        // Update connected nodes with processed image data URL
        updateConnectedNodes(processedDataUrl);

        setIsProcessing(false);
      };

      img.onerror = () => {
        setError("Failed to load image");
        setIsProcessing(false);
      };

      img.src = imageUrl;

    } catch (err) {
      setError("Error processing image");
      setIsProcessing(false);
    }
  };

  // Download processed image
  const downloadImage = () => {
    if (!processedImageUrl) return;

    const link = document.createElement('a');
    link.href = processedImageUrl;
    link.download = `processed-image-${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[320px]">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium flex items-center gap-1">
            <Image className="h-4 w-4" />
            {data.label || "Image Processing"}
          </Label>

          <Select value={operation} onValueChange={(value) => setOperation(value as ImageOperation)}>
            <SelectTrigger className="h-7 w-[120px]">
              <SelectValue placeholder="Operation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="resize">Resize</SelectItem>
              <SelectItem value="crop">Crop</SelectItem>
              <SelectItem value="rotate">Rotate</SelectItem>
              <SelectItem value="grayscale">Grayscale</SelectItem>
              <SelectItem value="blur">Blur</SelectItem>
              <SelectItem value="brightness">Brightness</SelectItem>
              <SelectItem value="contrast">Contrast</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="input">Input</TabsTrigger>
            <TabsTrigger value="output">Output</TabsTrigger>
          </TabsList>

          <TabsContent value="input" className="space-y-2">
            <div className="flex flex-col gap-2">
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Image
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />

                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={processImage}
                  disabled={!imageUrl || isProcessing}
                >
                  {isProcessing ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Sliders className="h-4 w-4 mr-2" />
                  )}
                  Process
                </Button>
              </div>

              {imageUrl && (
                <div className="relative mt-2 border rounded-md overflow-hidden">
                  <img
                    src={imageUrl}
                    alt="Input"
                    className="w-full h-auto max-h-[150px] object-contain bg-muted"
                  />
                </div>
              )}

              {operation === "resize" && (
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">Width</Label>
                    <Input
                      type="number"
                      value={width}
                      onChange={(e) => setWidth(parseInt(e.target.value) || 0)}
                      className="h-8"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Height</Label>
                    <Input
                      type="number"
                      value={height}
                      onChange={(e) => setHeight(parseInt(e.target.value) || 0)}
                      className="h-8"
                    />
                  </div>
                </div>
              )}

              {operation === "rotate" && (
                <div>
                  <Label className="text-xs">Rotation (degrees)</Label>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[rotation]}
                      min={0}
                      max={360}
                      step={1}
                      onValueChange={(value) => setRotation(value[0])}
                    />
                    <span className="text-xs w-8 text-right">{rotation}°</span>
                  </div>
                </div>
              )}

              {(operation === "crop" || operation === "blur" || operation === "brightness" || operation === "contrast") && (
                <div>
                  <Label className="text-xs">
                    {operation === "crop" ? "Crop Amount" :
                     operation === "blur" ? "Blur Intensity" :
                     operation === "brightness" ? "Brightness" : "Contrast"}
                  </Label>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[intensity]}
                      min={0}
                      max={100}
                      step={1}
                      onValueChange={(value) => setIntensity(value[0])}
                    />
                    <span className="text-xs w-8 text-right">{intensity}%</span>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="output" className="space-y-2">
            {processedImageUrl ? (
              <div className="flex flex-col gap-2">
                <div className="relative border rounded-md overflow-hidden">
                  <img
                    src={processedImageUrl}
                    alt="Processed"
                    className="w-full h-auto max-h-[150px] object-contain bg-muted"
                  />
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={downloadImage}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            ) : (
              <div className="flex items-center justify-center h-[150px] border border-dashed rounded-md text-muted-foreground text-sm">
                Process an image to see the result
              </div>
            )}
          </TabsContent>
        </Tabs>

        {error && (
          <div className="p-2 bg-destructive/10 text-destructive rounded-md text-xs mt-2">
            {error}
          </div>
        )}
      </div>

      {/* Hidden canvas for image processing */}
      <canvas ref={canvasRef} className="hidden" />

      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

ImageProcessingNode.displayName = "ImageProcessingNode";

export default ImageProcessingNode;
