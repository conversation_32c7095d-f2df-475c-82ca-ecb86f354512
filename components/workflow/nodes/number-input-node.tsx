"use client";

import { useState, memo, useEffect } from "react";
import { <PERSON><PERSON>, Posi<PERSON> } from "reactflow";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>lider } from "@/components/ui/slider";
import { Hash, ArrowRightIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

/**
 * Number Input Node - Standardized Structure
 * Allows users to input numeric data with slider control
 */
const NumberInputNode = memo(({ data, id }: StandardNodeProps) => {
  // Use data.value from props as the source of truth, fallback to "0"
  const [value, setValue] = useState(data.value || "0");
  const [sliderValue, setSliderValue] = useState([parseFloat(data.value || "0")]);
  const [min, setMin] = useState(0);
  const [max, setMax] = useState(100);
  const [isAnimating, setIsAnimating] = useState(false);

  // Sync local state with data.value when it changes from external sources
  useEffect(() => {
    if (data.value !== undefined && data.value !== value) {
      setValue(data.value);
      setSliderValue([parseFloat(data.value || "0")]);
    }
  }, [data.value]);

  // Update connected nodes
  const updateConnectedNodes = (newValue: string) => {
    if (data.onChange) {
      data.onChange(newValue);

      // Add animation effect when data is sent
      setIsAnimating(true);
      setTimeout(() => setIsAnimating(false), 500);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);

    // Update slider if value is within range
    const numValue = parseFloat(newValue);
    if (!isNaN(numValue) && numValue >= min && numValue <= max) {
      setSliderValue([numValue]);
    }

    updateConnectedNodes(newValue);
  };

  const handleSliderChange = (newValue: number[]) => {
    setSliderValue(newValue);
    const stringValue = newValue[0].toString();
    setValue(stringValue);
    updateConnectedNodes(stringValue);
  };

  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMin = parseFloat(e.target.value);
    if (!isNaN(newMin) && newMin < max) {
      setMin(newMin);

      // Adjust slider value if needed
      if (sliderValue[0] < newMin) {
        const newSliderValue = [newMin];
        setSliderValue(newSliderValue);
        setValue(newMin.toString());
        updateConnectedNodes(newMin.toString());
      }
    }
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMax = parseFloat(e.target.value);
    if (!isNaN(newMax) && newMax > min) {
      setMax(newMax);

      // Adjust slider value if needed
      if (sliderValue[0] > newMax) {
        const newSliderValue = [newMax];
        setSliderValue(newSliderValue);
        setValue(newMax.toString());
        updateConnectedNodes(newMax.toString());
      }
    }
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <Hash className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Number Input"}
          </Label>
          {value && value !== "0" && (
            <div className="h-2 w-2 rounded-full bg-primary ml-auto" title="Has value" />
          )}
        </div>

        {/* Number Input */}
        <div className="space-y-2">
          <Label htmlFor={`number-input-${id}`} className="text-xs text-muted-foreground">
            Enter Number
          </Label>
          <Input
            id={`number-input-${id}`}
            value={value}
            onChange={handleInputChange}
            type="number"
            className="text-sm"
            placeholder="0"
          />
        </div>

        {/* Slider Control */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">
            Slider Control
          </Label>
          <Slider
            value={sliderValue}
            min={min}
            max={max}
            step={1}
            onValueChange={handleSliderChange}
            className="my-2"
          />

          {/* Min/Max Controls */}
          <div className="flex justify-between gap-2">
            <div className="flex items-center gap-1">
              <Label htmlFor={`min-${id}`} className="text-xs">Min:</Label>
              <Input
                id={`min-${id}`}
                value={min}
                onChange={handleMinChange}
                type="number"
                className="w-16 h-6 px-1 py-0 text-xs"
              />
            </div>
            <div className="flex items-center gap-1">
              <Label htmlFor={`max-${id}`} className="text-xs">Max:</Label>
              <Input
                id={`max-${id}`}
                value={max}
                onChange={handleMaxChange}
                type="number"
                className="w-16 h-6 px-1 py-0 text-xs"
              />
            </div>
          </div>
        </div>

        {/* Output Indicator */}
        {value && value !== "0" && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <span>Sending data</span>
            <ArrowRightIcon className={`h-3 w-3 ${isAnimating ? 'animate-pulse' : ''}`} />
          </div>
        )}
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

NumberInputNode.displayName = "NumberInputNode";

// Export execution definition for the workflow engine
export const numberInputExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    // Get the number value from config (node data)
    const numberValue = parseFloat(config.value || config.inputValue || "0");
    const min = parseFloat(config.min || "0");
    const max = parseFloat(config.max || "100");

    // Log execution
    context.log(`Number Input Node executing with value: ${numberValue}`);

    // Validate number
    const isValid = !isNaN(numberValue);
    const isInRange = numberValue >= min && numberValue <= max;

    // Return the number as output with metadata
    return {
      number: numberValue,
      data: numberValue.toString(),
      value: numberValue.toString(),
      text: numberValue.toString(),
      isValid: isValid,
      isInRange: isInRange,
      min: min,
      max: max,
      isInteger: Number.isInteger(numberValue),
      isPositive: numberValue > 0,
      isNegative: numberValue < 0,
      isZero: numberValue === 0,
      absoluteValue: Math.abs(numberValue)
    };
  },
  inputs: [], // Number input nodes don't take inputs from other nodes
  outputs: [
    {
      id: 'number',
      name: 'Number Output',
      type: 'number' as const,
      description: 'The numeric value entered by the user'
    },
    {
      id: 'data',
      name: 'Data Output',
      type: 'string' as const,
      description: 'String representation of the number'
    },
    {
      id: 'value',
      name: 'Value Output',
      type: 'string' as const,
      description: 'Same as data output, for compatibility'
    },
    {
      id: 'text',
      name: 'Text Output',
      type: 'string' as const,
      description: 'Text representation of the number'
    },
    {
      id: 'isValid',
      name: 'Is Valid Number',
      type: 'boolean' as const,
      description: 'Whether the input is a valid number'
    },
    {
      id: 'isInRange',
      name: 'Is In Range',
      type: 'boolean' as const,
      description: 'Whether the number is within min/max range'
    },
    {
      id: 'min',
      name: 'Minimum Value',
      type: 'number' as const,
      description: 'The minimum allowed value'
    },
    {
      id: 'max',
      name: 'Maximum Value',
      type: 'number' as const,
      description: 'The maximum allowed value'
    },
    {
      id: 'isInteger',
      name: 'Is Integer',
      type: 'boolean' as const,
      description: 'Whether the number is an integer'
    },
    {
      id: 'isPositive',
      name: 'Is Positive',
      type: 'boolean' as const,
      description: 'Whether the number is positive'
    },
    {
      id: 'isNegative',
      name: 'Is Negative',
      type: 'boolean' as const,
      description: 'Whether the number is negative'
    },
    {
      id: 'isZero',
      name: 'Is Zero',
      type: 'boolean' as const,
      description: 'Whether the number is zero'
    },
    {
      id: 'absoluteValue',
      name: 'Absolute Value',
      type: 'number' as const,
      description: 'The absolute value of the number'
    }
  ],
  timeout: 1000, // 1 second timeout
  retryable: false // No need to retry number input
};

export default NumberInputNode;
