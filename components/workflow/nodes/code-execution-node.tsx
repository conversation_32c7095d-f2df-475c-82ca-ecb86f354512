"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Posi<PERSON> } from "reactflow";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Play, AlertCircle, CodeIcon, ArrowLeftIcon, ArrowRightIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

/**
 * Code Execution Node - Standardized Structure
 * Execute JavaScript code with input data processing
 */
const CodeExecutionNode = memo(({ data, id }: StandardNodeProps) => {
  const [code, setCode] = useState("// Example: Transform input data\nfunction process(input) {\n  try {\n    // Parse input if it's JSON\n    const data = typeof input === 'string' ? JSON.parse(input) : input;\n    \n    // Return transformed data\n    return { processed: true, data };\n  } catch (e) {\n    return { error: e.message, input };\n  }\n}\n\nreturn process(INPUT);");
  const [output, setOutput] = useState("");
  const [error, setError] = useState("");
  const [autoExecute, setAutoExecute] = useState(false);

  // Update connected nodes
  const updateConnectedNodes = (result: string) => {
    if (data.onChange) {
      data.onChange(result);
    }
  };

  // Execute code when input changes if autoExecute is enabled
  useEffect(() => {
    if (data.inputValue && autoExecute) {
      executeCode(data.inputValue);
    }
  }, [data.inputValue, autoExecute]);

  // Execute the code with the given input
  const executeCode = (input: string = data.inputValue || "") => {
    setError("");

    try {
      // Create a safe execution environment
      const safeEval = (code: string, input: any) => {
        // Define a safe context with limited functionality
        const INPUT = input;

        // Use Function constructor to create a function with controlled scope
        const fn = new Function("INPUT", code);

        // Execute with timeout to prevent infinite loops
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Execution timed out (5s limit)")), 5000);
        });

        return Promise.race([
          Promise.resolve().then(() => fn(INPUT)),
          timeoutPromise
        ]);
      };

      // Execute the code
      safeEval(code, input)
        .then(result => {
          // Format the result
          const formattedResult = typeof result === 'object'
            ? JSON.stringify(result, null, 2)
            : String(result);

          setOutput(formattedResult);
          updateConnectedNodes(formattedResult);
        })
        .catch(err => {
          setError(err.message);
          setOutput("");
          updateConnectedNodes("");
        });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      setOutput("");
      updateConnectedNodes("");
    }
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[360px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <CodeIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Code Execution"}
          </Label>
          {error && (
            <div className="h-2 w-2 rounded-full bg-red-500 ml-auto" title="Execution error" />
          )}
          {output && !error && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Executed successfully" />
          )}
        </div>

        {/* Input Indicator */}
        {data.inputValue && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Processing input data</span>
            <ArrowRightIcon className="h-3 w-3" />
          </div>
        )}

        {/* Code Editor */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs text-muted-foreground">
              JavaScript Code
            </Label>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <Switch
                  id={`auto-execute-${id}`}
                  checked={autoExecute}
                  onCheckedChange={setAutoExecute}
                />
                <Label htmlFor={`auto-execute-${id}`} className="text-xs cursor-pointer">
                  Auto-run
                </Label>
              </div>
              <Button
                size="sm"
                onClick={() => executeCode()}
                className="h-7 px-2 text-xs"
              >
                <Play className="h-3 w-3 mr-1" />
                Run
              </Button>
            </div>
          </div>

          <div className="border rounded-md overflow-hidden">
            <div className="bg-muted px-3 py-1 text-xs font-medium border-b">
              JavaScript
            </div>
            <Textarea
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="min-h-[120px] max-h-[200px] resize-none text-xs font-mono border-0 focus-visible:ring-0"
              spellCheck="false"
              placeholder="// Enter your JavaScript code here..."
            />
          </div>
        </div>

        {/* Input Display */}
        {data.inputValue && (
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Input Data</Label>
            <Textarea
              value={data.inputValue}
              readOnly
              className="min-h-[60px] max-h-[100px] resize-none text-xs font-mono bg-muted/50"
            />
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Error</Label>
            <div className="flex items-start gap-2 p-2 rounded-md bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800">
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <pre className="text-sm text-red-700 dark:text-red-400 whitespace-pre-wrap">{error}</pre>
            </div>
          </div>
        )}

        {/* Output Display */}
        {output && (
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Output</Label>
            <Textarea
              value={output}
              readOnly
              className="min-h-[100px] max-h-[150px] resize-none text-xs font-mono bg-muted/50"
            />
          </div>
        )}
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

CodeExecutionNode.displayName = "CodeExecutionNode";

export default CodeExecutionNode;
