"use client";

import { useState, useEffect, useMemo, memo } from "react";
import { <PERSON><PERSON>, <PERSON>si<PERSON> } from "reactflow";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../../components/ui/table";
import {
  ChevronDown,
  ChevronUp,
  Table as TableIcon,
  AlertCircle,
  Search,
  ArrowUpDown,
  FileJson,
  FileSpreadsheet,
  ArrowLeftIcon
} from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type SortDirection = "asc" | "desc";

interface TableColumn {
  key: string;
  label: string;
  sortable: boolean;
}

/**
 * Table Output Node - Standardized Structure
 * Display data in interactive table format with sorting and filtering
 */
const TableOutputNode = memo(({ data }: StandardNodeProps) => {
  const [tableData, setTableData] = useState<any[]>([]);
  const [columns, setColumns] = useState<TableColumn[]>([]);
  const [error, setError] = useState<string>("");
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(5);

  // Function to parse CSV data with better handling of quoted values
  const parseCSV = (csvText: string): any[] => {
    // Split into lines, handling potential quoted newlines
    const lines: string[] = [];
    let currentLine = '';
    let inQuotes = false;

    for (let i = 0; i < csvText.length; i++) {
      const char = csvText[i];

      if (char === '"') {
        inQuotes = !inQuotes;
        currentLine += char;
      } else if (char === '\n' && !inQuotes) {
        lines.push(currentLine);
        currentLine = '';
      } else {
        currentLine += char;
      }
    }

    // Add the last line if there's content
    if (currentLine) {
      lines.push(currentLine);
    }

    // Parse CSV lines into objects
    if (lines.length < 2) {
      throw new Error("CSV must have at least a header row and one data row");
    }

    // Parse header row
    const headerLine = lines[0];
    const headers: string[] = [];
    let headerStart = 0;
    inQuotes = false;

    for (let i = 0; i <= headerLine.length; i++) {
      const char = headerLine[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if ((char === ',' || i === headerLine.length) && !inQuotes) {
        let header = headerLine.substring(headerStart, i).trim();

        // Remove surrounding quotes if present
        if (header.startsWith('"') && header.endsWith('"')) {
          header = header.substring(1, header.length - 1).replace(/""/g, '"');
        }

        headers.push(header);
        headerStart = i + 1;
      }
    }

    // Parse data rows
    return lines.slice(1).map(line => {
      const values: string[] = [];
      let valueStart = 0;
      inQuotes = false;

      for (let i = 0; i <= line.length; i++) {
        const char = line[i];

        if (char === '"') {
          inQuotes = !inQuotes;
        } else if ((char === ',' || i === line.length) && !inQuotes) {
          let value = line.substring(valueStart, i).trim();

          // Remove surrounding quotes if present
          if (value.startsWith('"') && value.endsWith('"')) {
            value = value.substring(1, value.length - 1).replace(/""/g, '"');
          }

          values.push(value);
          valueStart = i + 1;
        }
      }

      // Create object from headers and values
      return headers.reduce((obj: Record<string, string>, header, i) => {
        obj[header] = values[i] || '';
        return obj;
      }, {});
    });
  };

  // Process input data when it changes
  useEffect(() => {
    if (!data.inputValue) {
      setTableData([]);
      setColumns([]);
      setError("");
      return;
    }

    try {
      // Try to parse the input as JSON
      let parsedData: any;

      try {
        // Check if it's already a valid JSON object
        if (typeof data.inputValue === 'object') {
          parsedData = data.inputValue;
        } else {
          parsedData = JSON.parse(data.inputValue);
        }
      } catch (e) {
        // If not valid JSON, check if it's a CSV-like string
        if (typeof data.inputValue === 'string' &&
            (data.inputValue.includes(',') || data.inputValue.includes('\t'))) {
          try {
            // Try to parse as CSV
            parsedData = parseCSV(data.inputValue);
          } catch (csvError) {
            // If CSV parsing fails, try tab-delimited
            if (data.inputValue.includes('\t')) {
              const tabData = data.inputValue.replace(/\t/g, ',');
              parsedData = parseCSV(tabData);
            } else {
              throw new Error("Invalid CSV format");
            }
          }
        } else {
          throw new Error("Invalid data format");
        }
      }

      // Handle different data formats
      if (Array.isArray(parsedData)) {
        // Array of objects
        if (parsedData.length > 0 && typeof parsedData[0] === 'object') {
          setTableData(parsedData);

          // Extract columns from the first item
          const firstItem = parsedData[0];
          const extractedColumns = Object.keys(firstItem).map(key => ({
            key,
            label: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
            sortable: true
          }));

          setColumns(extractedColumns);
          setError("");
        }
        // Array of arrays (assume first row is headers)
        else if (parsedData.length > 0 && Array.isArray(parsedData[0])) {
          const headers = parsedData[0];
          const rows = parsedData.slice(1).map(row => {
            return headers.reduce((obj, header, i) => {
              obj[header] = row[i];
              return obj;
            }, {} as any);
          });

          setTableData(rows);
          setColumns(headers.map(h => ({
            key: h,
            label: h.charAt(0).toUpperCase() + h.slice(1).replace(/([A-Z])/g, ' $1'),
            sortable: true
          })));
          setError("");
        }
        // Simple array of values
        else {
          const rows = parsedData.map((value, index) => ({ id: index, value }));
          setTableData(rows);
          setColumns([
            { key: 'id', label: 'ID', sortable: true },
            { key: 'value', label: 'Value', sortable: true }
          ]);

          setError("");
        }
      }
      // Object with arrays or nested objects
      else if (typeof parsedData === 'object') {
        // Check if it's an object with a data/results property that is an array
        const dataArray = parsedData.data || parsedData.results || parsedData.items || parsedData.rows;

        if (Array.isArray(dataArray)) {
          setTableData(dataArray);

          if (dataArray.length > 0) {
            const firstItem = dataArray[0];
            const extractedColumns = Object.keys(firstItem).map(key => ({
              key,
              label: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
              sortable: true
            }));

            setColumns(extractedColumns);
          } else {
            setColumns([]);
          }

          setError("");
        } else {
          // Convert object to array of key-value pairs
          const rows = Object.entries(parsedData).map(([key, value]) => ({
            key,
            value: typeof value === 'object' ? JSON.stringify(value) : value
          }));

          setTableData(rows);
          setColumns([
            { key: 'key', label: 'Key', sortable: true },
            { key: 'value', label: 'Value', sortable: true }
          ]);

          setError("");
        }
      } else {
        throw new Error("Invalid data format");
      }
    } catch (err) {
      setError("Failed to parse input data");
      setTableData([]);
      setColumns([]);
    }
  }, [data.inputValue]);

  // Send data to connected nodes when tableData or columns change
  useEffect(() => {
    if (tableData.length > 0 && columns.length > 0 && data.onChange) {
      const chartData = formatDataForChart(tableData, columns);
      if (chartData) {
        data.onChange(JSON.stringify(chartData));
      }
    }
  }, [tableData, columns, data.onChange]);

  // Format table data for chart visualization
  const formatDataForChart = (tableData: any[], columns: TableColumn[]) => {
    if (tableData.length === 0 || columns.length === 0) return null;

    // Find columns with numeric values
    const numericColumns = columns.filter(col => {
      return tableData.some(row => {
        const value = row[col.key];
        return !isNaN(parseFloat(value)) && isFinite(Number(value));
      });
    });

    // If no numeric columns found, return null
    if (numericColumns.length === 0) {
      return null;
    }

    // Find a suitable column for labels (prefer non-numeric)
    const nonNumericColumns = columns.filter(col =>
      !numericColumns.some(numCol => numCol.key === col.key)
    );

    const labelColumn = nonNumericColumns.length > 0 ? nonNumericColumns[0] : columns[0];

    // Create datasets for each numeric column
    const datasets = numericColumns.map(column => {
      return {
        label: column.label,
        data: tableData.map(row => {
          const value = row[column.key];
          return !isNaN(parseFloat(value)) ? parseFloat(value) : 0;
        }),
        backgroundColor: generateRandomColors(tableData.length),
        borderColor: 'rgba(120, 113, 108, 1)', // Stone-500
        borderWidth: 1
      };
    });

    // Create chart data
    return {
      labels: tableData.map(row => String(row[labelColumn.key] || '')),
      datasets: datasets
    };
  };

  // Helper function to generate random colors
  const generateRandomColors = (count: number) => {
    const colors = [];
    const baseColors = [
      'rgba(168, 162, 158, 0.8)', // Stone-400
      'rgba(120, 113, 108, 0.8)', // Stone-500
      'rgba(87, 83, 78, 0.8)',    // Stone-600
      'rgba(68, 64, 60, 0.8)',    // Stone-700
      'rgba(41, 37, 36, 0.8)',    // Stone-800
    ];

    for (let i = 0; i < count; i++) {
      colors.push(baseColors[i % baseColors.length]);
    }

    return colors;
  };

  // Handle sorting
  const handleSort = (columnKey: string) => {
    if (sortColumn === columnKey) {
      // Toggle direction if already sorting by this column
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new sort column and default to ascending
      setSortColumn(columnKey);
      setSortDirection("asc");
    }
  };

  // Filter and sort data
  const processedData = useMemo(() => {
    let result = [...tableData];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(row => {
        return Object.values(row).some(value => {
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(term);
        });
      });
    }

    // Apply sorting
    if (sortColumn) {
      result.sort((a, b) => {
        const aValue = a[sortColumn];
        const bValue = b[sortColumn];

        // Handle different value types
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
        } else {
          const aString = String(aValue || '').toLowerCase();
          const bString = String(bValue || '').toLowerCase();
          return sortDirection === "asc"
            ? aString.localeCompare(bString)
            : bString.localeCompare(aString);
        }
      });
    }

    return result;
  }, [tableData, searchTerm, sortColumn, sortDirection]);

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    return processedData.slice(startIndex, startIndex + rowsPerPage);
  }, [processedData, currentPage, rowsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(processedData.length / rowsPerPage);

  // Export data as CSV
  const exportCSV = () => {
    if (tableData.length === 0 || columns.length === 0) return;

    // Create CSV header
    const header = columns.map(col => `"${col.label}"`).join(',');

    // Create CSV rows
    const rows = tableData.map(row => {
      return columns.map(col => {
        const value = row[col.key];
        // Handle different value types and escape quotes
        if (value === null || value === undefined) return '""';
        if (typeof value === 'object') return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
        return `"${String(value).replace(/"/g, '""')}"`;
      }).join(',');
    }).join('\n');

    // Combine header and rows
    const csv = `${header}\n${rows}`;

    // Create download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `table-export-${Date.now()}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Export data as JSON
  const exportJSON = () => {
    if (tableData.length === 0 || columns.length === 0) return;

    // Create a clean array of objects for export
    const exportData = tableData.map(row => {
      const cleanRow: Record<string, any> = {};
      columns.forEach(col => {
        let value = row[col.key];

        // Try to parse numeric values
        if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
          const num = Number(value);
          if (Number.isInteger(num)) {
            value = parseInt(value);
          } else {
            value = parseFloat(value);
          }
        }

        // Try to parse boolean values
        if (value === 'true' || value === 'false') {
          value = value === 'true';
        }

        cleanRow[col.key] = value;
      });
      return cleanRow;
    });

    // Convert to JSON string with pretty formatting
    const jsonStr = JSON.stringify(exportData, null, 2);

    // Create download link
    const blob = new Blob([jsonStr], { type: 'application/json;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `table-export-${Date.now()}.json`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[400px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <TableIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Table Output"}
          </Label>
          {tableData.length > 0 && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title={`${tableData.length} rows`} />
          )}
        </div>

        {/* Input Indicator */}
        {tableData.length > 0 && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Displaying {tableData.length} rows</span>
          </div>
        )}

        {/* Export Controls */}
        <div className="flex items-center justify-between">
          <Label className="text-xs text-muted-foreground">
            Table Controls
          </Label>
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs"
              onClick={exportCSV}
              disabled={tableData.length === 0}
              title="Export as CSV"
            >
              <FileSpreadsheet className="h-3 w-3 mr-1" />
              CSV
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs"
              onClick={exportJSON}
              disabled={tableData.length === 0}
              title="Export as JSON"
            >
              <FileJson className="h-3 w-3 mr-1" />
              JSON
            </Button>
          </div>
        </div>

        {error ? (
          <div className="p-3 bg-destructive/10 text-destructive rounded-md text-xs mt-2 flex items-center gap-2">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <span>{error}</span>
          </div>
        ) : tableData.length > 0 ? (
          <>
            <div className="flex items-center gap-2 mt-1">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 h-8"
                />
              </div>

              <Select
                value={rowsPerPage.toString()}
                onValueChange={(value) => {
                  setRowsPerPage(parseInt(value));
                  setCurrentPage(1); // Reset to first page when changing rows per page
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder="Rows" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    {columns.map((column) => (
                      <TableHead key={column.key} className="px-2 py-1">
                        {column.sortable ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 px-1 font-medium flex items-center gap-1"
                            onClick={() => handleSort(column.key)}
                          >
                            {column.label}
                            {sortColumn === column.key ? (
                              sortDirection === "asc" ? (
                                <ChevronUp className="h-3 w-3" />
                              ) : (
                                <ChevronDown className="h-3 w-3" />
                              )
                            ) : (
                              <ArrowUpDown className="h-3 w-3 opacity-50" />
                            )}
                          </Button>
                        ) : (
                          column.label
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedData.length > 0 ? (
                    paginatedData.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {columns.map((column) => (
                          <TableCell key={column.key} className="px-2 py-1 text-xs">
                            {typeof row[column.key] === 'object'
                              ? JSON.stringify(row[column.key])
                              : String(row[column.key] !== undefined ? row[column.key] : '')}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="text-center py-4 text-muted-foreground">
                        No results found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-2">
                <div className="text-xs text-muted-foreground">
                  Showing {((currentPage - 1) * rowsPerPage) + 1}-
                  {Math.min(currentPage * rowsPerPage, processedData.length)} of {processedData.length} rows
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    size="icon"
                    variant="outline"
                    className="h-7 w-7"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  >
                    <ChevronDown className="h-4 w-4 rotate-90" />
                  </Button>
                  <span className="text-xs w-12 text-center">
                    {currentPage} / {totalPages}
                  </span>
                  <Button
                    size="icon"
                    variant="outline"
                    className="h-7 w-7"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  >
                    <ChevronDown className="h-4 w-4 -rotate-90" />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-[200px] border border-dashed rounded-md">
            <TableIcon className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">No data to display</p>
            <p className="text-xs text-muted-foreground mt-1">Connect a data source to this node</p>
          </div>
        )}
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

TableOutputNode.displayName = "TableOutputNode";

export default TableOutputNode;
