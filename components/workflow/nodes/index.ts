import { nodeRegistry } from '@/lib/workflow/node-registry';
import { NodeRegistration } from '@/lib/workflow/node-interface';
import {
  TextIcon,
  Hash,
  Upload,
  BarChart,
  Globe,
  Code,
  FilterIcon,
  Database,
  Image as ImageIcon,
  MessageSquare,
  Table as TableIcon,
  Sparkles,
  RefreshCw,
  WandIcon,
  Package,
  RotateCcw,
  Clock
} from 'lucide-react';

// Import execution definitions
import { textInputExecution } from './text-input-node';
import { sampleNewExecution } from './sample-new-node';
import { numberInputExecution } from './number-input-node';
import { textOutputExecution } from './text-output-node';
import { fileInputExecution } from './file-input-node';
import { converterExecution } from './converter-node';
import { nlpExecution } from './nlp-node';
import { loopExecution } from './loop-node';
import { schedulerExecution } from './scheduler-node';

/**
 * Register all basic input nodes (loaded immediately)
 */
const basicNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'textInput',
      label: 'Text Input',
      description: 'Input text data manually',
      icon: TextIcon,
      category: 'input',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./text-input-node'),
    execution: textInputExecution
  },
  {
    metadata: {
      type: 'numberInput',
      label: 'Number Input',
      description: 'Input numeric data',
      icon: Hash,
      category: 'input',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./number-input-node'),
    execution: numberInputExecution
  },
  {
    metadata: {
      type: 'textOutput',
      label: 'Text Output',
      description: 'Display text data',
      icon: TextIcon,
      category: 'output',
      needsOnChangeHandler: false,
      isDynamic: false
    },
    loader: () => import('./text-output-node'),
    execution: textOutputExecution
  },
  {
    metadata: {
      type: 'converter',
      label: 'Converter',
      description: 'Convert between data formats',
      icon: RefreshCw,
      category: 'transform',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./converter-node'),
    execution: converterExecution
  },
  {
    metadata: {
      type: 'installed-node',
      label: 'Installed Node',
      description: 'Node from marketplace',
      icon: Package,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./installed-node'),
    execution: {
      inputs: [
        {
          id: 'input',
          name: 'Input',
          type: 'object',
          required: false,
          description: 'Input data for the installed node'
        },
        {
          id: 'data',
          name: 'Data',
          type: 'object',
          required: false,
          description: 'Data input from connected nodes'
        },
        {
          id: 'value',
          name: 'Value',
          type: 'object',
          required: false,
          description: 'Value input from connected nodes'
        }
      ],
      outputs: [
        {
          id: 'output',
          name: 'Output',
          type: 'object',
          description: 'Output data from the installed node'
        },
        {
          id: 'data',
          name: 'Data',
          type: 'object',
          description: 'Data output for connected nodes'
        },
        {
          id: 'result',
          name: 'Result',
          type: 'object',
          description: 'Result output for connected nodes'
        }
      ],
      execute: async (inputs, config, context) => {
        try {
          context.log(`[InstalledNode] Starting execution with inputs: ${JSON.stringify(inputs)}`, 'info');
          context.log(`[InstalledNode] Config keys: ${Object.keys(config).join(', ')}`, 'debug');

          // Get the node definition from the config (extracted from node.data)
          const nodeDefinition = config.nodeDefinition;

          // Merge workflow inputs with node's input values
          const mergedInputs = {
            ...inputs, // Inputs from connected nodes
            ...(config.inputValues || {}), // Input values from the node UI
          };

          context.log(`[InstalledNode] Merged inputs: ${JSON.stringify(mergedInputs)}`, 'debug');

          if (!nodeDefinition) {
            context.log(`[InstalledNode] No nodeDefinition found in config, using fallback`, 'warn');
            return {
              output: `Processed: ${mergedInputs.input || 'No input'} (Basic execution - no definition)`
            };
          }

          context.log(`[InstalledNode] Found nodeDefinition: ${nodeDefinition.name || nodeDefinition.id}`, 'info');

          // Check if the node definition has an execute function
          if (nodeDefinition.execute && typeof nodeDefinition.execute === 'function') {
            context.log(`[InstalledNode] Executing using nodeDefinition.execute function`, 'info');

            // Execute using the node definition's execute function with merged inputs
            const result = await nodeDefinition.execute(mergedInputs, config);

            context.log(`[InstalledNode] Execution completed with result: ${JSON.stringify(result)}`, 'info');
            return result;
          } else {
            // Try to load and execute the node code
            context.log(`[InstalledNode] No execute function, trying to load node code`, 'info');

            try {
              const response = await fetch(`/api/nodes/code/${nodeDefinition.id}`);
              if (!response.ok) {
                throw new Error('Failed to load node code');
              }

              const codeData = await response.json();

              if (codeData.code) {
                context.log(`[InstalledNode] Executing loaded code`, 'info');

                // Create a safe execution environment
                const executionEnv = {
                  inputs: mergedInputs,
                  config,
                  nodeId: nodeDefinition.id,
                  nodeType: nodeDefinition.type
                };

                // Execute the code in a safe way
                const nodeFunction = new Function('executionEnv', `
                  ${codeData.code}

                  // Try to find and execute the node definition
                  if (typeof nodeDefinition !== 'undefined' && typeof nodeDefinition.execute === 'function') {
                    return nodeDefinition.execute(executionEnv.inputs, executionEnv.config);
                  } else if (typeof executeNode === 'function') {
                    return executeNode(executionEnv);
                  } else {
                    return { output: 'Node executed successfully', inputs: executionEnv.inputs };
                  }
                `);

                const result = await Promise.resolve(nodeFunction(executionEnv));
                context.log(`[InstalledNode] Code execution completed with result: ${JSON.stringify(result)}`, 'info');
                return result;
              } else {
                throw new Error('No executable code found');
              }
            } catch (codeError) {
              context.log(`[InstalledNode] Code execution failed: ${codeError instanceof Error ? codeError.message : 'Unknown error'}`, 'error');

              // Fallback execution
              return {
                output: `Processed: ${mergedInputs.input || 'No input'} (Fallback execution - code failed)`,
                error: 'Code execution failed, used fallback'
              };
            }
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          context.log(`[InstalledNode] Execution error: ${errorMessage}`, 'error');
          throw error;
        }
      }
    }
  }
];

/**
 * Register advanced nodes (loaded dynamically)
 */
const advancedNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'fileInput',
      label: 'File Input',
      description: 'Upload and read files',
      icon: Upload,
      category: 'input',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./file-input-node'),
    execution: fileInputExecution
  },
  {
    metadata: {
      type: 'chartOutput',
      label: 'Chart Output',
      description: 'Display data as charts',
      icon: BarChart,
      category: 'output',
      needsOnChangeHandler: false,
      isDynamic: true
    },
    loader: () => import('./chart-output-node')
  },
  {
    metadata: {
      type: 'tableOutput',
      label: 'Table Output',
      description: 'Display data in table format',
      icon: TableIcon,
      category: 'output',
      needsOnChangeHandler: false,
      isDynamic: true
    },
    loader: () => import('./table-output-node')
  },
  {
    metadata: {
      type: 'transform',
      label: 'Transform',
      description: 'Transform and manipulate data',
      icon: WandIcon,
      category: 'transform',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./transform-node')
  },
  {
    metadata: {
      type: 'filter',
      label: 'Filter',
      description: 'Filter data based on conditions',
      icon: FilterIcon,
      category: 'transform',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./filter-node')
  },
  {
    metadata: {
      type: 'mathOperation',
      label: 'Math Operation',
      description: 'Perform mathematical operations',
      icon: Hash,
      category: 'transform',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./math-operation-node')
  },
  {
    metadata: {
      type: 'apiRequest',
      label: 'API Request',
      description: 'Make HTTP API requests',
      icon: Globe,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./api-request-node')
  },
  {
    metadata: {
      type: 'codeExecution',
      label: 'Code Execution',
      description: 'Execute custom JavaScript code',
      icon: Code,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./code-execution-node')
  }
];

/**
 * Register data and AI nodes
 */
const dataAndAiNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'databaseQuery',
      label: 'Database Query',
      description: 'Query databases with SQL',
      icon: Database,
      category: 'data',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./database-query-node')
  },
  {
    metadata: {
      type: 'imageProcessing',
      label: 'Image Processing',
      description: 'Process and analyze images',
      icon: ImageIcon,
      category: 'ai',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./image-processing-node')
  },
  {
    metadata: {
      type: 'nlp',
      label: 'NLP Analysis',
      description: 'Natural language processing',
      icon: MessageSquare,
      category: 'ai',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./nlp-node'),
    execution: nlpExecution
  },
  {
    metadata: {
      type: 'generateText',
      label: 'Generate Text',
      description: 'Generate text using AI models',
      icon: Sparkles,
      category: 'ai',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./generate-text-node')
  }
];

/**
 * Control flow nodes - loops, scheduling, and automation
 */
const controlFlowNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'loop',
      label: 'Loop Control',
      description: 'Execute iterations with count, while, or foreach loops',
      icon: RotateCcw,
      category: 'control',
      needsOnChangeHandler: true,
      isDynamic: true,
      tags: ['loop', 'iteration', 'control', 'automation']
    },
    loader: () => import('./loop-node'),
    execution: loopExecution
  },
  {
    metadata: {
      type: 'scheduler',
      label: 'Scheduler',
      description: 'Schedule workflow execution with intervals, cron, or one-time',
      icon: Clock,
      category: 'control',
      needsOnChangeHandler: true,
      isDynamic: true,
      tags: ['schedule', 'automation', 'timer', 'cron']
    },
    loader: () => import('./scheduler-node'),
    execution: schedulerExecution
  }
];

/**
 * Marketplace nodes - uploaded and installed nodes
 */
const marketplaceNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'uploaded-package',
      label: 'Uploaded Package',
      description: 'Node from uploaded marketplace package',
      icon: Package,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: true,
      tags: ['marketplace', 'uploaded', 'package']
    },
    loader: () => import('./uploaded-package-node'),
    execution: {
      inputs: [
        {
          id: 'input',
          name: 'Input',
          type: 'object',
          required: false,
          description: 'Input data for the uploaded node'
        },
        {
          id: 'data',
          name: 'Data',
          type: 'object',
          required: false,
          description: 'Data input from connected nodes'
        },
        {
          id: 'value',
          name: 'Value',
          type: 'object',
          required: false,
          description: 'Value input from connected nodes'
        }
      ],
      outputs: [
        {
          id: 'output',
          name: 'Output',
          type: 'object',
          description: 'Output data from the uploaded node'
        },
        {
          id: 'data',
          name: 'Data',
          type: 'object',
          description: 'Data output for connected nodes'
        },
        {
          id: 'result',
          name: 'Result',
          type: 'object',
          description: 'Result output for connected nodes'
        }
      ],
      execute: async (inputs, config, context) => {
        try {
          context.log(`[UploadedPackage] Starting execution with inputs: ${JSON.stringify(inputs)}`, 'info');

          // Get the node definition from the config
          const nodeDefinition = config.nodeDefinition;

          // Merge workflow inputs with node's input values
          const mergedInputs = {
            ...inputs,
            ...(config.inputValues || {}),
          };

          if (!nodeDefinition) {
            context.log(`[UploadedPackage] No nodeDefinition found in config, using fallback`, 'warn');
            return {
              output: `Processed: ${mergedInputs.input || 'No input'} (Basic execution - no definition)`
            };
          }

          // Check if the node definition has an execute function
          if (nodeDefinition.execute && typeof nodeDefinition.execute === 'function') {
            context.log(`[UploadedPackage] Executing using nodeDefinition.execute function`, 'info');

            const result = await nodeDefinition.execute(mergedInputs, config);

            context.log(`[UploadedPackage] Execution completed with result: ${JSON.stringify(result)}`, 'info');
            return result;
          } else {
            // Try to load and execute the node code
            context.log(`[UploadedPackage] No execute function, trying to load node code`, 'info');

            try {
              const response = await fetch(`/api/nodes/code/${nodeDefinition.id}`);
              if (!response.ok) {
                throw new Error('Failed to load node code');
              }

              const codeData = await response.json();

              if (codeData.code) {
                context.log(`[UploadedPackage] Executing loaded code`, 'info');

                // Create a safe execution environment
                const executionEnv = {
                  inputs: mergedInputs,
                  config,
                  nodeId: nodeDefinition.id,
                  nodeType: nodeDefinition.type
                };

                // Execute the code in a safe way
                const nodeFunction = new Function('executionEnv', `
                  ${codeData.code}

                  // Try to find and execute the node definition
                  if (typeof nodeDefinition !== 'undefined' && typeof nodeDefinition.execute === 'function') {
                    return nodeDefinition.execute(executionEnv.inputs, executionEnv.config);
                  } else if (typeof executeNode === 'function') {
                    return executeNode(executionEnv);
                  } else {
                    return { output: 'Node executed successfully', inputs: executionEnv.inputs };
                  }
                `);

                const result = await Promise.resolve(nodeFunction(executionEnv));
                context.log(`[UploadedPackage] Code execution completed with result: ${JSON.stringify(result)}`, 'info');
                return result;
              } else {
                throw new Error('No executable code found');
              }
            } catch (codeError) {
              context.log(`[UploadedPackage] Code execution failed: ${codeError instanceof Error ? codeError.message : 'Unknown error'}`, 'error');

              // Fallback execution
              return {
                output: `Processed: ${mergedInputs.input || 'No input'} (Fallback execution - code failed)`,
                error: 'Code execution failed, used fallback'
              };
            }
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          context.log(`[UploadedPackage] Execution error: ${errorMessage}`, 'error');
          throw error;
        }
      }
    }
  }
];

/**
 * Example/Demo nodes - showing how easy it is to add new nodes
 */
const exampleNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'sampleNew',
      label: 'Sample Node',
      description: 'Example of new modular node architecture',
      icon: Sparkles,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: true,
      tags: ['example', 'demo', 'sample']
    },
    loader: () => import('./sample-new-node'),
    execution: sampleNewExecution
  }
];

/**
 * Register all nodes with the registry
 */
export function registerAllNodes(): void {
  console.log('Registering workflow nodes...');

  // Register basic nodes
  basicNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register advanced nodes
  advancedNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register data and AI nodes
  dataAndAiNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register control flow nodes
  controlFlowNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register marketplace nodes
  marketplaceNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register example nodes
  exampleNodes.forEach(node => nodeRegistry.registerNode(node));

  const stats = nodeRegistry.getStats();
  console.log('Node registration complete:', stats);
}

/**
 * Get all registered nodes (for compatibility)
 */
export function getAllRegisteredNodes() {
  return nodeRegistry.getAllNodes();
}

/**
 * Export the registry for direct access
 */
export { nodeRegistry };
