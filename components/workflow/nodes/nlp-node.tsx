"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Position, useReactFlow } from "reactflow";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, MessageSquare, Sparkles, Tag } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";
import { useNodeExecution } from "../execution-context";

type NlpOperation = "sentiment" | "entities" | "keywords" | "summarize" | "language" | "classify";

/**
 * NLP Node - Standardized Structure
 * Natural Language Processing with sentiment, entities, keywords, and more
 */
const NlpNode = memo(({ data, id }: StandardNodeProps) => {
  const { setNodes } = useReactFlow();
  const [operation, setOperation] = useState<NlpOperation>((data as any).operation || "sentiment");
  const [text, setText] = useState<string>((data as any).text || data.inputValue || "");
  const [result, setResult] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("input");
  const [executionResult, setExecutionResult] = useState<any>(null);

  // Use execution context to get node execution status
  const { result: nodeExecutionResult, isExecuting, isCompleted } = useNodeExecution(id);

  // Update connected nodes
  const updateConnectedNodes = (content: string) => {
    if (data.onChange) {
      data.onChange(content);
    }
  };

  // Update node data with current operation and text for execution
  useEffect(() => {
    // Store current state in React Flow node data for execution engine
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              operation,
              text,
              inputValue: text // Also store as inputValue for compatibility
            }
          };
        }
        return node;
      })
    );
  }, [operation, text, id, setNodes]);

  // Handle input changes from connected nodes
  useEffect(() => {
    if (data.inputValue) {
      setText(data.inputValue);
    }
  }, [data.inputValue]);

  // Handle execution results from workflow engine
  useEffect(() => {
    if ((data as any).executionResult) {
      console.log('[NLP Node] Received execution result from data:', (data as any).executionResult);
      setExecutionResult((data as any).executionResult);
      setResult((data as any).executionResult);
      setActiveTab("output");

      // Update connected nodes with the result
      if ((data as any).executionResult.text || (data as any).executionResult.result) {
        updateConnectedNodes((data as any).executionResult.text || (data as any).executionResult.result);
      }
    }
  }, [(data as any).executionResult]);

  // Handle execution results from execution context
  useEffect(() => {
    if (isCompleted && nodeExecutionResult) {
      console.log('[NLP Node] Received execution result from context:', nodeExecutionResult);
      setExecutionResult(nodeExecutionResult.outputs);
      setResult(nodeExecutionResult.outputs);
      setActiveTab("output");
      setError("");

      // Update connected nodes with the result
      if (nodeExecutionResult.outputs.text || nodeExecutionResult.outputs.result) {
        updateConnectedNodes(nodeExecutionResult.outputs.text || nodeExecutionResult.outputs.result);
      }
    }
  }, [isCompleted, nodeExecutionResult]);

  // Handle execution state from workflow engine
  useEffect(() => {
    if (isExecuting) {
      setError("");
      setActiveTab("output");
    }
  }, [isExecuting]);

  // Process the text (manual execution)
  const processText = async () => {
    if (!text.trim()) {
      setError("No text to process");
      return;
    }

    setIsProcessing(true);
    setError("");
    setResult(null);
    setExecutionResult(null); // Clear previous execution result

    try {
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1000));

      let processedResult;

      switch (operation) {
        case "sentiment":
          // Simulate sentiment analysis
          const sentimentScore = Math.random() * 2 - 1; // -1 to 1
          processedResult = {
            score: sentimentScore.toFixed(2),
            sentiment: sentimentScore > 0.3 ? "positive" :
                      sentimentScore < -0.3 ? "negative" : "neutral",
            confidence: (0.5 + Math.random() * 0.5).toFixed(2)
          };
          break;

        case "entities":
          // Simulate entity extraction
          const entities = [];
          if (text.includes("John")) entities.push({ text: "John", type: "PERSON", confidence: 0.92 });
          if (text.includes("New York")) entities.push({ text: "New York", type: "LOCATION", confidence: 0.95 });
          if (text.includes("Apple")) entities.push({ text: "Apple", type: "ORGANIZATION", confidence: 0.88 });
          if (text.includes("yesterday")) entities.push({ text: "yesterday", type: "DATE", confidence: 0.85 });
          if (text.includes("$100")) entities.push({ text: "$100", type: "MONEY", confidence: 0.97 });

          // Add some random entities based on text length
          if (entities.length < 3 && text.length > 50) {
            const words = text.split(/\s+/);
            for (let i = 0; i < Math.min(3, words.length); i++) {
              if (words[i].length > 4 && Math.random() > 0.7) {
                const types = ["PERSON", "LOCATION", "ORGANIZATION", "DATE", "PRODUCT"];
                entities.push({
                  text: words[i],
                  type: types[Math.floor(Math.random() * types.length)],
                  confidence: (0.7 + Math.random() * 0.3).toFixed(2)
                });
              }
            }
          }

          processedResult = { entities };
          break;

        case "keywords":
          // Simulate keyword extraction
          const words = text.split(/\s+/).filter(w => w.length > 3);
          const keywords = [];

          for (let i = 0; i < Math.min(5, words.length); i++) {
            if (Math.random() > 0.3) {
              keywords.push({
                text: words[Math.floor(Math.random() * words.length)],
                relevance: (0.5 + Math.random() * 0.5).toFixed(2)
              });
            }
          }

          processedResult = { keywords };
          break;

        case "summarize":
          // Simulate text summarization
          const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
          let summary = "";

          if (sentences.length > 2) {
            // Pick 1-2 sentences for the summary
            const numSentences = Math.min(2, Math.ceil(sentences.length / 3));
            const selectedSentences = [];

            for (let i = 0; i < numSentences; i++) {
              const idx = Math.floor(Math.random() * sentences.length);
              selectedSentences.push(sentences[idx]);
            }

            summary = selectedSentences.join(". ") + ".";
          } else {
            summary = text;
          }

          processedResult = {
            summary,
            original_length: text.length,
            summary_length: summary.length,
            reduction_percentage: ((1 - (summary.length / text.length)) * 100).toFixed(1) + "%"
          };
          break;

        case "language":
          // Simulate language detection
          const languages = [
            { language: "English", code: "en", confidence: 0.92 },
            { language: "Spanish", code: "es", confidence: 0.05 },
            { language: "French", code: "fr", confidence: 0.02 },
          ];

          processedResult = { languages };
          break;

        case "classify":
          // Simulate text classification
          const categories = [
            { category: "Technology", confidence: Math.random().toFixed(2) },
            { category: "Business", confidence: Math.random().toFixed(2) },
            { category: "Entertainment", confidence: Math.random().toFixed(2) },
            { category: "Health", confidence: Math.random().toFixed(2) },
            { category: "Science", confidence: Math.random().toFixed(2) }
          ];

          // Sort by confidence
          categories.sort((a, b) => parseFloat(b.confidence) - parseFloat(a.confidence));

          processedResult = { categories };
          break;

        default:
          processedResult = { error: "Unknown operation" };
      }

      setResult(processedResult);
      updateConnectedNodes(JSON.stringify(processedResult, null, 2));

    } catch (err) {
      setError("Error processing text");
      updateConnectedNodes("");
    } finally {
      setIsProcessing(false);
    }
  };

  // Render the result based on operation type
  const renderResult = () => {
    if (!result) return null;

    switch (operation) {
      case "sentiment":
        return (
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Sentiment:</span>
              <span className={`text-sm font-medium ${
                result.sentiment === "positive" ? "text-green-600 dark:text-green-400" :
                result.sentiment === "negative" ? "text-red-600 dark:text-red-400" : "text-muted-foreground"
              }`}>
                {result.sentiment.toUpperCase()}
              </span>
            </div>
            <div className="h-2 bg-muted rounded-full overflow-hidden">
              <div
                className={`h-full ${
                  result.sentiment === "positive" ? "bg-green-500 dark:bg-green-600" :
                  result.sentiment === "negative" ? "bg-red-500 dark:bg-red-600" : "bg-muted-foreground"
                }`}
                style={{ width: `${Math.abs(parseFloat(result.score) * 100)}%`, marginLeft: result.score < 0 ? 0 : "50%" }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Negative</span>
              <span>Neutral</span>
              <span>Positive</span>
            </div>
            <div className="flex items-center justify-between mt-2">
              <span className="text-xs text-muted-foreground">Score: {result.score}</span>
              <span className="text-xs text-muted-foreground">Confidence: {result.confidence}</span>
            </div>
          </div>
        );

      case "entities":
        return (
          <div className="space-y-2">
            {result.entities.length > 0 ? (
              result.entities.map((entity: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-1 bg-muted/50 rounded border">
                  <div className="flex items-center gap-1">
                    <Tag className="h-3 w-3 text-primary" />
                    <span className="text-sm font-medium">{entity.text}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs px-1.5 py-0.5 bg-muted rounded-full">{entity.type}</span>
                    <span className="text-xs text-muted-foreground">{entity.confidence}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-muted-foreground text-center py-2">No entities detected</div>
            )}
          </div>
        );

      case "keywords":
        return (
          <div className="space-y-1">
            {result.keywords.length > 0 ? (
              result.keywords.map((keyword: any, index: number) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">{keyword.text}</span>
                  <div className="flex items-center gap-1">
                    <div className="h-1.5 bg-primary rounded-full" style={{ width: `${parseFloat(keyword.relevance) * 50}px` }}></div>
                    <span className="text-xs text-muted-foreground">{keyword.relevance}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-muted-foreground text-center py-2">No keywords extracted</div>
            )}
          </div>
        );

      case "summarize":
        return (
          <div className="space-y-2">
            <div className="p-2 bg-muted/50 border rounded-md text-sm">
              {result.summary}
            </div>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Original: {result.original_length} chars</span>
              <span>Summary: {result.summary_length} chars</span>
              <span>Reduction: {result.reduction_percentage}</span>
            </div>
          </div>
        );

      case "language":
        return (
          <div className="space-y-2">
            {result.languages.map((lang: any, index: number) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <span className="text-sm font-medium">{lang.language}</span>
                  <span className="text-xs text-muted-foreground">({lang.code})</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="h-1.5 bg-primary rounded-full" style={{ width: `${lang.confidence * 50}px` }}></div>
                  <span className="text-xs text-muted-foreground">{lang.confidence}</span>
                </div>
              </div>
            ))}
          </div>
        );

      case "classify":
        return (
          <div className="space-y-2">
            {result.categories.map((category: any, index: number) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm">{category.category}</span>
                <div className="flex items-center gap-1">
                  <div className="h-1.5 bg-primary rounded-full" style={{ width: `${parseFloat(category.confidence) * 50}px` }}></div>
                  <span className="text-xs text-muted-foreground">{category.confidence}</span>
                </div>
              </div>
            ))}
          </div>
        );

      default:
        return <pre className="text-xs overflow-auto p-2 bg-muted/50 rounded-md">{JSON.stringify(result, null, 2)}</pre>;
    }
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[320px]">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium flex items-center gap-1">
            <MessageSquare className="h-4 w-4" />
            {data.label || "NLP Analysis"}
          </Label>

          <Select value={operation} onValueChange={(value) => setOperation(value as NlpOperation)}>
            <SelectTrigger className="h-7 w-[120px]">
              <SelectValue placeholder="Operation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sentiment">Sentiment</SelectItem>
              <SelectItem value="entities">Entities</SelectItem>
              <SelectItem value="keywords">Keywords</SelectItem>
              <SelectItem value="summarize">Summarize</SelectItem>
              <SelectItem value="language">Language</SelectItem>
              <SelectItem value="classify">Classify</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="input">Input</TabsTrigger>
            <TabsTrigger value="output" className="relative">
              Output
              {(isExecuting || isProcessing) && (
                <div className="absolute -top-1 -right-1 h-2 w-2 bg-primary rounded-full animate-pulse" />
              )}
              {isCompleted && result && (
                <div className="absolute -top-1 -right-1 h-2 w-2 bg-green-500 rounded-full" />
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="input" className="space-y-2">
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter text to analyze..."
              className="min-h-[120px] resize-none"
            />

            <Button
              size="sm"
              onClick={processText}
              disabled={isProcessing || isExecuting || !text.trim()}
              className="w-full"
            >
              {isProcessing ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : isExecuting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Sparkles className="mr-2 h-4 w-4" />
              )}
              {isProcessing ? "Processing..." : isExecuting ? "Executing..." : "Analyze Text"}
            </Button>

            {isExecuting && (
              <div className="text-xs text-muted-foreground text-center">
                Workflow execution in progress...
              </div>
            )}
          </TabsContent>

          <TabsContent value="output" className="min-h-[150px]">
            {(isProcessing || isExecuting) ? (
              <div className="flex flex-col items-center justify-center h-full gap-2">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
                <div className="text-xs text-muted-foreground">
                  {isExecuting ? "Workflow executing..." : "Processing..."}
                </div>
              </div>
            ) : result ? (
              <div className="p-2">
                {(executionResult || isCompleted) && (
                  <div className="mb-2 p-1 bg-primary/10 text-primary text-xs rounded border">
                    ✓ Executed by workflow engine
                  </div>
                )}
                {renderResult()}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground text-sm">
                {text.trim() ? "Click 'Analyze Text' or run workflow to see results" : "Enter text to analyze"}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {error && (
          <div className="p-2 bg-destructive/10 text-destructive rounded-md text-xs mt-2">
            {error}
          </div>
        )}
      </div>

      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

NlpNode.displayName = "NlpNode";

export default NlpNode;

// Export execution definition for the workflow engine
export const nlpExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    console.log('[NLP Execution] Starting execution with inputs:', inputs, 'config:', config);

    // Extract input text from various sources
    const inputText = inputs.text || inputs.data || inputs.value || config.inputValue || config.text || "";
    const operation = config.operation || "sentiment";

    console.log('[NLP Execution] Extracted text:', inputText, 'operation:', operation);

    // Log execution start
    context.log(`NLP Node executing ${operation} analysis on: "${inputText.substring(0, 50)}${inputText.length > 50 ? '...' : ''}"`);

    if (!inputText.trim()) {
      console.log('[NLP Execution] No text provided, throwing error');
      throw new Error("No text provided for NLP analysis");
    }

    // Simulate processing time based on text length and operation
    const processingTime = Math.min(100 + (inputText.length * 2), 3000);
    await new Promise(resolve => setTimeout(resolve, processingTime));

    let result;

    try {
      switch (operation) {
        case "sentiment":
          // Simulate sentiment analysis
          const sentimentScore = Math.random() * 2 - 1; // -1 to 1
          result = {
            score: parseFloat(sentimentScore.toFixed(2)),
            sentiment: sentimentScore > 0.3 ? "positive" :
                      sentimentScore < -0.3 ? "negative" : "neutral",
            confidence: parseFloat((0.5 + Math.random() * 0.5).toFixed(2)),
            operation: "sentiment"
          };
          break;

        case "entities":
          // Simulate entity extraction
          const entities: any[] = [];
          const entityPatterns = [
            { pattern: /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g, type: "PERSON" },
            { pattern: /\b[A-Z][a-z]+(?: [A-Z][a-z]+)*\b/g, type: "LOCATION" },
            { pattern: /\$\d+(?:,\d{3})*(?:\.\d{2})?/g, type: "MONEY" },
            { pattern: /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g, type: "DATE" },
            { pattern: /\b(?:yesterday|today|tomorrow)\b/gi, type: "DATE" }
          ];

          entityPatterns.forEach(({ pattern, type }) => {
            const matches = inputText.match(pattern);
            if (matches) {
              matches.forEach((match: string) => {
                entities.push({
                  text: match,
                  type,
                  confidence: parseFloat((0.7 + Math.random() * 0.3).toFixed(2))
                });
              });
            }
          });

          result = {
            entities,
            entityCount: entities.length,
            operation: "entities"
          };
          break;

        case "keywords":
          // Simulate keyword extraction
          const words = inputText.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter((w: string) => w.length > 3);

          const uniqueWords = [...new Set(words)];
          const keywords = [];

          for (let i = 0; i < Math.min(8, uniqueWords.length); i++) {
            if (Math.random() > 0.2) {
              keywords.push({
                text: uniqueWords[Math.floor(Math.random() * uniqueWords.length)],
                relevance: parseFloat((0.4 + Math.random() * 0.6).toFixed(2))
              });
            }
          }

          // Sort by relevance
          keywords.sort((a, b) => b.relevance - a.relevance);

          result = {
            keywords: keywords.slice(0, 5),
            keywordCount: keywords.length,
            operation: "keywords"
          };
          break;

        case "summarize":
          // Simulate text summarization
          const sentences = inputText.split(/[.!?]+/).filter((s: string) => s.trim().length > 0);
          let summary = "";

          if (sentences.length > 2) {
            // Pick 1-2 sentences for the summary
            const numSentences = Math.min(2, Math.ceil(sentences.length / 3));
            const selectedSentences: string[] = [];

            for (let i = 0; i < numSentences; i++) {
              const idx = Math.floor(Math.random() * sentences.length);
              if (!selectedSentences.includes(sentences[idx])) {
                selectedSentences.push(sentences[idx].trim());
              }
            }

            summary = selectedSentences.join(". ") + ".";
          } else {
            summary = inputText;
          }

          result = {
            summary,
            originalLength: inputText.length,
            summaryLength: summary.length,
            reductionPercentage: parseFloat(((1 - (summary.length / inputText.length)) * 100).toFixed(1)),
            operation: "summarize"
          };
          break;

        case "language":
          // Simulate language detection
          const languages = [
            { language: "English", code: "en", confidence: 0.95 },
            { language: "Spanish", code: "es", confidence: 0.03 },
            { language: "French", code: "fr", confidence: 0.02 }
          ];

          result = {
            detectedLanguage: languages[0],
            allLanguages: languages,
            operation: "language"
          };
          break;

        case "classify":
          // Simulate text classification
          const categories = [
            { category: "Technology", confidence: Math.random() },
            { category: "Business", confidence: Math.random() },
            { category: "Entertainment", confidence: Math.random() },
            { category: "Health", confidence: Math.random() },
            { category: "Science", confidence: Math.random() }
          ].map(cat => ({
            ...cat,
            confidence: parseFloat(cat.confidence.toFixed(2))
          }));

          // Sort by confidence
          categories.sort((a, b) => b.confidence - a.confidence);

          result = {
            categories,
            topCategory: categories[0],
            operation: "classify"
          };
          break;

        default:
          throw new Error(`Unknown NLP operation: ${operation}`);
      }

      // Log successful completion
      context.log(`NLP ${operation} analysis completed successfully`);

      const outputs = {
        // Primary outputs
        result: result,
        data: JSON.stringify(result, null, 2),
        text: typeof result === 'object' && result.summary ? result.summary : JSON.stringify(result),

        // Specific outputs based on operation
        sentiment: operation === 'sentiment' ? result.sentiment : null,
        score: operation === 'sentiment' ? result.score : null,
        entities: operation === 'entities' ? result.entities : null,
        keywords: operation === 'keywords' ? result.keywords : null,
        summary: operation === 'summarize' ? result.summary : null,
        language: operation === 'language' ? result.detectedLanguage?.language : null,
        categories: operation === 'classify' ? result.categories : null,

        // Metadata outputs
        operation: operation,
        inputLength: inputText.length,
        processingTime: processingTime,
        processedAt: new Date().toISOString(),

        // Boolean outputs
        isPositive: operation === 'sentiment' ? result.sentiment === 'positive' : null,
        isNegative: operation === 'sentiment' ? result.sentiment === 'negative' : null,
        isNeutral: operation === 'sentiment' ? result.sentiment === 'neutral' : null,
        hasEntities: operation === 'entities' ? (result.entities?.length || 0) > 0 : null,
        hasKeywords: operation === 'keywords' ? (result.keywords?.length || 0) > 0 : null
      };

      console.log('[NLP Execution] Returning outputs:', outputs);
      return outputs;

    } catch (error) {
      context.log(`NLP execution failed: ${(error as Error).message}`, 'error');
      throw error;
    }
  },

  // Input schema
  inputs: [
    { id: 'text', name: 'Text Input', type: 'string' as const, required: true, description: 'Text to analyze' },
    { id: 'data', name: 'Data Input', type: 'string' as const, required: false, description: 'Alternative text input' },
    { id: 'value', name: 'Value Input', type: 'string' as const, required: false, description: 'Alternative text input' }
  ],

  // Output schema
  outputs: [
    { id: 'result', name: 'Analysis Result', type: 'object' as const, description: 'Complete analysis result object' },
    { id: 'data', name: 'JSON Data', type: 'string' as const, description: 'Result as formatted JSON string' },
    { id: 'text', name: 'Text Output', type: 'string' as const, description: 'Primary text result' },
    { id: 'sentiment', name: 'Sentiment', type: 'string' as const, description: 'Sentiment classification (positive/negative/neutral)' },
    { id: 'score', name: 'Sentiment Score', type: 'number' as const, description: 'Sentiment score (-1 to 1)' },
    { id: 'entities', name: 'Entities', type: 'array' as const, description: 'Extracted entities' },
    { id: 'keywords', name: 'Keywords', type: 'array' as const, description: 'Extracted keywords' },
    { id: 'summary', name: 'Summary', type: 'string' as const, description: 'Text summary' },
    { id: 'language', name: 'Language', type: 'string' as const, description: 'Detected language' },
    { id: 'categories', name: 'Categories', type: 'array' as const, description: 'Classification categories' },
    { id: 'operation', name: 'Operation', type: 'string' as const, description: 'NLP operation performed' },
    { id: 'inputLength', name: 'Input Length', type: 'number' as const, description: 'Length of input text' },
    { id: 'processingTime', name: 'Processing Time', type: 'number' as const, description: 'Processing time in milliseconds' },
    { id: 'processedAt', name: 'Processed At', type: 'string' as const, description: 'Processing timestamp' },
    { id: 'isPositive', name: 'Is Positive', type: 'boolean' as const, description: 'True if sentiment is positive' },
    { id: 'isNegative', name: 'Is Negative', type: 'boolean' as const, description: 'True if sentiment is negative' },
    { id: 'isNeutral', name: 'Is Neutral', type: 'boolean' as const, description: 'True if sentiment is neutral' },
    { id: 'hasEntities', name: 'Has Entities', type: 'boolean' as const, description: 'True if entities were found' },
    { id: 'hasKeywords', name: 'Has Keywords', type: 'boolean' as const, description: 'True if keywords were found' }
  ],

  // Execution configuration
  timeout: 15000, // 15 seconds timeout for complex text processing
  retryable: true,
  maxRetries: 2
};
