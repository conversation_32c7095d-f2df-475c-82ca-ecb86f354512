"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Posi<PERSON> } from "reactflow";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Loader2, Database, RefreshCw, GlobeIcon, ArrowLeftIcon, ArrowRightIcon, AlertCircleIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";
import apiCache from "@/lib/api-cache";

type HttpMethod = "GET" | "POST" | "PUT" | "DELETE";

/**
 * API Request Node - Standardized Structure
 * Make HTTP requests to external APIs with caching support
 */
const ApiRequestNode = memo(({ data, id }: StandardNodeProps) => {
  const [url, setUrl] = useState("https://jsonplaceholder.typicode.com/todos/1");
  const [method, setMethod] = useState<HttpMethod>("GET");
  const [requestBody, setRequestBody] = useState("");
  const [response, setResponse] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [autoFetch, setAutoFetch] = useState(false);
  const [useCache, setUseCache] = useState(true);
  const [isCached, setIsCached] = useState(false);

  // Update connected nodes
  const updateConnectedNodes = (content: string) => {
    if (data.onChange) {
      data.onChange(content);
    }
  };

  // Handle input changes
  useEffect(() => {
    if (data.inputValue && autoFetch) {
      // If we receive input and autoFetch is enabled, use it as the URL
      setUrl(data.inputValue);
      fetchData();
    }
  }, [data.inputValue, autoFetch]);

  const fetchData = async (forceRefresh = false) => {
    setIsLoading(true);
    setError("");
    setIsCached(false);

    try {
      // Generate a cache key based on the request parameters
      const cacheKey = `${method}:${url}:${requestBody || ''}`;

      // Check if we have a cached response and should use it
      if (useCache && !forceRefresh) {
        const cachedData = apiCache.get(cacheKey);
        if (cachedData) {
          setResponse(cachedData);
          updateConnectedNodes(cachedData);
          setIsCached(true);
          setIsLoading(false);
          return;
        }
      }

      // No cache hit or cache disabled, make the actual request
      const options: RequestInit = {
        method,
        headers: {
          "Content-Type": "application/json",
        },
      };

      if (method !== "GET" && requestBody) {
        options.body = requestBody;
      }

      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      const formattedResponse = JSON.stringify(data, null, 2);

      // Store in cache if caching is enabled
      if (useCache) {
        apiCache.set(cacheKey, formattedResponse);
      }

      setResponse(formattedResponse);
      updateConnectedNodes(formattedResponse);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      setResponse("");
      updateConnectedNodes("");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[320px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <GlobeIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "API Request"}
          </Label>
          {isLoading && (
            <div className="animate-pulse h-2 w-2 rounded-full bg-amber-500 ml-auto" title="Loading..." />
          )}
          {response && !isLoading && !error && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Request successful" />
          )}
          {error && (
            <div className="h-2 w-2 rounded-full bg-red-500 ml-auto" title="Request failed" />
          )}
        </div>

        {/* Input Indicator */}
        {data.inputValue && autoFetch && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Auto-fetching from input</span>
            <ArrowRightIcon className="h-3 w-3" />
          </div>
        )}

        {/* HTTP Method & URL */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">
            HTTP Request
          </Label>
          <div className="flex gap-2">
            <Select
              value={method}
              onValueChange={(value) => setMethod(value as HttpMethod)}
            >
              <SelectTrigger className="w-[100px] text-sm">
                <SelectValue placeholder="Method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="GET">GET</SelectItem>
                <SelectItem value="POST">POST</SelectItem>
                <SelectItem value="PUT">PUT</SelectItem>
                <SelectItem value="DELETE">DELETE</SelectItem>
              </SelectContent>
            </Select>

            <Input
              id={`api-url-${id}`}
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://api.example.com"
              className="flex-1 text-sm"
            />
          </div>
        </div>

        {/* Request Body (for POST/PUT) */}
        {(method === "POST" || method === "PUT") && (
          <div className="space-y-2">
            <Label htmlFor={`request-body-${id}`} className="text-xs text-muted-foreground">
              Request Body (JSON)
            </Label>
            <Textarea
              id={`request-body-${id}`}
              value={requestBody}
              onChange={(e) => setRequestBody(e.target.value)}
              placeholder='{"key": "value"}'
              className="min-h-[80px] max-h-[120px] resize-none text-sm"
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">
            Actions
          </Label>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              onClick={() => fetchData(false)}
              disabled={isLoading}
              className="flex-1 text-sm"
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isLoading ? "Fetching..." : "Fetch Data"}
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => fetchData(true)}
              disabled={isLoading}
              title="Force refresh (bypass cache)"
              className="px-3"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Settings */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">
            Settings
          </Label>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Switch
                id={`auto-fetch-${id}`}
                checked={autoFetch}
                onCheckedChange={setAutoFetch}
              />
              <Label htmlFor={`auto-fetch-${id}`} className="text-xs cursor-pointer">
                Auto-fetch
              </Label>
            </div>

            <div className="flex items-center gap-2">
              <Switch
                id={`use-cache-${id}`}
                checked={useCache}
                onCheckedChange={setUseCache}
              />
              <Label htmlFor={`use-cache-${id}`} className="text-xs cursor-pointer">
                Use cache
              </Label>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Error</Label>
            <div className="flex items-center gap-2 p-2 rounded-md bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800">
              <AlertCircleIcon className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-700 dark:text-red-400">{error}</span>
            </div>
          </div>
        )}

        {/* Response Display */}
        {response && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs text-muted-foreground">Response</Label>
              {isCached && (
                <div className="flex items-center gap-1">
                  <Database className="h-3 w-3 text-primary" />
                  <span className="text-xs text-primary">Cached</span>
                </div>
              )}
            </div>
            <Textarea
              value={response}
              readOnly
              className="min-h-[120px] max-h-[200px] resize-none text-xs font-mono bg-muted/50"
            />
          </div>
        )}
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

ApiRequestNode.displayName = "ApiRequestNode";

export default ApiRequestNode;
