"use client";

import { memo, useState, useEffect } from "react";
import { <PERSON><PERSON>, Position, useReactFlow } from "reactflow";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RotateCcw, Play, Square, Settings } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type LoopType = "count" | "while" | "foreach";

const LoopNode = memo(({ data, id }: StandardNodeProps) => {
  const { setNodes } = useReactFlow();
  const [loopType, setLoopType] = useState<LoopType>((data as any).loopType || "count");
  const [iterations, setIterations] = useState<number>((data as any).iterations || 3);
  const [condition, setCondition] = useState<string>((data as any).condition || "");
  const [arrayInput, setArrayInput] = useState<string>((data as any).arrayInput || "");
  const [currentIteration, setCurrentIteration] = useState<number>(0);
  const [isLooping, setIsLooping] = useState<boolean>(false);
  const [results, setResults] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<string>("config");

  // Update node data when settings change
  useEffect(() => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? {
              ...node,
              data: {
                ...node.data,
                loopType,
                iterations,
                condition,
                arrayInput,
                currentIteration,
                results
              }
            }
          : node
      )
    );
  }, [loopType, iterations, condition, arrayInput, currentIteration, results, id, setNodes]);

  // Handle input changes from connected nodes
  useEffect(() => {
    if (data.inputValue) {
      // Process input for loop execution
      console.log('[Loop Node] Received input:', data.inputValue);
    }
  }, [data.inputValue]);

  const startLoop = async () => {
    setIsLooping(true);
    setCurrentIteration(0);
    setResults([]);
    setActiveTab("output");

    try {
      switch (loopType) {
        case "count":
          await executeCountLoop();
          break;
        case "while":
          await executeWhileLoop();
          break;
        case "foreach":
          await executeForeachLoop();
          break;
      }
    } catch (error) {
      console.error('[Loop Node] Execution error:', error);
    } finally {
      setIsLooping(false);
    }
  };

  const executeCountLoop = async () => {
    for (let i = 0; i < iterations; i++) {
      setCurrentIteration(i + 1);

      // Simulate processing
      await new Promise(resolve => setTimeout(resolve, 500));

      const result = {
        iteration: i + 1,
        input: data.inputValue || `Iteration ${i + 1}`,
        output: `Processed iteration ${i + 1}`,
        timestamp: new Date().toISOString()
      };

      setResults(prev => [...prev, result]);

      // Update connected nodes with current iteration result
      updateConnectedNodes(result);
    }
  };

  const executeWhileLoop = async () => {
    let iteration = 0;
    const maxIterations = 10; // Safety limit

    while (iteration < maxIterations && evaluateCondition(condition, iteration)) {
      iteration++;
      setCurrentIteration(iteration);

      await new Promise(resolve => setTimeout(resolve, 500));

      const result = {
        iteration,
        condition: condition,
        input: data.inputValue || `While iteration ${iteration}`,
        output: `While processed iteration ${iteration}`,
        timestamp: new Date().toISOString()
      };

      setResults(prev => [...prev, result]);
      updateConnectedNodes(result);
    }
  };

  const executeForeachLoop = async () => {
    try {
      const array = JSON.parse(arrayInput || '[]');

      for (let i = 0; i < array.length; i++) {
        setCurrentIteration(i + 1);

        await new Promise(resolve => setTimeout(resolve, 500));

        const result = {
          iteration: i + 1,
          item: array[i],
          input: data.inputValue || array[i],
          output: `Processed item: ${JSON.stringify(array[i])}`,
          timestamp: new Date().toISOString()
        };

        setResults(prev => [...prev, result]);
        updateConnectedNodes(result);
      }
    } catch (error) {
      console.error('[Loop Node] Invalid array input:', error);
    }
  };

  const evaluateCondition = (condition: string, iteration: number): boolean => {
    try {
      // Simple condition evaluation (replace 'i' with current iteration)
      const evalCondition = condition.replace(/i/g, iteration.toString());
      return eval(evalCondition);
    } catch {
      return false;
    }
  };

  const updateConnectedNodes = (result: any) => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? {
              ...node,
              data: {
                ...node.data,
                outputValue: result,
                lastResult: result
              }
            }
          : node
      )
    );
  };

  const stopLoop = () => {
    setIsLooping(false);
  };

  return (
    <div className="bg-background border-2 border-border rounded-lg shadow-sm min-w-[300px]">
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      <div className="p-3">
        <div className="flex items-center gap-2 mb-3">
          <div className="p-1.5 bg-orange-100 dark:bg-orange-900/20 rounded">
            <RotateCcw className="w-4 h-4 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <h3 className="font-medium text-sm">Loop Control</h3>
            <p className="text-xs text-muted-foreground">Execute iterations</p>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="config">Config</TabsTrigger>
            <TabsTrigger value="output" className="relative">
              Output
              {isLooping && (
                <div className="absolute -top-1 -right-1 h-2 w-2 bg-orange-500 rounded-full animate-pulse" />
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="config" className="space-y-3">
            <div>
              <Label className="text-xs">Loop Type</Label>
              <Select value={loopType} onValueChange={(value: LoopType) => setLoopType(value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="count">Count Loop</SelectItem>
                  <SelectItem value="while">While Loop</SelectItem>
                  <SelectItem value="foreach">For Each</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {loopType === "count" && (
              <div>
                <Label className="text-xs">Iterations</Label>
                <Input
                  type="number"
                  value={iterations}
                  onChange={(e) => setIterations(parseInt(e.target.value) || 1)}
                  min={1}
                  max={100}
                  className="h-8"
                />
              </div>
            )}

            {loopType === "while" && (
              <div>
                <Label className="text-xs">Condition (use 'i' for iteration)</Label>
                <Input
                  value={condition}
                  onChange={(e) => setCondition(e.target.value)}
                  placeholder="i < 5"
                  className="h-8"
                />
              </div>
            )}

            {loopType === "foreach" && (
              <div>
                <Label className="text-xs">Array (JSON format)</Label>
                <Input
                  value={arrayInput}
                  onChange={(e) => setArrayInput(e.target.value)}
                  placeholder='["item1", "item2", "item3"]'
                  className="h-8"
                />
              </div>
            )}

            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={startLoop}
                disabled={isLooping}
                className="flex-1"
              >
                {isLooping ? (
                  <>
                    <RotateCcw className="mr-2 h-3 w-3 animate-spin" />
                    Running...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-3 w-3" />
                    Start Loop
                  </>
                )}
              </Button>

              {isLooping && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={stopLoop}
                >
                  <Square className="h-3 w-3" />
                </Button>
              )}
            </div>

            {isLooping && (
              <div className="text-xs text-muted-foreground text-center">
                Iteration {currentIteration} of {loopType === "count" ? iterations : "?"}
              </div>
            )}
          </TabsContent>

          <TabsContent value="output" className="min-h-[150px]">
            {results.length > 0 ? (
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {results.map((result, index) => (
                  <div key={index} className="p-2 bg-muted rounded text-xs">
                    <div className="font-medium">Iteration {result.iteration}</div>
                    <div className="text-muted-foreground truncate">{result.output}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground text-sm">
                {isLooping ? "Loop executing..." : "Configure and start loop to see results"}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

LoopNode.displayName = "LoopNode";

export default LoopNode;

// Export execution definition for the workflow engine
export const loopExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    const { loopType, iterations, condition, arrayInput } = config;
    const inputData = inputs.data || inputs.text || inputs.value || "";

    context.log(`Loop Node executing ${loopType} loop`);

    const results: any[] = [];

    switch (loopType) {
      case "count":
        for (let i = 0; i < (iterations || 3); i++) {
          const result = {
            iteration: i + 1,
            input: inputData,
            output: `Loop iteration ${i + 1}: ${inputData}`,
            timestamp: new Date().toISOString()
          };
          results.push(result);
        }
        break;

      case "foreach":
        try {
          const array = JSON.parse(arrayInput || '[]');
          array.forEach((item: any, index: number) => {
            const result = {
              iteration: index + 1,
              item,
              input: inputData,
              output: `Processed item ${index + 1}: ${JSON.stringify(item)}`,
              timestamp: new Date().toISOString()
            };
            results.push(result);
          });
        } catch (error) {
          context.log(`Invalid array input: ${error}`, 'error');
        }
        break;

      default:
        results.push({
          iteration: 1,
          input: inputData,
          output: `Single execution: ${inputData}`,
          timestamp: new Date().toISOString()
        });
    }

    context.log(`Loop completed with ${results.length} iterations`);

    return {
      results,
      totalIterations: results.length,
      lastResult: results[results.length - 1],
      data: results,
      text: `Loop completed: ${results.length} iterations`
    };
  },
  inputs: [
    { id: 'data', name: 'Input Data', type: 'object' as const, required: false }
  ],
  outputs: [
    { id: 'results', name: 'All Results', type: 'array' as const },
    { id: 'lastResult', name: 'Last Result', type: 'object' as const },
    { id: 'totalIterations', name: 'Total Iterations', type: 'number' as const },
    { id: 'data', name: 'Results Data', type: 'array' as const },
    { id: 'text', name: 'Summary Text', type: 'string' as const }
  ],
  timeout: 60000,
  retryable: true
};
