"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Package, Play, Loader2, CheckCircle, XCircle } from "lucide-react";
interface UploadedPackageNodeProps {
  data: any;
  id: string;
  selected?: boolean;
}

/**
 * Uploaded Package Node - Generic component for marketplace uploaded nodes
 * Handles nodes uploaded from the sample package format
 */
const UploadedPackageNode = memo(({ data, id }: UploadedPackageNodeProps) => {
  const [inputs, setInputs] = useState<Record<string, any>>(data.inputs || {});
  const [outputs, setOutputs] = useState<Record<string, any>>({});
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [error, setError] = useState<string>('');

  // Get node metadata
  const nodeDefinition = data.nodeDefinition || {};
  const nodeName = nodeDefinition.name || data.label || 'Uploaded Node';
  const nodeDescription = nodeDefinition.description || 'Custom uploaded node';
  const nodeInputs = nodeDefinition.inputs || [];
  const nodeOutputs = nodeDefinition.outputs || [];

  // Update connected nodes with outputs
  useEffect(() => {
    if (Object.keys(outputs).length > 0 && data.onChange) {
      // Send the primary output (usually 'result' or 'data')
      const primaryOutput = outputs.result || outputs.data || outputs[Object.keys(outputs)[0]];
      if (primaryOutput !== undefined) {
        console.log(`UploadedPackageNode ${id}: Sending output to connected nodes:`, primaryOutput);
        data.onChange(primaryOutput);
      }
    }
  }, [outputs, data, id]);

  // Handle input changes
  const handleInputChange = (inputId: string, value: any) => {
    const newInputs = { ...inputs, [inputId]: value };
    setInputs(newInputs);

    // Update node data
    if (data.onDataUpdate) {
      data.onDataUpdate({ ...data, inputs: newInputs });
    }
  };

  // Execute the node
  const executeNode = async () => {
    setIsExecuting(true);
    setError('');
    setExecutionStatus('idle');

    try {
      console.log(`UploadedPackageNode ${id}: Starting execution with inputs:`, inputs);

      // Try to execute using the node definition's execute function
      if (nodeDefinition.execute && typeof nodeDefinition.execute === 'function') {
        console.log(`UploadedPackageNode ${id}: Executing using nodeDefinition.execute`);

        const result = await nodeDefinition.execute(inputs, {});

        if (result && typeof result === 'object') {
          setOutputs(result);
          setExecutionStatus('success');
          console.log(`UploadedPackageNode ${id}: Execution successful:`, result);
        } else {
          // Handle simple return values
          const wrappedResult = {
            result: result,
            data: result,
            output: result
          };
          setOutputs(wrappedResult);
          setExecutionStatus('success');
          console.log(`UploadedPackageNode ${id}: Execution successful (wrapped):`, wrappedResult);
        }
      } else {
        // Try to load and execute the node code from the marketplace
        console.log(`UploadedPackageNode ${id}: Loading node code from API`);

        const nodeCodeResponse = await fetch(`/api/nodes/code/${data.originalNodeId || data.nodeId || id}`);
        if (!nodeCodeResponse.ok) {
          throw new Error('Failed to load node code');
        }

        const codeData = await nodeCodeResponse.json();

        if (codeData.code) {
          console.log(`UploadedPackageNode ${id}: Executing loaded code`);

          // Create a safe execution environment
          const executionEnv = {
            inputs,
            config: {},
            nodeId: id,
            nodeType: data.nodeType || 'uploaded-package'
          };

          // Execute the code in a safe way
          const nodeFunction = new Function('executionEnv', `
            ${codeData.code}

            // Try to find and execute the node definition
            if (typeof nodeDefinition !== 'undefined' && typeof nodeDefinition.execute === 'function') {
              return nodeDefinition.execute(executionEnv.inputs, executionEnv.config);
            } else if (typeof executeNode === 'function') {
              return executeNode(executionEnv);
            } else {
              return { output: 'Node executed successfully', inputs: executionEnv.inputs };
            }
          `);

          const result = await Promise.resolve(nodeFunction(executionEnv));

          if (result && typeof result === 'object') {
            setOutputs(result);
          } else {
            setOutputs({
              result: result,
              data: result,
              output: result
            });
          }
          setExecutionStatus('success');
          console.log(`UploadedPackageNode ${id}: Code execution successful:`, result);
        } else {
          throw new Error('No executable code found');
        }
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      setExecutionStatus('error');
      setOutputs({});
      console.error(`UploadedPackageNode ${id}: Execution failed:`, errorMessage);
    } finally {
      setIsExecuting(false);
    }
  };

  // Get status icon
  const getStatusIcon = () => {
    if (isExecuting) {
      return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />;
    }
    switch (executionStatus) {
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'error':
        return <XCircle className="h-3 w-3 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[300px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium flex-1">
            {nodeName}
          </Label>
          {getStatusIcon()}
        </div>

        {/* Description */}
        {nodeDescription && (
          <div className="text-xs text-muted-foreground">
            {nodeDescription}
          </div>
        )}

        {/* Dynamic Inputs */}
        {nodeInputs.length > 0 && (
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Inputs</Label>
            {nodeInputs.map((input: any) => (
              <div key={input.id} className="space-y-1">
                <Label className="text-xs">{input.name}</Label>
                <Input
                  type={input.type === 'number' ? 'number' : 'text'}
                  value={inputs[input.id] || ''}
                  onChange={(e) => handleInputChange(input.id, e.target.value)}
                  placeholder={input.description || `Enter ${input.name}`}
                  className="h-8 text-sm"
                />
              </div>
            ))}
          </div>
        )}

        {/* Generic Input (fallback) */}
        {nodeInputs.length === 0 && (
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Input</Label>
            <Input
              value={inputs.input || ''}
              onChange={(e) => handleInputChange('input', e.target.value)}
              placeholder="Enter input data"
              className="h-8 text-sm"
            />
          </div>
        )}

        {/* Outputs Display */}
        {Object.keys(outputs).length > 0 && (
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Outputs</Label>
            {Object.entries(outputs).map(([key, value]) => (
              <div key={key} className="space-y-1">
                <Label className="text-xs capitalize">{key}</Label>
                <div className="p-2 bg-muted/50 rounded border text-xs font-mono">
                  {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4" />
              <span className="font-medium">Error:</span>
            </div>
            <div className="mt-1 text-xs">{error}</div>
          </div>
        )}

        {/* Execute Button */}
        <Button
          onClick={executeNode}
          disabled={isExecuting}
          size="sm"
          className="w-full"
        >
          {isExecuting ? (
            <>
              <Loader2 className="h-3 w-3 mr-2 animate-spin" />
              Executing...
            </>
          ) : (
            <>
              <Play className="h-3 w-3 mr-2" />
              Execute
            </>
          )}
        </Button>
      </div>

      {/* Input Handles - Following standard positioning */}
      {nodeInputs.length > 0 ? (
        nodeInputs.map((input: any, index: number) => (
          <Handle
            key={`input-${input.id || input.name || index}`}
            type="target"
            position={Position.Left}
            id={input.id || input.name || `input-${index}`}
            className="w-4 h-4 border-2 border-background bg-muted hover:bg-muted-foreground/20 transition-colors"
            style={{
              top: nodeInputs.length === 1 ? '50%' : `${((index + 1) / (nodeInputs.length + 1)) * 100}%`,
            }}
            title={`${input.label || input.name || input.id}: ${input.description || 'Input connection'}`}
          />
        ))
      ) : (
        <Handle
          type="target"
          position={Position.Left}
          id="input"
          className="w-4 h-4 border-2 border-background bg-muted hover:bg-muted-foreground/20 transition-colors"
          style={{ top: '50%' }}
          title="Input connection"
        />
      )}

      {/* Output Handles - Following standard positioning */}
      {nodeOutputs.length > 0 ? (
        nodeOutputs.map((output: any, index: number) => (
          <Handle
            key={`output-${output.id || output.name || index}`}
            type="source"
            position={Position.Right}
            id={output.id || output.name || `output-${index}`}
            className="w-4 h-4 border-2 border-background bg-primary hover:bg-primary/80 transition-colors"
            style={{
              top: nodeOutputs.length === 1 ? '50%' : `${((index + 1) / (nodeOutputs.length + 1)) * 100}%`,
            }}
            title={`${output.label || output.name || output.id}: ${output.description || 'Output connection'}`}
          />
        ))
      ) : (
        <Handle
          type="source"
          position={Position.Right}
          id="output"
          className="w-4 h-4 border-2 border-background bg-primary hover:bg-primary/80 transition-colors"
          style={{ top: '50%' }}
          title="Output connection"
        />
      )}
    </div>
  );
});

UploadedPackageNode.displayName = "UploadedPackageNode";

export default UploadedPackageNode;
