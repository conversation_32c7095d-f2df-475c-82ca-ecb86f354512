"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useRef, memo } from "react";
import { <PERSON><PERSON>, Posi<PERSON> } from "reactflow";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Settings,
  Loader2,
  AlertCircle,
  Copy,
  Check,
  RefreshCw,
  Trash2,
  ArrowLeftIcon,
  ArrowRightIcon
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

// Define AI model options
type AIModel = "gpt-3.5-turbo" | "gpt-4" | "claude-3-haiku" | "claude-3-sonnet" | "claude-3-opus" | "deepseek-chat" | "deepseek-coder" | "gemini-1.5-pro" | "gemini-1.5-flash";

/**
 * Generate Text Node - Standardized Structure
 * Generate text using AI models with configurable parameters
 */
const GenerateTextNode = memo(({ data, id }: StandardNodeProps) => {
  // State for the prompt and generated text
  const [prompt, setPrompt] = useState<string>((data as any).prompt || "");
  const [generatedText, setGeneratedText] = useState<string>("");
  const [rawResponse, setRawResponse] = useState<any>(null);
  const [extractedData, setExtractedData] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [copied, setCopied] = useState<boolean>(false);

  // State for API settings
  const [apiKey, setApiKey] = useState<string>("");
  const [selectedModel, setSelectedModel] = useState<AIModel>("gemini-1.5-flash");
  const [temperature, setTemperature] = useState<number>(0.7);
  const [maxTokens, setMaxTokens] = useState<number>(500);
  const [directOutput, setDirectOutput] = useState<boolean>(false);
  const [settingsOpen, setSettingsOpen] = useState<boolean>(false);

  // Ref to store the API key in localStorage
  const apiKeyStorageKey = `ai-api-key-${id}`;
  const modelStorageKey = `ai-model-${id}`;
  const temperatureStorageKey = `ai-temperature-${id}`;
  const maxTokensStorageKey = `ai-max-tokens-${id}`;
  const directOutputStorageKey = `ai-direct-output-${id}`;

  // Load API key and settings from localStorage on mount
  useEffect(() => {
    const storedApiKey = localStorage.getItem(apiKeyStorageKey);
    if (storedApiKey) {
      setApiKey(storedApiKey);
    }

    const storedModel = localStorage.getItem(modelStorageKey);
    if (storedModel) {
      setSelectedModel(storedModel as AIModel);
    }

    const storedTemperature = localStorage.getItem(temperatureStorageKey);
    if (storedTemperature) {
      setTemperature(parseFloat(storedTemperature));
    }

    const storedMaxTokens = localStorage.getItem(maxTokensStorageKey);
    if (storedMaxTokens) {
      setMaxTokens(parseInt(storedMaxTokens));
    }

    const storedDirectOutput = localStorage.getItem(directOutputStorageKey);
    if (storedDirectOutput) {
      setDirectOutput(storedDirectOutput === 'true');
    }
  }, [apiKeyStorageKey, modelStorageKey, temperatureStorageKey, maxTokensStorageKey, directOutputStorageKey]);

  // Save settings to localStorage
  const saveSettings = useCallback(() => {
    localStorage.setItem(apiKeyStorageKey, apiKey);
    localStorage.setItem(modelStorageKey, selectedModel);
    localStorage.setItem(temperatureStorageKey, temperature.toString());
    localStorage.setItem(maxTokensStorageKey, maxTokens.toString());
    localStorage.setItem(directOutputStorageKey, directOutput.toString());
    setSettingsOpen(false);
  }, [apiKey, selectedModel, temperature, maxTokens, directOutput, apiKeyStorageKey, modelStorageKey, temperatureStorageKey, maxTokensStorageKey, directOutputStorageKey]);

  // Clear settings
  const clearSettings = useCallback(() => {
    localStorage.removeItem(apiKeyStorageKey);
    localStorage.removeItem(modelStorageKey);
    localStorage.removeItem(temperatureStorageKey);
    localStorage.removeItem(maxTokensStorageKey);
    localStorage.removeItem(directOutputStorageKey);
    setApiKey("");
    setSelectedModel("gemini-1.5-flash");
    setTemperature(0.7);
    setMaxTokens(500);
    setDirectOutput(false);
  }, [apiKeyStorageKey, modelStorageKey, temperatureStorageKey, maxTokensStorageKey, directOutputStorageKey]);

  // Update the prompt in the node data
  useEffect(() => {
    if (data.onChange) {
      data.onChange(prompt);
    }
  }, [prompt, data]);

  // Update connected nodes with generated text
  useEffect(() => {
    if (generatedText && data.onChange) {
      // When directOutput is true, send the raw API response data
      // When false, send the processed text content
      const outputData = directOutput
        ? (rawResponse ? JSON.stringify(rawResponse, null, 2) : generatedText)
        : generatedText;

      console.log(`GenerateTextNode ${id}: Sending ${directOutput ? 'raw API response' : 'processed text'} to connected nodes:`, outputData);
      data.onChange(outputData);
    }
  }, [generatedText, rawResponse, data, id, directOutput]);

  // Update connected nodes with raw data (second output handle)
  useEffect(() => {
    if (extractedData && data.onRawDataChange) {
      console.log(`GenerateTextNode ${id}: Sending extracted raw data to connected nodes:`, extractedData);
      data.onRawDataChange(extractedData);
    }
  }, [extractedData, data, id]);

  // Extract raw data from AI response (remove narration)
  const extractRawData = useCallback((text: string): string => {
    if (!text) return "";

    // Common patterns to identify data sections
    const patterns = [
      // Tables, lists, JSON, CSV patterns
      /```[\s\S]*?```/g,  // Code blocks
      /\|.*\|[\s\S]*?\|.*\|/g,  // Table format
      /^\d+\..*$/gm,  // Numbered lists
      /^-.*$/gm,  // Bullet lists
      /^\*.*$/gm,  // Asterisk lists
      /\{[\s\S]*?\}/g,  // JSON objects
      /\[[\s\S]*?\]/g,  // Arrays
      /"[^"]*":\s*"[^"]*"/g,  // Key-value pairs
    ];

    let extractedContent = "";

    // Try to extract structured data
    for (const pattern of patterns) {
      const matches = text.match(pattern);
      if (matches && matches.length > 0) {
        extractedContent += matches.join('\n') + '\n';
      }
    }

    // If no structured data found, try to extract lines that look like data
    if (!extractedContent.trim()) {
      const lines = text.split('\n');
      const dataLines = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed &&
               !trimmed.toLowerCase().includes('here') &&
               !trimmed.toLowerCase().includes('below') &&
               !trimmed.toLowerCase().includes('above') &&
               !trimmed.toLowerCase().includes('sample') &&
               !trimmed.toLowerCase().includes('example') &&
               !trimmed.toLowerCase().includes('data') &&
               !trimmed.match(/^(here|below|above|sample|example)/i) &&
               (trimmed.includes(',') || trimmed.includes('|') || trimmed.includes(':') || /^\d+\./.test(trimmed));
      });
      extractedContent = dataLines.join('\n');
    }

    return extractedContent.trim() || text; // Fallback to original text if no data extracted
  }, []);

  // Update extracted data when generated text changes
  useEffect(() => {
    if (generatedText) {
      const extracted = extractRawData(generatedText);
      setExtractedData(extracted);
    }
  }, [generatedText, extractRawData]);

  // Copy generated text to clipboard
  const copyToClipboard = useCallback(() => {
    navigator.clipboard.writeText(generatedText).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  }, [generatedText]);

  // Generate text using the selected AI model
  const generateText = useCallback(async () => {
    if (!prompt.trim()) {
      setError("Please enter a prompt");
      return;
    }

    if (!apiKey) {
      setError("API key is required. Please configure it in settings.");
      setSettingsOpen(true);
      return;
    }

    setIsGenerating(true);
    setError("");

    try {
      // Determine which API to use based on the selected model
      const isOpenAI = selectedModel.startsWith("gpt");
      const isAnthropic = selectedModel.startsWith("claude");
      const isDeepSeek = selectedModel.startsWith("deepseek");
      const isGemini = selectedModel.startsWith("gemini");

      let response;

      if (isOpenAI) {
        // OpenAI API call
        response = await fetch("https://api.openai.com/v1/chat/completions", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: selectedModel,
            messages: [{ role: "user", content: prompt }],
            temperature: temperature,
            max_tokens: maxTokens
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error?.message || "Failed to generate text");
        }

        setRawResponse(data);
        setGeneratedText(data.choices[0].message.content);
      } else if (isDeepSeek) {
        // DeepSeek API call (OpenAI-compatible)
        response = await fetch("https://api.deepseek.com/v1/chat/completions", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: selectedModel,
            messages: [{ role: "user", content: prompt }],
            temperature: temperature,
            max_tokens: maxTokens,
            stream: false
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error?.message || data.message || "Failed to generate text");
        }

        setRawResponse(data);
        setGeneratedText(data.choices[0].message.content);
      } else if (isGemini) {
        // Google Gemini API call
        response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${selectedModel}:generateContent?key=${apiKey}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig: {
              temperature: temperature,
              maxOutputTokens: maxTokens,
              topP: 0.8,
              topK: 10
            }
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error?.message || data.message || "Failed to generate text");
        }

        setRawResponse(data);
        if (data.candidates && data.candidates.length > 0 && data.candidates[0].content) {
          setGeneratedText(data.candidates[0].content.parts[0].text);
        } else {
          throw new Error("No content generated by Gemini");
        }
      } else if (isAnthropic) {
        // Anthropic API call
        response = await fetch("https://api.anthropic.com/v1/messages", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-api-key": apiKey,
            "anthropic-version": "2023-06-01"
          },
          body: JSON.stringify({
            model: selectedModel,
            messages: [{ role: "user", content: prompt }],
            temperature: temperature,
            max_tokens: maxTokens
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error?.message || "Failed to generate text");
        }

        setRawResponse(data);
        setGeneratedText(data.content[0].text);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsGenerating(false);
    }
  }, [prompt, apiKey, selectedModel, temperature, maxTokens]);

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[320px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Generate Text"}
          </Label>
          {isGenerating && (
            <div className="animate-pulse h-2 w-2 rounded-full bg-amber-500 ml-auto" title="Generating..." />
          )}
          {generatedText && !isGenerating && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Generated" />
          )}
        </div>

        {/* Input Indicator */}
        {data.inputValue && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Using input data</span>
            <ArrowRightIcon className="h-3 w-3" />
          </div>
        )}

        {/* Settings Dialog */}
        <div className="flex items-center justify-between">
          <Label className="text-xs text-muted-foreground">
            AI Configuration
          </Label>
          <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <Settings className="h-3.5 w-3.5" />
                <span className="sr-only">Settings</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>AI Settings</DialogTitle>
                <DialogDescription>
                  Configure your AI model and API key. Your API key is stored locally in your browser.
                  <br />
                  <span className="text-xs mt-1 block">
                    Get API keys: <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Google AI</a> | <a href="https://platform.deepseek.com/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">DeepSeek</a> | <a href="https://platform.openai.com/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">OpenAI</a> | <a href="https://console.anthropic.com/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Anthropic</a>
                  </span>
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="api-key" className="text-right">
                    API Key
                  </Label>
                  <Input
                    id="api-key"
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="AIza... (Google) | sk-... (DeepSeek/OpenAI) | sk-ant-... (Anthropic)"
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="model" className="text-right">
                    Model
                  </Label>
                  <Select value={selectedModel} onValueChange={(value) => setSelectedModel(value as AIModel)}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gemini-1.5-flash">Gemini 1.5 Flash</SelectItem>
                      <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                      <SelectItem value="deepseek-chat">DeepSeek Chat</SelectItem>
                      <SelectItem value="deepseek-coder">DeepSeek Coder</SelectItem>
                      <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                      <SelectItem value="gpt-4">GPT-4</SelectItem>
                      <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                      <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                      <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="temperature" className="text-right">
                    Temperature
                  </Label>
                  <div className="col-span-3 flex items-center gap-2">
                    <Input
                      id="temperature"
                      type="number"
                      min="0"
                      max="1"
                      step="0.1"
                      value={temperature}
                      onChange={(e) => setTemperature(parseFloat(e.target.value))}
                      className="w-20"
                    />
                    <span className="text-xs text-muted-foreground">
                      (0 = deterministic, 1 = creative)
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="max-tokens" className="text-right">
                    Max Tokens
                  </Label>
                  <Input
                    id="max-tokens"
                    type="number"
                    min="1"
                    max="8192"
                    value={maxTokens}
                    onChange={(e) => setMaxTokens(parseInt(e.target.value))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="direct-output" className="text-right">
                    Direct Output
                  </Label>
                  <div className="col-span-3 flex items-center gap-2">
                    <Switch
                      id="direct-output"
                      checked={directOutput}
                      onCheckedChange={setDirectOutput}
                    />
                    <span className="text-xs text-muted-foreground">
                      Output raw API response JSON instead of processed text
                    </span>
                  </div>
                </div>
              </div>
              <DialogFooter className="flex justify-between">
                <Button variant="outline" onClick={clearSettings} className="mr-auto">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear
                </Button>
                <Button onClick={saveSettings}>Save Settings</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Textarea
          placeholder="Enter your prompt here..."
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          className="min-h-[80px] text-sm"
        />

        <Button
          onClick={generateText}
          disabled={isGenerating || !prompt.trim()}
          className="w-full"
        >
          {isGenerating ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              Generate
            </>
          )}
        </Button>

        {error && (
          <div className="p-2 bg-destructive/10 text-destructive rounded-md text-xs flex items-center gap-1.5">
            <AlertCircle className="h-3.5 w-3.5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}

        {generatedText && (
          <div className="mt-2 border rounded-md bg-muted/50 relative">
            <div className="flex items-center justify-between p-2 border-b bg-muted/30">
              <Label className="text-xs font-medium text-muted-foreground">
                Generated Response
              </Label>
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={copyToClipboard}
                  title="Copy to clipboard"
                >
                  {copied ? (
                    <Check className="h-3.5 w-3.5 text-green-500" />
                  ) : (
                    <Copy className="h-3.5 w-3.5" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={generateText}
                  disabled={isGenerating}
                  title="Regenerate"
                >
                  <RefreshCw className={`h-3.5 w-3.5 ${isGenerating ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </div>
            <ScrollArea className="h-32 w-full">
              <div className="p-3 text-sm whitespace-pre-wrap break-words">
                {generatedText}
              </div>
            </ScrollArea>
            <div className="p-2 border-t bg-muted/30">
              <div className="text-xs text-muted-foreground flex items-center justify-between">
                <span>
                  Output: {directOutput
                    ? 'Connected nodes will receive raw API response JSON'
                    : 'Connected nodes will receive processed text'
                  }
                </span>
                <span className="text-primary">
                  {directOutput ? '🔗 Direct' : '✓ Ready'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Main output handle - complete AI response */}
      <div className="absolute right-0 top-[40%] transform -translate-y-1/2 flex items-center">
        <span className="text-xs text-muted-foreground mr-2 bg-background px-1 rounded">Full</span>
        <Handle
          type="source"
          position={Position.Right}
          id="output"
          className="w-3 h-3 bg-primary border-2 border-background"
        />
      </div>

      {/* Raw data output handle - extracted data only */}
      <div className="absolute right-0 top-[60%] transform -translate-y-1/2 flex items-center">
        <span className="text-xs text-muted-foreground mr-2 bg-background px-1 rounded">Data</span>
        <Handle
          type="source"
          position={Position.Right}
          id="rawData"
          className="w-3 h-3 bg-orange-500 border-2 border-background"
        />
      </div>
    </div>
  );
});

GenerateTextNode.displayName = "GenerateTextNode";

export default GenerateTextNode;
