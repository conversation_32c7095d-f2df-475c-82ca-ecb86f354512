"use client";

import { <PERSON>act<PERSON><PERSON><PERSON>rovider } from "reactflow";
import dynamic from 'next/dynamic';
import { Suspense, memo, useState, useEffect } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

// Define the workflow data interface
interface WorkflowData {
  id: string;
  name: string;
  description: string | null;
  nodes: any[];
  edges: any[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// Lazy load the workflow container for better performance
const WorkflowContainer = dynamic(() => import("./workflow-container"), {
  ssr: false,
  loading: () => <WorkflowLoading />
});

// Loading component with skeleton UI
const WorkflowLoading = memo(() => {
  return (
    <div className="flex items-center justify-center h-screen w-full bg-background">
      <LoadingSpinner size="lg" text="Loading Workflow Builder..." />
    </div>
  );
});

interface WorkflowProviderProps {
  initialWorkflow?: WorkflowData | null;
}

export default function WorkflowProvider({ initialWorkflow }: WorkflowProviderProps) {
  // Track if the component is mounted to avoid hydration issues
  const [isMounted, setIsMounted] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    try {
      console.log("WorkflowProvider mounting with initialWorkflow:", initialWorkflow);
      setIsMounted(true);
    } catch (err) {
      console.error("Error in WorkflowProvider useEffect:", err);
      setError(err instanceof Error ? err : new Error("Unknown error in WorkflowProvider"));
    }
  }, [initialWorkflow]);

  // Handle errors
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen w-full bg-destructive/10">
        <div className="bg-destructive/10 border border-destructive text-destructive px-6 py-4 rounded-md max-w-md text-center">
          <h2 className="font-semibold mb-2">Error Loading Workflow</h2>
          <p>{error.message}</p>
          <p className="mt-2 text-sm text-muted-foreground">Please try refreshing the page or contact support if the issue persists.</p>
        </div>
      </div>
    );
  }

  if (!isMounted) {
    return <WorkflowLoading />;
  }

  try {
    // Create a default empty workflow if none is provided
    const workflowData = initialWorkflow || {
      id: '',
      name: 'New Workflow',
      description: null,
      nodes: [],
      edges: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: ''
    };

    console.log("WorkflowProvider rendering with workflowData:", workflowData);

    return (
      <ReactFlowProvider>
        <Suspense fallback={<WorkflowLoading />}>
          <WorkflowContainer initialWorkflow={workflowData} />
        </Suspense>
      </ReactFlowProvider>
    );
  } catch (err) {
    console.error("Error rendering WorkflowProvider:", err);
    return (
      <div className="flex flex-col items-center justify-center h-screen w-full bg-destructive/10">
        <div className="bg-destructive/10 border border-destructive text-destructive px-6 py-4 rounded-md max-w-md text-center">
          <h2 className="font-semibold mb-2">Error Loading Workflow</h2>
          <p>{err instanceof Error ? err.message : "An unexpected error occurred"}</p>
          <p className="mt-2 text-sm text-muted-foreground">Please try refreshing the page or contact support if the issue persists.</p>
        </div>
      </div>
    );
  }
}
