"use client";

import { useState, useEffect } from "react";
import { AppSidebar } from "./workflow-sidebar";

// Global event bus for communication between components
const nodeAddedEventName = "workflow:node-added";

// Create a custom event for adding nodes
export const createNodeAddedEvent = (nodeType: string) => {
  return new CustomEvent(nodeAddedEventName, {
    detail: { nodeType },
  });
};

// Listen for node added events
export const addNodeAddedEventListener = (callback: (nodeType: string) => void) => {
  const handler = (event: Event) => {
    const customEvent = event as CustomEvent;
    callback(customEvent.detail.nodeType);
  };

  window.addEventListener(nodeAddedEventName, handler);

  return () => {
    window.removeEventListener(nodeAddedEventName, handler);
  };
};

export function WorkflowSidebar() {
  return <AppSidebar />;
}
