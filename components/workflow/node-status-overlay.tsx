"use client";

import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
  Play,
  Pause,
  AlertTriangle
} from "lucide-react";
import { WorkflowExecutionEngine, NodeExecutionResult } from "@/lib/workflow/execution-engine";

interface NodeStatusOverlayProps {
  nodeId: string;
  workflowId: string;
  className?: string;
}

export function NodeStatusOverlay({ nodeId, workflowId, className = "" }: NodeStatusOverlayProps) {
  const [status, setStatus] = useState<'idle' | 'waiting' | 'executing' | 'completed' | 'failed'>('idle');
  const [result, setResult] = useState<NodeExecutionResult | null>(null);
  const [progress, setProgress] = useState(0);

  const executionEngine = WorkflowExecutionEngine.getInstance();

  useEffect(() => {
    if (!workflowId || !nodeId) return;

    const unsubscribe = executionEngine.onStatusChange(workflowId, (executionStatus) => {
      // Update node status based on workflow execution
      if (executionStatus.currentNode === nodeId) {
        setStatus('executing');
        setProgress(50); // Approximate progress for executing node
      } else if (executionStatus.completedNodes.includes(nodeId)) {
        setStatus('completed');
        setProgress(100);
        const nodeResult = executionStatus.results[nodeId];
        if (nodeResult) {
          setResult(nodeResult);
        }
      } else if (executionStatus.failedNodes.includes(nodeId)) {
        setStatus('failed');
        setProgress(0);
        const nodeResult = executionStatus.results[nodeId];
        if (nodeResult) {
          setResult(nodeResult);
        }
      } else if (executionStatus.status === 'running') {
        // Check if this node is waiting (dependencies not met)
        const hasCompletedDependencies = true; // This would need dependency checking logic
        setStatus(hasCompletedDependencies ? 'waiting' : 'idle');
        setProgress(0);
      } else {
        setStatus('idle');
        setProgress(0);
        setResult(null);
      }
    });

    return unsubscribe;
  }, [nodeId, workflowId, executionEngine]);

  const getStatusIcon = () => {
    switch (status) {
      case 'executing':
        return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'failed':
        return <XCircle className="h-3 w-3 text-red-500" />;
      case 'waiting':
        return <Clock className="h-3 w-3 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'executing':
        return (
          <Badge variant="default" className="bg-blue-500 text-white">
            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            Executing
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="default" className="bg-green-500 text-white">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        );
      case 'waiting':
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Waiting
          </Badge>
        );
      default:
        return null;
    }
  };

  if (status === 'idle') {
    return null;
  }

  return (
    <div className={`absolute top-2 right-2 z-10 ${className}`}>
      <div className="flex flex-col items-end gap-1">
        {/* Status Badge */}
        {getStatusBadge()}

        {/* Progress Bar for Executing Nodes */}
        {status === 'executing' && (
          <div className="w-24 bg-white rounded-full shadow-sm border">
            <Progress value={progress} className="h-1" />
          </div>
        )}

        {/* Execution Time */}
        {result && (
          <div className="text-xs text-muted-foreground bg-white px-2 py-1 rounded shadow-sm border">
            {result.executionTime}ms
          </div>
        )}

        {/* Error Message */}
        {status === 'failed' && result?.error && (
          <div className="max-w-48 text-xs text-red-600 bg-red-50 px-2 py-1 rounded shadow-sm border border-red-200">
            {result.error}
          </div>
        )}
      </div>
    </div>
  );
}

// Enhanced node wrapper that includes status overlay
export function NodeWithStatus({ 
  children, 
  nodeId, 
  workflowId, 
  className = "" 
}: { 
  children: React.ReactNode; 
  nodeId: string; 
  workflowId: string; 
  className?: string; 
}) {
  return (
    <div className={`relative ${className}`}>
      {children}
      <NodeStatusOverlay nodeId={nodeId} workflowId={workflowId} />
    </div>
  );
}
