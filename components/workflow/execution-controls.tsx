"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Play,
  Pause,
  Square,
  Clock,
  CheckCircle,
  XCircle,
  Activity,
  History,
  Calendar,
  Eye
} from "lucide-react";
import { Node, Edge } from "reactflow";
import {
  WorkflowExecutionEngine,
  WorkflowExecutionStatus,
  ExecutionContext,
  ExecutionOptions
} from "@/lib/workflow/execution-engine";
import { ExecutionPreviewModal } from "./execution-preview-modal";
import { ExecutionHistoryModal } from "./execution-history-modal";

interface ExecutionControlsProps {
  workflowId: string;
  nodes: Node[];
  edges: Edge[];
  context: ExecutionContext;
  onExecutionComplete?: (status: WorkflowExecutionStatus) => void;
}

export function ExecutionControls({
  workflowId,
  nodes,
  edges,
  context,
  onExecutionComplete
}: ExecutionControlsProps) {
  const [executionStatus, setExecutionStatus] = useState<WorkflowExecutionStatus | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [executionOptions, setExecutionOptions] = useState<ExecutionOptions>({
    mode: 'optimized',
    timeout: 30000,
    retryAttempts: 3,
    continueOnError: false,
    debugMode: false,
    maxConcurrentNodes: 3
  });

  const executionEngine = WorkflowExecutionEngine.getInstance();

  // Subscribe to execution status changes
  useEffect(() => {
    if (!workflowId) return;

    const unsubscribe = executionEngine.onStatusChange(workflowId, (status) => {
      setExecutionStatus(status);
      setIsExecuting(['running', 'paused'].includes(status.status));

      if (['completed', 'failed', 'cancelled'].includes(status.status)) {
        onExecutionComplete?.(status);
      }
    });

    // Check for existing execution
    const existingStatus = executionEngine.getExecutionStatus(workflowId);
    if (existingStatus) {
      setExecutionStatus(existingStatus);
      setIsExecuting(['running', 'paused'].includes(existingStatus.status));
    }

    return unsubscribe;
  }, [workflowId, executionEngine, onExecutionComplete]);

  const handleStartExecution = useCallback(async () => {
    if (!nodes.length) {
      alert('No nodes to execute');
      return;
    }

    setIsExecuting(true);
    try {
      await executionEngine.executeWorkflow(nodes, edges, context, executionOptions);
    } catch (error) {
      console.error('Execution failed:', error);
      setIsExecuting(false);
    }
  }, [nodes, edges, context, executionOptions, executionEngine]);

  const handlePauseExecution = useCallback(async () => {
    await executionEngine.pauseWorkflow(workflowId);
  }, [workflowId, executionEngine]);

  const handleResumeExecution = useCallback(async () => {
    await executionEngine.resumeWorkflow(workflowId);
  }, [workflowId, executionEngine]);

  const handleStopExecution = useCallback(async () => {
    await executionEngine.cancelWorkflow(workflowId);
    setIsExecuting(false);
  }, [workflowId, executionEngine]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const formatDuration = (start?: Date, end?: Date) => {
    if (!start) return '0s';
    const endTime = end || new Date();
    const duration = Math.floor((endTime.getTime() - start.getTime()) / 1000);

    if (duration < 60) {
      return `${duration}s`;
    } else if (duration < 3600) {
      return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    } else {
      return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`;
    }
  };

  return (
    <>
      {/* Compact Horizontal Execution Controls */}
      <div className="flex items-center gap-1">
        {/* Main execution button */}
        {!isExecuting ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={handleStartExecution}
                  size="icon"
                  className="h-8 w-8"
                >
                  <Play className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Execute Workflow</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <div className="flex items-center gap-1">
            {executionStatus?.status === 'running' ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={handlePauseExecution}
                      variant="outline"
                      size="icon"
                      className="h-8 w-8"
                    >
                      <Pause className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Pause Execution</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={handleResumeExecution}
                      variant="outline"
                      size="icon"
                      className="h-8 w-8"
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Resume Execution</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleStopExecution}
                    variant="destructive"
                    size="icon"
                    className="h-8 w-8"
                  >
                    <Square className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Stop Execution</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}

        {/* Status indicator - compact */}
        {executionStatus && (
          <div className="flex items-center gap-1 ml-1">
            <div className="flex items-center">
              {getStatusIcon(executionStatus.status)}
              {executionStatus.progress > 0 && (
                <span className="text-xs text-muted-foreground ml-1">
                  {executionStatus.progress}%
                </span>
              )}
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex items-center gap-1 ml-1">
          {/* Preview Results */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowPreviewModal(true)}
                  disabled={!executionStatus}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Preview Results</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Execution History */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowHistoryModal(true)}
                >
                  <History className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Execution History</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Schedule Execution */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => {
                    // TODO: Open scheduler modal
                    console.log('Open scheduler');
                  }}
                >
                  <Calendar className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Schedule Execution</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Modals */}
      <ExecutionPreviewModal
        open={showPreviewModal}
        onOpenChange={setShowPreviewModal}
        executionStatus={executionStatus}
        workflowId={workflowId}
      />

      <ExecutionHistoryModal
        open={showHistoryModal}
        onOpenChange={setShowHistoryModal}
        workflowId={workflowId}
      />
    </>
  );
}
