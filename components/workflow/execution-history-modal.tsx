"use client";

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Loader2, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  Eye,
  Download,
  Calendar
} from 'lucide-react';
import { ExecutionPreviewModal } from './execution-preview-modal';

interface ExecutionHistoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  workflowId: string;
}

interface ExecutionRecord {
  id: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  variables?: any;
  options?: any;
  results?: any;
  logs?: any[];
  error?: string;
  triggerNodeId?: string;
}

export function ExecutionHistoryModal({
  open,
  onOpenChange,
  workflowId
}: ExecutionHistoryModalProps) {
  const [executions, setExecutions] = useState<ExecutionRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedExecution, setSelectedExecution] = useState<ExecutionRecord | null>(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  // Load execution history when modal opens
  useEffect(() => {
    if (open && workflowId) {
      loadExecutionHistory();
    }
  }, [open, workflowId]);

  const loadExecutionHistory = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/workflows/${workflowId}/execute`);
      
      if (!response.ok) {
        throw new Error('Failed to load execution history');
      }
      
      const data = await response.json();
      setExecutions(data.executions || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      running: "default",
      completed: "secondary",
      failed: "destructive",
      cancelled: "outline"
    };

    return (
      <Badge variant={variants[status] || "outline"} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status}
      </Badge>
    );
  };

  const formatDuration = (duration: number | null) => {
    if (!duration) return 'N/A';
    
    const seconds = Math.floor(duration / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ${seconds % 60}s`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m`;
  };

  const handlePreviewExecution = (execution: ExecutionRecord) => {
    // Convert execution record to execution status format
    const executionStatus = {
      workflowId: execution.id,
      executionId: execution.id,
      status: execution.status,
      startTime: new Date(execution.startTime),
      endTime: execution.endTime ? new Date(execution.endTime) : undefined,
      progress: execution.status === 'completed' ? 100 : 0,
      results: execution.results || {},
      logs: execution.logs || [],
      completedNodes: [],
      failedNodes: [],
      error: execution.error
    };

    setSelectedExecution(execution);
    setShowPreviewModal(true);
  };

  const exportExecution = (execution: ExecutionRecord) => {
    const exportData = {
      executionId: execution.id,
      workflowId: workflowId,
      status: execution.status,
      startTime: execution.startTime,
      endTime: execution.endTime,
      duration: execution.duration,
      results: execution.results,
      logs: execution.logs,
      error: execution.error,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `execution-${execution.id}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl w-full max-h-[85vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Execution History
            </DialogTitle>
            <DialogDescription>
              View past executions for this workflow
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Header with refresh button */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {executions.length} execution{executions.length !== 1 ? 's' : ''} found
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={loadExecutionHistory}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            {/* Content */}
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading execution history...</span>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <XCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
                <p className="text-red-600">{error}</p>
                <Button onClick={loadExecutionHistory} className="mt-2">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            ) : executions.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No executions found for this workflow</p>
              </div>
            ) : (
              <div className="border rounded-lg">
                <ScrollArea className="h-[400px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Status</TableHead>
                        <TableHead>Started</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {executions.map((execution) => (
                        <TableRow key={execution.id}>
                          <TableCell>
                            {getStatusBadge(execution.status)}
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm">
                                {new Date(execution.startTime).toLocaleDateString()}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {new Date(execution.startTime).toLocaleTimeString()}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {formatDuration(execution.duration)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handlePreviewExecution(execution)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => exportExecution(execution)}
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Preview Modal */}
      {selectedExecution && (
        <ExecutionPreviewModal
          open={showPreviewModal}
          onOpenChange={setShowPreviewModal}
          executionStatus={{
            workflowId: selectedExecution.id,
            executionId: selectedExecution.id,
            status: selectedExecution.status,
            startTime: new Date(selectedExecution.startTime),
            endTime: selectedExecution.endTime ? new Date(selectedExecution.endTime) : undefined,
            progress: selectedExecution.status === 'completed' ? 100 : 0,
            results: selectedExecution.results || {},
            logs: selectedExecution.logs || [],
            completedNodes: [],
            failedNodes: [],
            error: selectedExecution.error
          }}
          workflowId={workflowId}
        />
      )}
    </>
  );
}
