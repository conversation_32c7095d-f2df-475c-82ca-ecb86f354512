"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import { Node, Edge } from "reactflow";
import {
  WorkflowExecutionEngine,
  WorkflowExecutionStatus,
  ExecutionContext as EngineExecutionContext,
  ExecutionOptions
} from "@/lib/workflow/execution-engine";

interface ExecutionContextType {
  // Execution state
  isExecuting: boolean;
  executionStatus: WorkflowExecutionStatus | null;

  // Execution controls
  startExecution: (nodes: Node[], edges: Edge[], options?: ExecutionOptions) => Promise<void>;
  startBackgroundExecution: (nodes: Node[], edges: Edge[], options?: ExecutionOptions, priority?: number, scheduledAt?: Date) => Promise<string>;
  pauseExecution: () => Promise<void>;
  resumeExecution: () => Promise<void>;
  stopExecution: () => Promise<void>;

  // Node-level execution
  executeNode: (nodeId: string, inputs?: Record<string, any>) => Promise<void>;

  // Status queries
  getNodeStatus: (nodeId: string) => 'idle' | 'waiting' | 'executing' | 'completed' | 'failed';
  getNodeResult: (nodeId: string) => any;

  // Configuration
  executionOptions: ExecutionOptions;
  setExecutionOptions: (options: ExecutionOptions) => void;

  // Context data
  workflowId: string;
  userId: string;
  variables: Record<string, any>;
  setVariables: (variables: Record<string, any>) => void;

  // Background execution
  backgroundExecutions: any[];
  loadBackgroundExecutions: () => Promise<void>;
}

const ExecutionContext = createContext<ExecutionContextType | null>(null);

interface ExecutionProviderProps {
  children: React.ReactNode;
  workflowId: string;
  userId: string;
  initialVariables?: Record<string, any>;
}

export function ExecutionProvider({
  children,
  workflowId,
  userId,
  initialVariables = {}
}: ExecutionProviderProps) {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStatus, setExecutionStatus] = useState<WorkflowExecutionStatus | null>(null);
  const [variables, setVariables] = useState<Record<string, any>>(initialVariables);
  const [backgroundExecutions, setBackgroundExecutions] = useState<any[]>([]);
  const [executionOptions, setExecutionOptions] = useState<ExecutionOptions>({
    mode: 'optimized',
    timeout: 30000,
    retryAttempts: 3,
    continueOnError: false,
    debugMode: false,
    maxConcurrentNodes: 3,
    backgroundExecution: false,
    saveResults: true,
    notifyOnComplete: true
  });

  const executionEngine = WorkflowExecutionEngine.getInstance();

  // Subscribe to execution status changes
  useEffect(() => {
    if (!workflowId) return;

    const unsubscribe = executionEngine.onStatusChange(workflowId, (status) => {
      setExecutionStatus(status);
      setIsExecuting(['running', 'paused'].includes(status.status));
    });

    // Check for existing execution
    const existingStatus = executionEngine.getExecutionStatus(workflowId);
    if (existingStatus) {
      setExecutionStatus(existingStatus);
      setIsExecuting(['running', 'paused'].includes(existingStatus.status));
    }

    return unsubscribe;
  }, [workflowId, executionEngine]);

  const startExecution = useCallback(async (
    nodes: Node[],
    edges: Edge[],
    options?: ExecutionOptions
  ) => {
    if (!nodes.length) {
      throw new Error('No nodes to execute');
    }

    const context: EngineExecutionContext = {
      workflowId,
      userId,
      variables,
      secrets: {}, // TODO: Implement secrets management
      settings: {}, // TODO: Implement settings management
      log: (message: string) => console.log(`[Workflow ${workflowId}]: ${message}`)
    };

    const finalOptions = { ...executionOptions, ...options };

    setIsExecuting(true);
    try {
      // Save canvas execution to database via API
      if (workflowId && workflowId !== '') {
        console.log('[ExecutionContext] Saving canvas execution to database');

        const response = await fetch(`/api/workflows/${workflowId}/execute`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            executionOptions: finalOptions,
            variables,
            triggerNodeId: null
          })
        });

        if (!response.ok) {
          console.warn('[ExecutionContext] Failed to save execution to database, continuing with local execution');
        } else {
          const executionData = await response.json();
          console.log('[ExecutionContext] Canvas execution saved to database:', executionData.executionId);
        }
      }

      // Execute workflow locally for real-time UI updates
      await executionEngine.executeWorkflow(nodes, edges, context, finalOptions);
    } catch (error) {
      console.error('Execution failed:', error);
      setIsExecuting(false);
      throw error;
    }
  }, [workflowId, userId, variables, executionOptions, executionEngine]);

  const startBackgroundExecution = useCallback(async (
    nodes: Node[],
    edges: Edge[],
    options?: ExecutionOptions,
    priority: number = 5,
    scheduledAt?: Date
  ): Promise<string> => {
    if (!nodes.length) {
      throw new Error('No nodes to execute');
    }

    const finalOptions = {
      ...executionOptions,
      ...options,
      backgroundExecution: true,
      saveResults: true
    };

    console.log('[ExecutionContext] Starting background execution');

    const response = await fetch(`/api/workflows/${workflowId}/execute-background`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        executionOptions: finalOptions,
        variables,
        priority,
        scheduledAt: scheduledAt?.toISOString()
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to start background execution');
    }

    const result = await response.json();
    console.log('[ExecutionContext] Background execution queued:', result.executionId);

    // Refresh background executions list
    await loadBackgroundExecutions();

    return result.executionId;
  }, [workflowId, variables, executionOptions]);

  const loadBackgroundExecutions = useCallback(async () => {
    try {
      const response = await fetch(`/api/workflows/${workflowId}/execute-background`);
      if (response.ok) {
        const data = await response.json();
        setBackgroundExecutions(data.recentExecutions || []);
      }
    } catch (error) {
      console.error('Error loading background executions:', error);
    }
  }, [workflowId]);

  const pauseExecution = useCallback(async () => {
    await executionEngine.pauseWorkflow(workflowId);
  }, [workflowId, executionEngine]);

  const resumeExecution = useCallback(async () => {
    await executionEngine.resumeWorkflow(workflowId);
  }, [workflowId, executionEngine]);

  const stopExecution = useCallback(async () => {
    await executionEngine.cancelWorkflow(workflowId);
    setIsExecuting(false);
  }, [workflowId, executionEngine]);

  const executeNode = useCallback(async (nodeId: string, inputs: Record<string, any> = {}) => {
    // This would execute a single node - useful for testing
    // Implementation would depend on having the node definition
    console.log('Execute single node:', nodeId, inputs);
  }, []);

  const getNodeStatus = useCallback((nodeId: string): 'idle' | 'waiting' | 'executing' | 'completed' | 'failed' => {
    if (!executionStatus) return 'idle';

    if (executionStatus.currentNode === nodeId) return 'executing';
    if (executionStatus.completedNodes.includes(nodeId)) return 'completed';
    if (executionStatus.failedNodes.includes(nodeId)) return 'failed';
    if (executionStatus.status === 'running') return 'waiting';

    return 'idle';
  }, [executionStatus]);

  const getNodeResult = useCallback((nodeId: string) => {
    return executionStatus?.results[nodeId] || null;
  }, [executionStatus]);

  const contextValue: ExecutionContextType = {
    // Execution state
    isExecuting,
    executionStatus,

    // Execution controls
    startExecution,
    startBackgroundExecution,
    pauseExecution,
    resumeExecution,
    stopExecution,

    // Node-level execution
    executeNode,

    // Status queries
    getNodeStatus,
    getNodeResult,

    // Configuration
    executionOptions,
    setExecutionOptions,

    // Context data
    workflowId,
    userId,
    variables,
    setVariables,

    // Background execution
    backgroundExecutions,
    loadBackgroundExecutions
  };

  return (
    <ExecutionContext.Provider value={contextValue}>
      {children}
    </ExecutionContext.Provider>
  );
}

export function useExecution() {
  const context = useContext(ExecutionContext);
  if (!context) {
    throw new Error('useExecution must be used within an ExecutionProvider');
  }
  return context;
}

// Hook for node-specific execution status
export function useNodeExecution(nodeId: string) {
  const { getNodeStatus, getNodeResult, executionStatus } = useExecution();

  const [status, setStatus] = useState<'idle' | 'waiting' | 'executing' | 'completed' | 'failed'>('idle');
  const [result, setResult] = useState<any>(null);

  useEffect(() => {
    const newStatus = getNodeStatus(nodeId);
    const newResult = getNodeResult(nodeId);

    setStatus(newStatus);
    setResult(newResult);
  }, [nodeId, getNodeStatus, getNodeResult, executionStatus]);

  return {
    status,
    result,
    isExecuting: status === 'executing',
    isCompleted: status === 'completed',
    isFailed: status === 'failed',
    isWaiting: status === 'waiting'
  };
}
