/* Styles for ReactFlow - both light and dark mode */

/* Common styles for all nodes */
.react-flow__node {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.react-flow__node-default {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  width: auto !important;
  height: auto !important;
}

/* Dark mode styles for ReactFlow */

/* Handle dark mode for ReactFlow edges */
.dark .react-flow__edge-path {
  stroke: rgba(255, 255, 255, 0.3);
}

.dark .react-flow__edge.selected .react-flow__edge-path,
.dark .react-flow__edge:focus .react-flow__edge-path,
.dark .react-flow__edge:focus-visible .react-flow__edge-path {
  stroke: hsl(var(--primary));
}

/* Handle dark mode for ReactFlow controls */
.dark .react-flow__controls {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
}

.dark .react-flow__controls-button {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

.dark .react-flow__controls-button:hover {
  background-color: hsl(var(--muted));
}

/* Handle dark mode for ReactFlow node selection */
.dark .react-flow__node.selected {
  box-shadow: 0 0 0 2px hsl(var(--primary));
}

/* Handle dark mode for ReactFlow minimap */
.dark .react-flow__minimap {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
}

/* Handle dark mode for ReactFlow attribution */
.dark .react-flow__attribution {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Handle dark mode for ReactFlow connection line */
.dark .react-flow__connection-path {
  stroke: hsl(var(--primary));
}

/* Handle dark mode for ReactFlow handle */
.dark .react-flow__handle {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--background));
}

/* Handle dark mode for ReactFlow panel */
.dark .react-flow__panel {
  background-color: transparent;
}

/* Handle dark mode for ReactFlow viewport */
.dark .react-flow__viewport {
  transition: background-color 0.3s ease;
}

/* Custom edge styles for better connections */
.react-flow__edge-path {
  stroke-width: 2;
  stroke: rgba(120, 113, 108, 0.6);
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: hsl(var(--primary));
  stroke-width: 3;
}

/* Ensure handles are visible and properly sized */
.react-flow__handle {
  width: 16px !important;
  height: 16px !important;
  background-color: #ff0000 !important; /* Bright red for testing */
  border: 3px solid #ffffff !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 1000 !important;
  pointer-events: all !important;
  transform: none !important;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5) !important;
  position: absolute !important;
}

/* Specific styles for source and target handles */
.react-flow__handle-right {
  right: -8px !important;
}

.react-flow__handle-left {
  left: -8px !important;
}

/* Add hover effect to make handles more noticeable */
.react-flow__handle:hover {
  transform: scale(1.5) !important;
  transition: transform 0.2s ease;
  cursor: crosshair !important;
}

/* Make sure edges are visible when selected */
.react-flow__edge.selected .react-flow__edge-path {
  stroke: #ff0000 !important;
  stroke-width: 3 !important;
  filter: drop-shadow(0 0 5px rgba(255, 0, 0, 0.5)) !important;
}

/* Make sure edges are visible */
.react-flow__edge-path {
  stroke-width: 3 !important;
  stroke: #666666 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Make sure connection lines are visible */
.react-flow__connection-path {
  stroke: #ff0000 !important;
  stroke-width: 3 !important;
  stroke-dasharray: 5, 5 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure controls are interactive */
.react-flow__panel {
  z-index: 50 !important;
  pointer-events: auto !important;
}

.react-flow__controls {
  z-index: 50 !important;
  pointer-events: auto !important;
}

.react-flow__minimap {
  z-index: 50 !important;
  pointer-events: auto !important;
}

/* Ensure buttons and controls are clickable */
.react-flow__panel button,
.react-flow__controls button,
.react-flow__panel a,
.react-flow__controls a {
  pointer-events: auto !important;
}

/* Ensure the workflow controls are above everything else */
.workflow-controls {
  z-index: 1000 !important;
  pointer-events: auto !important;
}

/* Add these styles to ensure the workflow canvas fits the available height */
.workflow-page {
  height: 100% !important; /* Use full height of parent container */
  width: 100% !important;
  overflow: hidden !important;
  position: relative !important;
}

/* Ensure ReactFlow container takes full height */
.react-flow {
  height: 100% !important;
  width: 100% !important;
}

.react-flow__container {
  height: 100% !important;
  width: 100% !important;
}

/* Ensure the parent containers also take full height */
.workflow-container {
  height: 100% !important;
  width: 100% !important;
  position: relative !important;
  overflow: hidden !important;
}
