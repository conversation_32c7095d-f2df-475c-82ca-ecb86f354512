"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { LayoutTemplate, FileText, Globe, BarChart, Code } from "lucide-react";
import { Node, Edge } from "reactflow";

// Template definitions
const templates = [
  {
    id: "text-processing",
    name: "Text Processing",
    description: "Process and transform text data",
    icon: FileText,
    preview: "/templates/text-processing.png",
    nodes: [
      {
        id: "1",
        type: "textInput",
        position: { x: 100, y: 100 },
        data: { label: "Text Input" },
      },
      {
        id: "2",
        type: "transform",
        position: { x: 100, y: 250 },
        data: { label: "Transform" },
      },
      {
        id: "3",
        type: "output",
        position: { x: 100, y: 400 },
        data: { label: "Output", inputValue: "" },
      },
    ],
    edges: [
      { id: "e1-2", source: "1", target: "2" },
      { id: "e2-3", source: "2", target: "3" },
    ],
  },
  {
    id: "api-visualization",
    name: "API Data Visualization",
    description: "Fetch data from an API and visualize it",
    icon: BarChart,
    preview: "/templates/api-visualization.png",
    nodes: [
      {
        id: "1",
        type: "apiRequest",
        position: { x: 100, y: 100 },
        data: { label: "API Request" },
      },
      {
        id: "2",
        type: "chartOutput",
        position: { x: 100, y: 300 },
        data: { label: "Chart Output", inputValue: "" },
      },
    ],
    edges: [
      { id: "e1-2", source: "1", target: "2" },
    ],
  },
  {
    id: "data-transformation",
    name: "Data Transformation",
    description: "Transform and filter data with code",
    icon: Code,
    preview: "/templates/data-transformation.png",
    nodes: [
      {
        id: "1",
        type: "textInput",
        position: { x: 100, y: 100 },
        data: { label: "Text Input" },
      },
      {
        id: "2",
        type: "codeExecution",
        position: { x: 100, y: 250 },
        data: { label: "Code Execution" },
      },
      {
        id: "3",
        type: "output",
        position: { x: 100, y: 400 },
        data: { label: "Output", inputValue: "" },
      },
    ],
    edges: [
      { id: "e1-2", source: "1", target: "2" },
      { id: "e2-3", source: "2", target: "3" },
    ],
  },
  {
    id: "api-processing",
    name: "API Data Processing",
    description: "Fetch, process, and filter API data",
    icon: Globe,
    preview: "/templates/api-processing.png",
    nodes: [
      {
        id: "1",
        type: "apiRequest",
        position: { x: 100, y: 100 },
        data: { label: "API Request" },
      },
      {
        id: "2",
        type: "codeExecution",
        position: { x: 100, y: 250 },
        data: { label: "Code Execution" },
      },
      {
        id: "3",
        type: "filter",
        position: { x: 100, y: 400 },
        data: { label: "Filter" },
      },
      {
        id: "4",
        type: "output",
        position: { x: 100, y: 550 },
        data: { label: "Output", inputValue: "" },
      },
    ],
    edges: [
      { id: "e1-2", source: "1", target: "2" },
      { id: "e2-3", source: "2", target: "3" },
      { id: "e3-4", source: "3", target: "4" },
    ],
  },
];

interface WorkflowTemplatesProps {
  onSelectTemplate: (nodes: Node[], edges: Edge[]) => void;
}

export default function WorkflowTemplates({ onSelectTemplate }: WorkflowTemplatesProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelectTemplate = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId);
    if (template) {
      onSelectTemplate(template.nodes, template.edges);
      setIsOpen(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2 bg-background transition-colors">
          <LayoutTemplate className="h-4 w-4" />
          Templates
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Workflow Templates</DialogTitle>
          <DialogDescription>
            Choose a template to start with a pre-configured workflow
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[400px] mt-4">
          <div className="grid grid-cols-2 gap-4 p-1">
            {templates.map((template) => (
              <div
                key={template.id}
                className="border rounded-lg overflow-hidden hover:border-primary transition-colors cursor-pointer"
                onClick={() => handleSelectTemplate(template.id)}
              >
                <div className="p-4 flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <template.icon className="h-5 w-5 text-primary" />
                    <h3 className="font-medium">{template.name}</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {template.description}
                  </p>
                  <div className="mt-2 bg-muted rounded-md p-2 h-[100px] flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <p className="text-xs">Template Preview</p>
                      <p className="text-xs">{template.nodes.length} nodes, {template.edges.length} connections</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
