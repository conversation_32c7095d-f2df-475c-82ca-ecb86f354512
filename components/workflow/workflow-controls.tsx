"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Save as SaveIcon,
  FolderOpen as FolderOpenIcon,
  MoreHorizontal as MoreHorizontalIcon,
  Trash2 as TrashIcon,
  Bookmark as BookmarkIcon,
  List as ListIcon,
  Plus as PlusIcon,
  File as FileIcon,
  Check as CheckIcon,
  X as XIcon,
  Loader2 as Loader2Icon,
  RotateCcw,
  Pencil as Edit
} from "lucide-react";
import { <PERSON>, Node } from "reactflow";
import { useRouter } from "next/navigation";
import { ScrollArea } from "@/components/ui/scroll-area";
import Link from "next/link";
import { ExecutionControls } from "./execution-controls";

interface WorkflowData {
  id: string;
  name: string;
  description: string | null;
  nodes: Node[];
  edges: Edge[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

type WorkflowControlsProps = {
  nodes: Node[];
  edges: Edge[];
  onSave: (name: string, nodes: Node[], edges: Edge[], description?: string) => Promise<boolean>;
  onLoad: (workflow: { name: string; nodes: Node[]; edges: Edge[] }) => void;
  onClear: () => void;
  onDeleteSelected: () => void;
  onUpdateDetails?: (name: string, description?: string) => Promise<boolean>;
  hasSelectedElements: boolean;
  initialName?: string;
  initialDescription?: string | null;
  workflowId?: string | null;
  userId?: string;
};

export default function WorkflowControls({
  nodes,
  edges,
  onSave,
  onLoad,
  onClear,
  onDeleteSelected,
  onUpdateDetails,
  hasSelectedElements,
  initialName,
  initialDescription,
  workflowId,
  userId,
}: WorkflowControlsProps) {
  const router = useRouter();
  const [workflowName, setWorkflowName] = useState(initialName || "");
  const [workflowDescription, setWorkflowDescription] = useState(initialDescription || "");
  const [savedWorkflows, setSavedWorkflows] = useState<WorkflowData[]>([]);
  const [isOpenSave, setIsOpenSave] = useState(false);
  const [isOpenLoad, setIsOpenLoad] = useState(false);
  const [isOpenDelete, setIsOpenDelete] = useState(false);
  const [isOpenEdit, setIsOpenEdit] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSave = async () => {
    if (!workflowName.trim()) return;

    setIsSaving(true);

    try {
      // Call parent save handler
      const success = await onSave(workflowName, nodes, edges, workflowDescription);

      if (success) {
        // Close dialog
        setIsOpenSave(false);
      }
    } catch (error) {
      console.error('Error saving workflow:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Fetch saved workflows from the API
  const fetchWorkflows = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/workflow');

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to fetch workflows');
      }

      const data = await response.json();
      setSavedWorkflows(data);
    } catch (error) {
      console.error('Error fetching workflows:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch workflows');
    } finally {
      setIsLoading(false);
    }
  };

  // Load workflows when the load dialog is opened
  useEffect(() => {
    if (isOpenLoad) {
      fetchWorkflows();
    }
  }, [isOpenLoad]);

  // Handle loading a workflow
  const handleLoad = (workflow: WorkflowData) => {
    // Parse the JSON strings back to objects
    const parsedNodes = typeof workflow.nodes === 'string'
      ? JSON.parse(workflow.nodes)
      : workflow.nodes;

    const parsedEdges = typeof workflow.edges === 'string'
      ? JSON.parse(workflow.edges)
      : workflow.edges;

    onLoad({
      name: workflow.name,
      nodes: parsedNodes,
      edges: parsedEdges
    });

    setIsOpenLoad(false);

    // Update the URL to reflect the loaded workflow
    if (workflow.id) {
      router.push(`/workflow/${workflow.id}`);
    }
  };

  // Handle updating workflow details
  const handleUpdateDetails = async () => {
    if (!workflowId || !onUpdateDetails) {
      setIsOpenEdit(false);
      return;
    }

    if (!workflowName.trim()) return;

    setIsUpdating(true);

    try {
      const success = await onUpdateDetails(workflowName, workflowDescription);

      if (success) {
        setIsOpenEdit(false);
      }
    } catch (error) {
      console.error('Error updating workflow details:', error);
      setError(error instanceof Error ? error.message : 'Failed to update workflow details');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle deleting a workflow
  const handleDeleteWorkflow = async () => {
    if (!workflowId) return;

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/workflow/${workflowId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete workflow');
      }

      setIsOpenDelete(false);

      // Redirect to workflows list
      router.push('/workflows');
    } catch (error) {
      console.error('Error deleting workflow:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete workflow');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="absolute top-4 right-4 z-50 workflow-controls">
      {/* Single Horizontal Row of Controls */}
      <div className="flex items-center gap-2 bg-background/95 backdrop-blur-sm rounded-md shadow-lg border p-2">
        {/* Execution Controls */}
        {workflowId && userId && (
          <div className="flex items-center gap-2 border-r border-border pr-2">
            <ExecutionControls
              workflowId={workflowId}
              nodes={nodes}
              edges={edges}
              context={{
                workflowId: workflowId,
                userId: userId,
                variables: {},
                secrets: {},
                settings: {},
                log: (message: string) => console.log(`[Workflow ${workflowId}]: ${message}`)
              }}
            />
          </div>
        )}

        {/* Workflow Management Controls */}
        <div className="flex items-center gap-2">
          {/* Delete Selected Button */}
          {hasSelectedElements && (
            <Button
              variant="outline"
              size="icon"
              onClick={onDeleteSelected}
              className="bg-background transition-colors pointer-events-auto h-8 w-8"
            >
              <TrashIcon className="h-4 w-4" />
              <span className="sr-only">Delete Selected</span>
            </Button>
          )}

          {/* Save Dialog */}
          <Dialog open={isOpenSave} onOpenChange={setIsOpenSave}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon" className="bg-background transition-colors pointer-events-auto h-8 w-8">
                <SaveIcon className="h-4 w-4" />
                <span className="sr-only">Save Workflow</span>
              </Button>
            </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Save Workflow</DialogTitle>
            <DialogDescription>
              {workflowId ? "Update your workflow details." : "Enter a name for your new workflow."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={workflowName}
                onChange={(e) => setWorkflowName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="description"
                value={workflowDescription}
                onChange={(e) => setWorkflowDescription(e.target.value)}
                className="col-span-3"
                placeholder="Optional description"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handleSave} disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <SaveIcon className="mr-2 h-4 w-4" />
                  {workflowId ? 'Update' : 'Save'}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

          {/* Load Dialog */}
          <Dialog open={isOpenLoad} onOpenChange={setIsOpenLoad}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon" className="bg-background transition-colors pointer-events-auto h-8 w-8">
                <FolderOpenIcon className="h-4 w-4" />
                <span className="sr-only">Load Workflow</span>
              </Button>
            </DialogTrigger>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>My Workflows</DialogTitle>
            <DialogDescription>
              Select a saved workflow to load or manage your workflows.
            </DialogDescription>
          </DialogHeader>

          {error && (
            <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <div className="py-4">
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2Icon className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : savedWorkflows.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <p>You don't have any workflows yet.</p>
                <p className="mt-2">
                  Click the "New Workflow" button to create your first workflow.
                </p>
                <Button
                  className="mt-4"
                  onClick={() => {
                    setIsOpenLoad(false);
                    router.push('/workflow');
                  }}
                >
                  <PlusIcon className="mr-2 h-4 w-4" />
                  New Workflow
                </Button>
              </div>
            ) : (
              <div className="grid gap-3">
                <ScrollArea className="h-[400px] mt-4">
                  <div className="border rounded-md divide-y">
                    {savedWorkflows.map((workflow) => (
                      <div
                        key={workflow.id}
                        className="p-3 flex items-center justify-between hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium truncate">{workflow.name}</h4>
                          {workflow.description && (
                            <p className="text-sm text-muted-foreground truncate">
                              {workflow.description}
                            </p>
                          )}
                          <p className="text-xs text-muted-foreground mt-1">
                            Last updated: {new Date(workflow.updatedAt).toLocaleString()}
                          </p>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleLoad(workflow)}
                          >
                            <FileIcon className="h-4 w-4 mr-1" />
                            Open
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
                <div className="flex justify-end mt-2">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => {
                        setIsOpenLoad(false);
                        router.push('/workflow');
                      }}
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      New Workflow
                    </Button>
                  </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Details Dialog */}
      {workflowId && onUpdateDetails && isOpenEdit && (
        <Dialog open={isOpenEdit} onOpenChange={setIsOpenEdit}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit Workflow Details</DialogTitle>
              <DialogDescription>
                Update the name and description of your workflow.
              </DialogDescription>
            </DialogHeader>
            <form className="space-y-4 py-4" onSubmit={(e) => { e.preventDefault(); handleUpdateDetails(); }}>
              <div className="space-y-2">
                <Label htmlFor="edit-name">Name</Label>
                <Input
                  id="edit-name"
                  value={workflowName}
                  onChange={(e) => setWorkflowName(e.target.value)}
                  placeholder="My Workflow"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">Description (optional)</Label>
                <Textarea
                  id="edit-description"
                  value={workflowDescription}
                  onChange={(e) => setWorkflowDescription(e.target.value)}
                  placeholder="Describe your workflow"
                  rows={4}
                  className="min-h-[100px]"
                />
              </div>
            </form>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsOpenEdit(false)}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpdateDetails}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <>
                    <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Edit className="mr-2 h-4 w-4" />
                    Update
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Dialog */}
      {workflowId && (
        <Dialog open={isOpenDelete} onOpenChange={setIsOpenDelete}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Delete Workflow</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this workflow? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p className="font-medium">{workflowName}</p>
              {workflowDescription && (
                <p className="text-muted-foreground text-sm mt-1">{workflowDescription}</p>
              )}
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsOpenDelete(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteWorkflow}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <>
                    <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <TrashIcon className="mr-2 h-4 w-4" />
                    Delete
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

          {/* More Options */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="bg-background transition-colors pointer-events-auto h-8 w-8">
                <MoreHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">More Options</span>
              </Button>
            </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Workflow Options</DropdownMenuLabel>
          <DropdownMenuSeparator />

          <DropdownMenuItem onClick={onClear}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Clear Canvas
          </DropdownMenuItem>

          {workflowId && (
            <>
              <DropdownMenuSeparator />
              {onUpdateDetails && (
                <DropdownMenuItem
                  onClick={() => setIsOpenEdit(true)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Details
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => setIsOpenDelete(true)}
                className="text-destructive focus:text-destructive"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                Delete Workflow
              </DropdownMenuItem>
            </>
          )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
