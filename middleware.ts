import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Paths that require authentication
  const protectedPaths = [
    '/profile',
    '/dashboard',
    '/developer',
    '/marketplace/subscription',
    '/marketplace/library',
    '/marketplace/updates',
    '/admin'
  ];

  // Check if the path is protected
  const isProtectedPath = protectedPaths.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  );

  // If path is not protected, allow access
  if (!isProtectedPath) {
    return NextResponse.next();
  }

  // For protected paths, check for NextAuth session
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  });

  // If no token, redirect to login
  if (!token) {
    const url = new URL('/login', request.url);
    url.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(url);
  }

  // Check admin access for admin routes
  if (pathname.startsWith('/admin')) {
    // Get user data from token
    const isAdmin = (token as any).isAdmin;
    const isSuperAdmin = (token as any).isSuperAdmin;

    if (!isAdmin && !isSuperAdmin) {
      // Redirect to dashboard if not admin
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  // Allow access if authenticated
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - api routes
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
