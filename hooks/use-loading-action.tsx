'use client';

import { useState } from 'react';

interface UseLoadingActionReturn {
  isLoading: boolean;
  loadingMessage: string | null;
  runWithLoading: <T>(action: () => Promise<T>, message?: string) => Promise<T>;
}

export function useLoadingAction(): UseLoadingActionReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState<string | null>(null);

  const runWithLoading = async <T,>(
    action: () => Promise<T>,
    message: string = 'Loading...'
  ): Promise<T> => {
    try {
      setIsLoading(true);
      setLoadingMessage(message);
      return await action();
    } finally {
      setIsLoading(false);
      setLoadingMessage(null);
    }
  };

  return {
    isLoading,
    loadingMessage,
    runWithLoading,
  };
}
