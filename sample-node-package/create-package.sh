#!/bin/bash

# Math Calculator Node Package Creator
# Creates a ZIP package ready for upload to the marketplace

set -e

echo "🔧 Creating Math Calculator Node Package..."

# Package information
PACKAGE_NAME="math-calculator-node"
VERSION="1.0.0"
OUTPUT_DIR="./dist"
PACKAGE_FILE="${PACKAGE_NAME}-${VERSION}.zip"

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Remove existing package if it exists
if [ -f "$OUTPUT_DIR/$PACKAGE_FILE" ]; then
    echo "🗑️  Removing existing package..."
    rm "$OUTPUT_DIR/$PACKAGE_FILE"
fi

# Create temporary directory for package contents
TEMP_DIR=$(mktemp -d)
echo "📁 Using temporary directory: $TEMP_DIR"

# Copy files to temporary directory
echo "📋 Copying package files..."

# Core files
cp package.json "$TEMP_DIR/"
cp index.js "$TEMP_DIR/"
cp component.jsx "$TEMP_DIR/"
cp execution.js "$TEMP_DIR/"
cp README.md "$TEMP_DIR/"
cp icon.svg "$TEMP_DIR/"

# Create screenshots directory in temp
mkdir -p "$TEMP_DIR/screenshots"
cp screenshots/README.md "$TEMP_DIR/screenshots/"

# Store the original directory
ORIGINAL_DIR=$(pwd)

# Create the ZIP package
echo "📦 Creating ZIP package..."
cd "$TEMP_DIR"
zip -r "$PACKAGE_FILE" . -x "*.DS_Store" "*.git*" "node_modules/*" "*.log"

# Move package to output directory
mv "$PACKAGE_FILE" "$ORIGINAL_DIR/$OUTPUT_DIR/"
cd "$ORIGINAL_DIR"

# Clean up temporary directory
rm -rf "$TEMP_DIR"

# Display package information
PACKAGE_PATH="$OUTPUT_DIR/$PACKAGE_FILE"
PACKAGE_SIZE=$(du -h "$PACKAGE_PATH" | cut -f1)

echo ""
echo "✅ Package created successfully!"
echo "📍 Location: $PACKAGE_PATH"
echo "📏 Size: $PACKAGE_SIZE"
echo ""
echo "📋 Package Contents:"
echo "   ✓ package.json (metadata and dependencies)"
echo "   ✓ index.js (main entry point)"
echo "   ✓ component.jsx (React component)"
echo "   ✓ execution.js (workflow execution logic)"
echo "   ✓ README.md (documentation)"
echo "   ✓ icon.svg (node icon)"
echo "   ✓ screenshots/ (preview images directory)"
echo ""
echo "🚀 Ready for upload to the marketplace!"
echo ""
echo "📝 Upload Instructions:"
echo "   1. Go to the Developer section in your application"
echo "   2. Click 'Upload Node' or 'New Node'"
echo "   3. Select the package file: $PACKAGE_FILE"
echo "   4. Fill in any additional required information"
echo "   5. Submit for review"
echo ""
echo "🔍 Package Validation:"
echo "   • Node type: Math Calculator"
echo "   • Category: Transform"
echo "   • Tier: Free"
echo "   • Version: $VERSION"
echo "   • Dependencies: React, ReactFlow, Lucide React"
echo ""

# Verify ZIP contents
echo "📂 Package contents verification:"
unzip -l "$PACKAGE_PATH" | grep -E '\.(json|js|jsx|md|svg)$' | awk '{print "   ✓ " $4}'

echo ""
echo "🎉 Package creation complete!"
