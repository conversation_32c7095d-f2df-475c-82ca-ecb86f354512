#!/usr/bin/env node

/**
 * Build script for Math Calculator Node package
 * Creates a ZIP file ready for upload to the marketplace
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import archiver from 'archiver';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const packageDir = __dirname;
const outputDir = path.join(__dirname, '..', 'dist');
const packageJson = JSON.parse(fs.readFileSync(path.join(packageDir, 'package.json'), 'utf8'));

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Create ZIP file
const outputPath = path.join(outputDir, `${packageJson.name}-${packageJson.version}.zip`);
const output = fs.createWriteStream(outputPath);
const archive = archiver('zip', {
  zlib: { level: 9 } // Maximum compression
});

// Handle archive events
output.on('close', () => {
  console.log(`✅ Package created: ${outputPath}`);
  console.log(`📦 Total size: ${archive.pointer()} bytes`);
  console.log(`🚀 Ready for upload to marketplace!`);
});

archive.on('warning', (err) => {
  if (err.code === 'ENOENT') {
    console.warn('⚠️ Warning:', err.message);
  } else {
    throw err;
  }
});

archive.on('error', (err) => {
  console.error('❌ Error creating package:', err);
  throw err;
});

// Pipe archive data to the file
archive.pipe(output);

// Add files to archive
const filesToInclude = [
  'package.json',
  'index.js',
  'component.jsx',
  'execution.js',
  'README.md',
  'icon.svg'
];

console.log('📁 Adding files to package...');

filesToInclude.forEach(file => {
  const filePath = path.join(packageDir, file);
  if (fs.existsSync(filePath)) {
    archive.file(filePath, { name: file });
    console.log(`   ✓ ${file}`);
  } else {
    console.warn(`   ⚠️ Missing: ${file}`);
  }
});

// Add screenshots if they exist
const screenshotsDir = path.join(packageDir, 'screenshots');
if (fs.existsSync(screenshotsDir)) {
  const screenshots = fs.readdirSync(screenshotsDir);
  screenshots.forEach(screenshot => {
    const screenshotPath = path.join(screenshotsDir, screenshot);
    archive.file(screenshotPath, { name: `screenshots/${screenshot}` });
    console.log(`   ✓ screenshots/${screenshot}`);
  });
}

// Finalize the archive
archive.finalize();

console.log('\n📋 Package Information:');
console.log(`   Name: ${packageJson.name}`);
console.log(`   Version: ${packageJson.version}`);
console.log(`   Description: ${packageJson.description}`);
console.log(`   Author: ${packageJson.author.name}`);
console.log(`   Category: ${packageJson.nodePlugin.category}`);
console.log(`   Tier: ${packageJson.nodePlugin.tier}`);
console.log(`   Tags: ${packageJson.nodePlugin.tags.join(', ')}`);
