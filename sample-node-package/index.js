/**
 * Math Calculator Node - Main Entry Point
 * Sample plugin for the workflow node system
 */

import MathCalculatorComponent from './component.jsx';
import { mathCalculatorExecution } from './execution.js';

// Node metadata following the standard interface
export const nodeMetadata = {
  type: 'mathCalculator',
  label: 'Math Calculator',
  description: 'Perform basic arithmetic operations (add, subtract, multiply, divide)',
  icon: 'Calculator', // Lucide icon name
  category: 'transform',
  needsOnChangeHandler: true,
  isDynamic: true,
  defaultWidth: 280,
  tags: ['math', 'calculator', 'arithmetic', 'numbers', 'utility']
};

// Export the component
export const component = MathCalculatorComponent;

// Export the execution logic
export const execution = mathCalculatorExecution;

// Export node registration for the plugin system
export const nodeRegistration = {
  metadata: nodeMetadata,
  loader: () => Promise.resolve({ default: MathCalculatorComponent }),
  execution: mathCalculatorExecution
};

// Default export for compatibility
export default {
  metadata: nodeMetadata,
  component: MathCalculatorComponent,
  execution: mathCalculatorExecution,
  registration: nodeRegistration
};

// Plugin information for the marketplace
export const pluginInfo = {
  id: 'math-calculator-node',
  name: 'Math Calculator',
  version: '1.0.0',
  description: 'Perform basic arithmetic operations (add, subtract, multiply, divide)',
  longDescription: 'A versatile math calculator node that can perform basic arithmetic operations. Connect two number inputs and select an operation to get the calculated result. Perfect for building mathematical workflows and data processing pipelines.',
  author: {
    name: 'Sample Developer',
    email: '<EMAIL>',
    website: 'https://example.com'
  },
  category: 'transform',
  tier: 'free',
  price: 0,
  tags: ['math', 'calculator', 'arithmetic', 'numbers', 'utility'],
  dependencies: [
    { name: 'react', version: '^18.0.0', required: true },
    { name: 'reactflow', version: '^11.0.0', required: true },
    { name: 'lucide-react', version: '^0.263.0', required: true }
  ],
  permissions: [],
  compatibility: {
    minVersion: '1.0.0'
  },
  screenshots: ['screenshot1.png', 'screenshot2.png'],
  repositoryUrl: 'https://github.com/example/math-calculator-node',
  documentationUrl: 'https://github.com/example/math-calculator-node#readme',
  verified: false,
  featured: false
};
