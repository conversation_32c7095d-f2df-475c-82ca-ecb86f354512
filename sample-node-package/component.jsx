"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Calculator, Plus, Minus, X, Divide } from "lucide-react";

/**
 * Math Calculator Node - Sample Plugin
 * Performs basic arithmetic operations on two numbers
 */
const MathCalculatorNode = memo(({ data, id }) => {
  const [operation, setOperation] = useState(data.operation || "add");
  const [numberA, setNumberA] = useState(data.numberA || 0);
  const [numberB, setNumberB] = useState(data.numberB || 0);
  const [result, setResult] = useState(null);
  const [isCalculating, setIsCalculating] = useState(false);

  // Calculate result when inputs or operation change
  useEffect(() => {
    calculateResult();
  }, [numberA, numberB, operation]);

  // Update connected nodes with result
  useEffect(() => {
    if (result !== null && data.onChange) {
      console.log(`MathCalculatorNode ${id}: Sending result to connected nodes:`, result);
      data.onChange(result.toString());
    }
  }, [result, data, id]);

  const calculateResult = () => {
    setIsCalculating(true);
    
    // Simulate calculation delay for demo purposes
    setTimeout(() => {
      const a = parseFloat(numberA) || 0;
      const b = parseFloat(numberB) || 0;
      let calculatedResult;

      switch (operation) {
        case "add":
          calculatedResult = a + b;
          break;
        case "subtract":
          calculatedResult = a - b;
          break;
        case "multiply":
          calculatedResult = a * b;
          break;
        case "divide":
          calculatedResult = b !== 0 ? a / b : "Error: Division by zero";
          break;
        default:
          calculatedResult = 0;
      }

      setResult(calculatedResult);
      setIsCalculating(false);

      // Update node data for workflow execution
      if (data.onDataUpdate) {
        data.onDataUpdate({
          operation,
          numberA: a,
          numberB: b,
          result: calculatedResult
        });
      }
    }, 300);
  };

  const getOperationIcon = () => {
    switch (operation) {
      case "add": return <Plus className="h-3 w-3" />;
      case "subtract": return <Minus className="h-3 w-3" />;
      case "multiply": return <X className="h-3 w-3" />;
      case "divide": return <Divide className="h-3 w-3" />;
      default: return <Calculator className="h-3 w-3" />;
    }
  };

  const getOperationLabel = () => {
    switch (operation) {
      case "add": return "Addition";
      case "subtract": return "Subtraction";
      case "multiply": return "Multiplication";
      case "divide": return "Division";
      default: return "Calculate";
    }
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <Calculator className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Math Calculator"}
          </Label>
          {isCalculating && (
            <div className="animate-pulse h-2 w-2 rounded-full bg-amber-500 ml-auto" title="Calculating..." />
          )}
          {result !== null && !isCalculating && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Calculated" />
          )}
        </div>

        {/* Operation Selection */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Operation</Label>
          <Select value={operation} onValueChange={setOperation}>
            <SelectTrigger className="h-8 text-sm">
              <SelectValue>
                <div className="flex items-center gap-2">
                  {getOperationIcon()}
                  <span>{getOperationLabel()}</span>
                </div>
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="add">
                <div className="flex items-center gap-2">
                  <Plus className="h-3 w-3" />
                  <span>Addition</span>
                </div>
              </SelectItem>
              <SelectItem value="subtract">
                <div className="flex items-center gap-2">
                  <Minus className="h-3 w-3" />
                  <span>Subtraction</span>
                </div>
              </SelectItem>
              <SelectItem value="multiply">
                <div className="flex items-center gap-2">
                  <X className="h-3 w-3" />
                  <span>Multiplication</span>
                </div>
              </SelectItem>
              <SelectItem value="divide">
                <div className="flex items-center gap-2">
                  <Divide className="h-3 w-3" />
                  <span>Division</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Number Inputs */}
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">Number A</Label>
            <Input
              type="number"
              value={numberA}
              onChange={(e) => setNumberA(e.target.value)}
              placeholder="0"
              className="h-8 text-sm"
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">Number B</Label>
            <Input
              type="number"
              value={numberB}
              onChange={(e) => setNumberB(e.target.value)}
              placeholder="0"
              className="h-8 text-sm"
            />
          </div>
        </div>

        {/* Result Display */}
        {result !== null && (
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">Result</Label>
            <div className="p-2 bg-muted/50 rounded border text-sm font-mono">
              {typeof result === 'number' ? result.toLocaleString() : result}
            </div>
          </div>
        )}

        {/* Calculate Button */}
        <Button
          onClick={calculateResult}
          disabled={isCalculating}
          size="sm"
          className="w-full"
        >
          {isCalculating ? "Calculating..." : "Recalculate"}
        </Button>
      </div>

      {/* Input Handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="numberA"
        style={{ top: '45%', background: '#3b82f6' }}
        title="Number A input"
      />
      <Handle
        type="target"
        position={Position.Left}
        id="numberB"
        style={{ top: '65%', background: '#3b82f6' }}
        title="Number B input"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="result"
        style={{ background: '#10b981' }}
        title="Calculation result output"
      />
    </div>
  );
});

MathCalculatorNode.displayName = "MathCalculatorNode";

export default MathCalculatorNode;
