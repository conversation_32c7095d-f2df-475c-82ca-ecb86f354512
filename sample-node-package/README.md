# Math Calculator Node

A simple and versatile math calculator node for workflow automation. Perform basic arithmetic operations with ease.

## Features

- **Four Basic Operations**: Addition, subtraction, multiplication, and division
- **Dual Input Support**: Accept numbers from connected nodes or manual input
- **Real-time Calculation**: Automatic calculation when inputs change
- **Error Handling**: Proper handling of division by zero and invalid inputs
- **Visual Feedback**: Clear operation icons and status indicators
- **Workflow Integration**: Full support for workflow execution and chaining

## Usage

### In the Workflow Canvas

1. **Add the Node**: Drag the Math Calculator node from the node selector
2. **Configure Operation**: Select your desired operation (add, subtract, multiply, divide)
3. **Set Numbers**: Enter numbers manually or connect input nodes
4. **Connect Output**: Connect the result to other nodes in your workflow

### Input Handles

- **Number A** (left, top): First number for the calculation
- **Number B** (left, bottom): Second number for the calculation

### Output Handle

- **Result** (right): The calculated result

### Supported Operations

| Operation | Symbol | Description |
|-----------|--------|-------------|
| Addition | + | Adds two numbers |
| Subtraction | - | Subtracts second number from first |
| Multiplication | × | Multiplies two numbers |
| Division | ÷ | Divides first number by second |

## Configuration

### Node Settings

- **Operation**: Choose the arithmetic operation to perform
- **Number A**: Default value for the first number (if not connected)
- **Number B**: Default value for the second number (if not connected)

### Input Priority

The node follows this priority order for inputs:
1. **Connected Nodes**: Values from connected input nodes take highest priority
2. **Manual Input**: Values entered directly in the node interface
3. **Default Config**: Fallback to configured default values

## Examples

### Basic Addition
```
Number A: 10
Number B: 5
Operation: Addition
Result: 15
```

### Chained Calculations
```
[Number Input: 100] → [Math Calculator: ÷ 4] → [Math Calculator: + 10] → [Text Output: 35]
```

### Data Processing Pipeline
```
[CSV Input] → [Extract Column] → [Math Calculator: × 1.2] → [Round Numbers] → [Chart Output]
```

## Error Handling

The node handles common error scenarios:

- **Division by Zero**: Returns "Error: Division by zero" message
- **Invalid Numbers**: Treats invalid inputs as 0
- **Missing Inputs**: Uses default values or 0 if no input provided

## Technical Details

### Dependencies

- React 18+
- ReactFlow 11+
- Lucide React (for icons)

### Node Interface

Implements the standard `StandardNodeProps` interface:
- `data`: Node configuration and handlers
- `id`: Unique node identifier

### Execution Support

Full workflow execution support with:
- Input validation
- Error handling
- Result propagation
- Execution logging

## Installation

This node can be installed through the marketplace or uploaded as a custom plugin.

### Via Marketplace
1. Go to the Marketplace
2. Search for "Math Calculator"
3. Click "Install"

### Via Upload
1. Go to Developer section
2. Click "Upload Node"
3. Select the node package ZIP file
4. Fill in the required information
5. Submit for review

## Development

### File Structure
```
math-calculator-node/
├── package.json          # Package metadata and dependencies
├── index.js              # Main entry point and exports
├── component.jsx         # React component implementation
├── execution.js          # Workflow execution logic
├── README.md            # Documentation (this file)
├── icon.svg             # Node icon
└── screenshots/         # Preview images
    ├── screenshot1.png
    └── screenshot2.png
```

### Building

The node is built as an ES module and can be dynamically loaded by the workflow system.

### Testing

Test the node by:
1. Installing in a development environment
2. Creating a test workflow
3. Connecting various input combinations
4. Verifying calculations and error handling

## License

MIT License - Feel free to use and modify as needed.

## Support

For issues or questions:
- GitHub: https://github.com/example/math-calculator-node
- Email: <EMAIL>

## Changelog

### v1.0.0
- Initial release
- Basic arithmetic operations
- Workflow execution support
- Error handling
- Visual feedback
