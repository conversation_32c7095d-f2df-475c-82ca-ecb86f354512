# Screenshots

This directory contains preview images for the Math Calculator Node.

## Files

- `screenshot1.png` - Main node interface showing the calculator with operation selection
- `screenshot2.png` - Node in action within a workflow, connected to other nodes

## Guidelines for Screenshots

When creating actual screenshots for your node:

1. **Resolution**: Use at least 800x600 pixels for clarity
2. **Format**: PNG format with transparency support
3. **Content**: Show the node in different states:
   - Default/empty state
   - Configured with sample data
   - Connected to other nodes in a workflow
   - Showing results/output

## Placeholder Images

Since this is a sample package, actual screenshot files are not included. In a real package, you would include:

```
screenshots/
├── screenshot1.png    # Primary interface view
├── screenshot2.png    # Workflow integration view
└── screenshot3.png    # Results/output view (optional)
```

## Creating Screenshots

To create screenshots for your node:

1. Install the node in your development environment
2. Create a test workflow
3. Configure the node with sample data
4. Take screenshots using your preferred tool
5. Optimize images for web (compress while maintaining quality)
6. Name files descriptively (screenshot1.png, screenshot2.png, etc.)

## Usage in Marketplace

These screenshots will be displayed in:
- Node detail pages
- Marketplace listings
- Search results
- Installation previews

Make sure they accurately represent your node's functionality and appearance.
