{"name": "math-calculator-node", "version": "1.0.0", "description": "A simple math calculator node for basic arithmetic operations", "main": "index.js", "type": "module", "keywords": ["math", "calculator", "arithmetic", "utility"], "author": {"name": "<PERSON><PERSON> Developer", "email": "<EMAIL>", "website": "https://example.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/example/math-calculator-node"}, "dependencies": {"react": "^18.0.0", "reactflow": "^11.0.0", "lucide-react": "^0.263.0"}, "peerDependencies": {"react": ">=18.0.0", "reactflow": ">=11.0.0"}, "engines": {"node": ">=18.0.0"}, "files": ["index.js", "component.jsx", "execution.js", "README.md", "icon.svg"], "nodePlugin": {"id": "math-calculator-node", "name": "Math Calculator", "version": "1.0.0", "description": "Perform basic arithmetic operations (add, subtract, multiply, divide)", "longDescription": "A versatile math calculator node that can perform basic arithmetic operations. Connect two number inputs and select an operation to get the calculated result. Perfect for building mathematical workflows and data processing pipelines.", "category": "transform", "tier": "free", "tags": ["math", "calculator", "arithmetic", "numbers", "utility"], "icon": "icon.svg", "screenshots": ["screenshot1.png", "screenshot2.png"], "compatibility": {"minVersion": "1.0.0"}, "permissions": [], "dependencies": [{"name": "react", "version": "^18.0.0", "required": true}, {"name": "reactflow", "version": "^11.0.0", "required": true}]}}