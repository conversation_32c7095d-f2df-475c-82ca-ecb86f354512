# Math Calculator Node - Testing Guide

This guide provides comprehensive instructions for testing the Math Calculator Node package upload functionality.

## Package Overview

The Math Calculator Node is a sample plugin designed to test the developer node upload system. It demonstrates:

- ✅ Standard node interface implementation
- ✅ Workflow execution support
- ✅ Proper error handling
- ✅ Input/output connectivity
- ✅ Real-time calculation
- ✅ Visual feedback and status indicators

## Package Structure

```
math-calculator-node-1.0.0.zip
├── package.json          # Package metadata and dependencies
├── index.js              # Main entry point and exports
├── component.jsx         # React component implementation
├── execution.js          # Workflow execution logic
├── README.md            # Documentation
├── icon.svg             # Node icon (SVG format)
└── screenshots/         # Preview images directory
    └── README.md        # Screenshots documentation
```

## Testing Scenarios

### 1. Package Upload Test

**Objective**: Verify the upload system can handle the package correctly.

**Steps**:
1. Navigate to Developer section
2. Click "Upload Node" or "New Node"
3. Select `math-calculator-node-1.0.0.zip`
4. Fill in upload form:
   - **Name**: Math Calculator
   - **Version**: 1.0.0
   - **Description**: Perform basic arithmetic operations
   - **Category**: Transform
   - **Tier**: Free
   - **Tags**: math, calculator, arithmetic, utility

**Expected Results**:
- ✅ Package uploads successfully
- ✅ Metadata is extracted correctly
- ✅ Files are processed without errors
- ✅ Node appears in developer dashboard

### 2. Node Installation Test

**Objective**: Test installation from marketplace/developer library.

**Steps**:
1. Go to Marketplace or My Library
2. Find the Math Calculator node
3. Click "Install"
4. Verify installation status

**Expected Results**:
- ✅ Installation completes successfully
- ✅ Node appears in workflow node selector
- ✅ Node is available under "Transform" category

### 3. Workflow Integration Test

**Objective**: Verify the node works correctly in workflows.

**Steps**:
1. Create new workflow
2. Add Math Calculator node to canvas
3. Configure operation (addition, subtraction, etc.)
4. Set input numbers manually
5. Verify calculation result

**Expected Results**:
- ✅ Node renders correctly on canvas
- ✅ UI controls work properly
- ✅ Calculations are accurate
- ✅ Visual feedback shows status

### 4. Node Connectivity Test

**Objective**: Test input/output connections with other nodes.

**Test Workflow**:
```
[Number Input: 10] → [Math Calculator: × 2] → [Text Output]
```

**Steps**:
1. Add Number Input node, set value to 10
2. Add Math Calculator node, set operation to multiply
3. Add Text Output node
4. Connect: Number Input → Math Calculator (Number A)
5. Set Number B to 2 manually
6. Connect: Math Calculator → Text Output
7. Execute workflow

**Expected Results**:
- ✅ Connections work properly
- ✅ Data flows between nodes
- ✅ Result shows 20 in Text Output
- ✅ No connection errors

### 5. Execution Engine Test

**Objective**: Test workflow execution functionality.

**Steps**:
1. Create workflow with Math Calculator
2. Configure inputs and operation
3. Run workflow execution
4. Check execution logs
5. Verify output data

**Expected Results**:
- ✅ Execution starts without errors
- ✅ Logs show calculation steps
- ✅ Output data is correct format
- ✅ Execution completes successfully

### 6. Error Handling Test

**Objective**: Verify proper error handling.

**Test Cases**:

**Division by Zero**:
- Set Number A: 10
- Set Number B: 0
- Set Operation: Division
- Expected: "Error: Division by zero" message

**Invalid Inputs**:
- Connect text node with non-numeric value
- Expected: Treats as 0, continues calculation

**Missing Inputs**:
- Leave inputs empty
- Expected: Uses default values (0)

### 7. Performance Test

**Objective**: Test node performance and responsiveness.

**Steps**:
1. Add multiple Math Calculator nodes to workflow
2. Connect them in chain (output → input)
3. Change values and observe response time
4. Test with large numbers

**Expected Results**:
- ✅ UI remains responsive
- ✅ Calculations complete quickly
- ✅ No memory leaks or crashes
- ✅ Handles large numbers correctly

## Validation Checklist

### Package Structure ✅
- [ ] package.json contains all required metadata
- [ ] Component follows StandardNodeProps interface
- [ ] Execution logic implements proper interface
- [ ] Icon is SVG format and displays correctly
- [ ] README provides clear documentation

### Functionality ✅
- [ ] All four operations work correctly (add, subtract, multiply, divide)
- [ ] Input handles accept connections
- [ ] Output handle sends data to connected nodes
- [ ] Manual input overrides connected input when typing
- [ ] Error handling works for edge cases

### Integration ✅
- [ ] Node appears in correct category
- [ ] Installation/uninstallation works
- [ ] Workflow execution integration
- [ ] Data serialization/deserialization
- [ ] Proper cleanup on node removal

### User Experience ✅
- [ ] Intuitive interface design
- [ ] Clear visual feedback
- [ ] Responsive controls
- [ ] Helpful tooltips and labels
- [ ] Consistent with app theme

## Common Issues and Solutions

### Upload Issues
- **Problem**: Package rejected during upload
- **Solution**: Check package.json format and required fields

### Installation Issues
- **Problem**: Node doesn't appear after installation
- **Solution**: Refresh page or check browser console for errors

### Connection Issues
- **Problem**: Handles don't connect properly
- **Solution**: Verify handle IDs and positions in component

### Execution Issues
- **Problem**: Workflow execution fails
- **Solution**: Check execution.js implementation and error handling

## Success Criteria

The test is successful if:

1. ✅ Package uploads without errors
2. ✅ Node installs and appears in selector
3. ✅ All mathematical operations work correctly
4. ✅ Node connects properly with other nodes
5. ✅ Workflow execution completes successfully
6. ✅ Error handling works as expected
7. ✅ UI is responsive and user-friendly
8. ✅ No console errors or warnings

## Next Steps

After successful testing:

1. **Documentation**: Update any missing documentation
2. **Screenshots**: Add actual screenshots to package
3. **Optimization**: Improve performance if needed
4. **Features**: Add more advanced features
5. **Publishing**: Submit for marketplace review

## Support

For issues during testing:
- Check browser console for errors
- Review network requests in developer tools
- Verify package structure and file contents
- Test with different browsers
- Check application logs for server-side errors
