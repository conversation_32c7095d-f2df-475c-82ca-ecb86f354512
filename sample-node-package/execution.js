/**
 * Math Calculator Node Execution Logic
 * Handles workflow execution for the math calculator node
 */

export const mathCalculatorExecution = {
  /**
   * Execute the math calculation
   * @param {Object} inputs - Input data from connected nodes
   * @param {Object} config - Node configuration
   * @param {Object} context - Execution context with logging
   * @returns {Promise<Object>} - Execution result
   */
  execute: async (inputs, config, context) => {
    try {
      context.log("Math Calculator Node: Starting execution");

      // Extract inputs with fallbacks to config values
      let numberA = 0;
      let numberB = 0;
      let operation = config.operation || "add";

      // Check for inputs from connected nodes
      if (inputs) {
        // Handle numberA input
        if (inputs.numberA !== undefined) {
          numberA = parseFloat(inputs.numberA) || 0;
          context.log(`Math Calculator Node: Received numberA from connected node: ${numberA}`);
        } else if (config.numberA !== undefined) {
          numberA = parseFloat(config.numberA) || 0;
          context.log(`Math Calculator Node: Using configured numberA: ${numberA}`);
        }

        // Handle numberB input
        if (inputs.numberB !== undefined) {
          numberB = parseFloat(inputs.numberB) || 0;
          context.log(`Math Calculator Node: Received numberB from connected node: ${numberB}`);
        } else if (config.numberB !== undefined) {
          numberB = parseFloat(config.numberB) || 0;
          context.log(`Math Calculator Node: Using configured numberB: ${numberB}`);
        }

        // Handle operation input (if connected to another node)
        if (inputs.operation) {
          operation = inputs.operation;
          context.log(`Math Calculator Node: Received operation from connected node: ${operation}`);
        }
      }

      // Validate operation
      const validOperations = ["add", "subtract", "multiply", "divide"];
      if (!validOperations.includes(operation)) {
        throw new Error(`Invalid operation: ${operation}. Must be one of: ${validOperations.join(", ")}`);
      }

      // Perform calculation
      let result;
      let operationSymbol;

      switch (operation) {
        case "add":
          result = numberA + numberB;
          operationSymbol = "+";
          break;
        case "subtract":
          result = numberA - numberB;
          operationSymbol = "-";
          break;
        case "multiply":
          result = numberA * numberB;
          operationSymbol = "×";
          break;
        case "divide":
          if (numberB === 0) {
            throw new Error("Division by zero is not allowed");
          }
          result = numberA / numberB;
          operationSymbol = "÷";
          break;
        default:
          throw new Error(`Unsupported operation: ${operation}`);
      }

      // Round result to avoid floating point precision issues
      result = Math.round(result * 1000000) / 1000000;

      const calculation = `${numberA} ${operationSymbol} ${numberB} = ${result}`;
      context.log(`Math Calculator Node: Calculation completed: ${calculation}`);

      // Return execution result
      return {
        success: true,
        data: {
          result: result,
          calculation: calculation,
          operation: operation,
          numberA: numberA,
          numberB: numberB,
          operationSymbol: operationSymbol
        },
        outputs: {
          result: result,
          calculation: calculation,
          data: result // For compatibility with other nodes
        },
        metadata: {
          executionTime: Date.now(),
          nodeType: "math-calculator",
          version: "1.0.0"
        }
      };

    } catch (error) {
      context.log(`Math Calculator Node: Execution failed: ${error.message}`);
      
      return {
        success: false,
        error: error.message,
        data: null,
        outputs: {
          result: null,
          calculation: `Error: ${error.message}`,
          data: null
        },
        metadata: {
          executionTime: Date.now(),
          nodeType: "math-calculator",
          version: "1.0.0",
          error: true
        }
      };
    }
  },

  /**
   * Validate node configuration
   * @param {Object} config - Node configuration to validate
   * @returns {Object} - Validation result
   */
  validate: (config) => {
    const errors = [];
    const warnings = [];

    // Check if operation is valid
    const validOperations = ["add", "subtract", "multiply", "divide"];
    if (config.operation && !validOperations.includes(config.operation)) {
      errors.push(`Invalid operation: ${config.operation}. Must be one of: ${validOperations.join(", ")}`);
    }

    // Check if numbers are valid (if provided)
    if (config.numberA !== undefined && isNaN(parseFloat(config.numberA))) {
      warnings.push("Number A is not a valid number, will default to 0");
    }

    if (config.numberB !== undefined && isNaN(parseFloat(config.numberB))) {
      warnings.push("Number B is not a valid number, will default to 0");
    }

    // Check for division by zero
    if (config.operation === "divide" && parseFloat(config.numberB) === 0) {
      errors.push("Division by zero is not allowed");
    }

    return {
      valid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  },

  /**
   * Get node input/output schema
   * @returns {Object} - Schema definition
   */
  getSchema: () => {
    return {
      inputs: {
        numberA: {
          type: "number",
          required: false,
          description: "First number for calculation",
          defaultValue: 0
        },
        numberB: {
          type: "number", 
          required: false,
          description: "Second number for calculation",
          defaultValue: 0
        },
        operation: {
          type: "string",
          required: false,
          description: "Math operation to perform",
          enum: ["add", "subtract", "multiply", "divide"],
          defaultValue: "add"
        }
      },
      outputs: {
        result: {
          type: "number",
          description: "Calculation result"
        },
        calculation: {
          type: "string",
          description: "Human-readable calculation string"
        },
        data: {
          type: "number",
          description: "Result data for compatibility with other nodes"
        }
      },
      config: {
        operation: {
          type: "string",
          required: true,
          description: "Default math operation",
          enum: ["add", "subtract", "multiply", "divide"],
          defaultValue: "add"
        },
        numberA: {
          type: "number",
          required: false,
          description: "Default value for number A",
          defaultValue: 0
        },
        numberB: {
          type: "number",
          required: false,
          description: "Default value for number B", 
          defaultValue: 0
        }
      }
    };
  }
};
