<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <!-- Calculator body -->
  <rect x="4" y="2" width="16" height="20" rx="2" ry="2" fill="none" stroke="currentColor"/>
  
  <!-- Display screen -->
  <rect x="6" y="4" width="12" height="3" rx="1" ry="1" fill="currentColor" opacity="0.1"/>
  
  <!-- Button grid -->
  <!-- Row 1 -->
  <circle cx="7" cy="10" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="10" cy="10" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="13" cy="10" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="16" cy="10" r="1" fill="currentColor" opacity="0.5"/>
  
  <!-- Row 2 -->
  <circle cx="7" cy="13" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="10" cy="13" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="13" cy="13" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="16" cy="13" r="1" fill="currentColor" opacity="0.5"/>
  
  <!-- Row 3 -->
  <circle cx="7" cy="16" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="10" cy="16" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="13" cy="16" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="16" cy="16" r="1" fill="currentColor" opacity="0.5"/>
  
  <!-- Row 4 -->
  <circle cx="7" cy="19" r="1" fill="currentColor" opacity="0.3"/>
  <ellipse cx="10.5" cy="19" rx="2" ry="1" fill="currentColor" opacity="0.3"/>
  <circle cx="16" cy="19" r="1" fill="currentColor" opacity="0.7"/>
  
  <!-- Math symbols overlay -->
  <text x="16" y="11" text-anchor="middle" font-size="6" fill="white" font-weight="bold">+</text>
  <text x="16" y="14" text-anchor="middle" font-size="6" fill="white" font-weight="bold">-</text>
  <text x="16" y="17" text-anchor="middle" font-size="6" fill="white" font-weight="bold">×</text>
  <text x="16" y="20" text-anchor="middle" font-size="6" fill="white" font-weight="bold">÷</text>
</svg>
