# 🔧 Node Execution Pattern Template

## 📋 **STANDARDIZED EXECUTION PATTERN FOR ALL NODES**

This template ensures consistent execution implementation across all workflow nodes, both existing and future nodes.

---

## 🎯 **Standard Execution Pattern**

### **1. Execution Definition Structure**
```typescript
// At the end of each node file (e.g., my-node.tsx)
export const myNodeExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    // 1. Extract inputs and config
    const inputData = inputs.data || inputs.text || config.value || "";
    
    // 2. Log execution start
    context.log(`${NODE_NAME} executing with input: "${inputData}"`);
    
    // 3. Validate inputs (if needed)
    if (REQUIRES_INPUT && !inputData) {
      throw new Error('Required input is missing');
    }
    
    // 4. Process the data
    try {
      // Main processing logic here
      const result = await processData(inputData, config);
      
      // 5. Log success
      context.log(`${NODE_NAME} completed successfully`);
      
      // 6. Return outputs
      return {
        // Primary outputs
        result: result,
        data: result,
        
        // Metadata outputs
        success: true,
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime,
        
        // Additional outputs specific to node type
        ...additionalOutputs
      };
      
    } catch (error) {
      // 7. Handle errors
      context.log(`${NODE_NAME} failed: ${error.message}`, 'error');
      throw error;
    }
  },
  
  // 8. Input schema
  inputs: [
    { 
      id: 'data', 
      name: 'Input Data', 
      type: 'string' as const, 
      required: false,
      description: 'Data to process'
    }
    // Add more inputs as needed
  ],
  
  // 9. Output schema
  outputs: [
    { 
      id: 'result', 
      name: 'Result', 
      type: 'string' as const,
      description: 'Processed result'
    },
    { 
      id: 'data', 
      name: 'Data Output', 
      type: 'string' as const,
      description: 'Same as result, for compatibility'
    },
    { 
      id: 'success', 
      name: 'Success Status', 
      type: 'boolean' as const,
      description: 'Whether processing was successful'
    },
    { 
      id: 'timestamp', 
      name: 'Processing Timestamp', 
      type: 'string' as const,
      description: 'When the processing occurred'
    }
    // Add more outputs as needed
  ],
  
  // 10. Execution options
  timeout: 30000, // 30 seconds default
  retryable: true, // Can be retried on failure
  maxRetries: 3
};
```

### **2. Registration Pattern**
```typescript
// In components/workflow/nodes/index.ts

// 1. Import execution definition
import { myNodeExecution } from './my-node';

// 2. Add to appropriate node array
const appropriateNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'myNode',
      label: 'My Node',
      description: 'Description of what this node does',
      icon: MyIcon,
      category: 'appropriate-category',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./my-node'),
    execution: myNodeExecution // Add execution definition
  }
];
```

---

## 📝 **Node Categories and Execution Patterns**

### **Input Nodes** (Category: 'input')
```typescript
// Pattern: Generate data from user input or external sources
execute: async (inputs, config, context) => {
  const userInput = config.value || config.inputValue || "";
  
  return {
    data: userInput,
    text: userInput,
    value: userInput,
    // Type-specific outputs
  };
}
```

### **Output Nodes** (Category: 'output')
```typescript
// Pattern: Display or export data
execute: async (inputs, config, context) => {
  const inputData = inputs.data || inputs.text || inputs.result || "";
  
  // Process for display/export
  const formatted = formatForOutput(inputData, config);
  
  return {
    displayed: true,
    formatted: formatted,
    originalData: inputData
  };
}
```

### **Transform Nodes** (Category: 'transform')
```typescript
// Pattern: Modify or convert data
execute: async (inputs, config, context) => {
  const inputData = inputs.data || inputs.text || "";
  
  // Transform the data
  const transformed = transformData(inputData, config);
  
  return {
    result: transformed,
    data: transformed,
    originalInput: inputData,
    transformationType: config.transformType
  };
}
```

### **Advanced Nodes** (Category: 'advanced')
```typescript
// Pattern: Complex processing with external services
execute: async (inputs, config, context) => {
  const inputData = inputs.data || inputs.text || "";
  
  // Complex processing
  const result = await complexProcessing(inputData, config, context);
  
  return {
    result: result,
    data: result,
    metadata: {
      processingMethod: 'advanced',
      resourcesUsed: ['api', 'computation']
    }
  };
}
```

### **AI Nodes** (Category: 'ai')
```typescript
// Pattern: AI/ML processing
execute: async (inputs, config, context) => {
  const inputData = inputs.data || inputs.text || "";
  
  // AI processing
  const aiResult = await aiProcessing(inputData, config);
  
  return {
    result: aiResult,
    confidence: aiResult.confidence,
    model: config.model,
    processingTime: aiResult.processingTime
  };
}
```

### **Data Nodes** (Category: 'data')
```typescript
// Pattern: Database or external data operations
execute: async (inputs, config, context) => {
  const query = inputs.query || config.query || "";
  
  // Data operation
  const data = await dataOperation(query, config);
  
  return {
    data: data,
    result: data,
    recordCount: data.length,
    query: query
  };
}
```

---

## 🎯 **Implementation Checklist for Each Node**

### **✅ Required Steps:**
1. **Add execution function** with proper error handling
2. **Define input schema** with types and descriptions
3. **Define output schema** with types and descriptions
4. **Set execution options** (timeout, retryable, maxRetries)
5. **Import execution definition** in index.ts
6. **Add execution to registration** in appropriate node array
7. **Test execution logic** with sample data
8. **Document execution behavior** in node comments

### **✅ Quality Standards:**
- **Error Handling**: Graceful failure with meaningful error messages
- **Type Safety**: Proper TypeScript types for all inputs/outputs
- **Logging**: Comprehensive logging for debugging
- **Performance**: Reasonable timeouts and resource usage
- **Compatibility**: Consistent input/output naming conventions
- **Documentation**: Clear descriptions for all inputs/outputs

---

## 🚀 **Future Node Development Pattern**

### **For New Nodes (Upload/Install):**
```typescript
// 1. Create node component with UI
const MyNewNode = memo(({ data, id }: StandardNodeProps) => {
  // UI implementation
});

// 2. Add execution definition
export const myNewNodeExecution = {
  execute: async (inputs, config, context) => {
    // Execution logic following standard pattern
  },
  inputs: [/* Input schema */],
  outputs: [/* Output schema */],
  timeout: 30000,
  retryable: true
};

// 3. Export both
export default MyNewNode;
```

### **For Marketplace Nodes:**
```typescript
// Node definition should include execution
const nodeDefinition = {
  metadata: {
    type: 'marketplaceNode',
    label: 'Marketplace Node',
    // ... other metadata
  },
  execution: {
    execute: async (inputs, config, context) => {
      // Execution logic
    },
    inputs: [/* Schema */],
    outputs: [/* Schema */]
  }
};
```

---

## 🎊 **Benefits of Standardized Pattern**

### **Developer Experience:**
- **Consistent API**: Same pattern across all nodes
- **Easy Testing**: Standardized testing approach
- **Quick Development**: Template-based development
- **Clear Documentation**: Consistent documentation format

### **User Experience:**
- **Predictable Behavior**: All nodes behave consistently
- **Better Error Messages**: Standardized error handling
- **Reliable Execution**: Consistent timeout and retry behavior
- **Clear Input/Output**: Standardized data flow

### **System Benefits:**
- **Maintainability**: Easy to update and maintain
- **Scalability**: Easy to add new nodes
- **Debugging**: Consistent logging and error reporting
- **Performance**: Optimized execution patterns

**🚀 This pattern ensures all nodes, both existing and future, follow the same high-quality execution standards!**
