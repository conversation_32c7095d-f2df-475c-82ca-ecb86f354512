# Generate Text Node Enhancements

This document describes the recent enhancements made to the generate-text-node component for improved usability and functionality.

## Overview

The generate-text-node has been enhanced with three major improvements:
1. **Scrollable Response Preview**: Better handling of long AI-generated responses
2. **Improved Output Connectivity**: Enhanced data flow to connected nodes
3. **Direct Output Mode**: Raw format output for specialized use cases

## New Features

### 📜 Scrollable Response Preview

#### Before
- Fixed height response area that could overflow
- No clear visual separation between sections
- Limited space for viewing long responses

#### After
- **Structured Layout**: Clear header, scrollable content, and status footer
- **Fixed Height Scroll Area**: 128px (h-32) scrollable container for responses
- **Visual Hierarchy**: Distinct sections with proper borders and backgrounds
- **Action Buttons**: Easily accessible copy and regenerate buttons in header

#### Implementation
```tsx
<ScrollArea className="h-32 w-full">
  <div className="p-3 text-sm whitespace-pre-wrap break-words">
    {generatedText}
  </div>
</ScrollArea>
```

### 🔗 Enhanced Output Connectivity

#### Before
- Used `data.onGenerate` callback (non-standard)
- Inconsistent with other node types
- Limited integration with workflow system

#### After
- **Standardized Output**: Uses `data.onChange` callback like other nodes
- **Automatic Registration**: Included in `needsOnChangeHandler` function
- **Consistent Data Flow**: Follows same pattern as other workflow nodes
- **Debug Logging**: Console logs for tracking data flow

#### Implementation
```tsx
// Update connected nodes with generated text
useEffect(() => {
  if (generatedText && data.onChange) {
    console.log(`GenerateTextNode ${id}: Sending generated text to connected nodes:`, generatedText);
    data.onChange(generatedText);
  }
}, [generatedText, data, id]);
```

### 🔗 Direct Output Mode

#### Before
- Only standard text output available
- No option for raw format output
- Limited integration with specialized workflows

#### After
- **Toggle Option**: Switch between standard and direct output modes
- **Raw API Response**: When enabled, outputs the complete JSON response from AI APIs
- **Visual Indicators**: Clear UI feedback showing current mode
- **Persistent Settings**: Direct output preference saved per node

#### Implementation
```tsx
// Store both processed text and raw API response
const [generatedText, setGeneratedText] = useState<string>("");
const [rawResponse, setRawResponse] = useState<any>(null);

// Direct output logic
const outputData = directOutput
  ? (rawResponse ? JSON.stringify(rawResponse, null, 2) : generatedText)
  : generatedText;

// Settings UI
<Switch
  id="direct-output"
  checked={directOutput}
  onCheckedChange={setDirectOutput}
/>
```

#### Use Cases
- **API Integration**: Full API response data for system-to-system communication
- **Data Analysis**: Complete response metadata and structure
- **Debugging**: Raw API responses for troubleshooting
- **Advanced Processing**: Access to confidence scores, tokens, and other metadata

## UI/UX Improvements

### Response Preview Layout

```
┌─────────────────────────────────────┐
│ Generated Response        [📋] [🔄] │ ← Header with actions
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │                                 │ │ ← Scrollable content area
│ │ AI generated text appears here  │ │   (fixed height: 128px)
│ │ with proper line breaks and     │ │
│ │ formatting preserved...         │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Output: Connected nodes will receive │ ← Status footer
│ this text                    ✓ Ready │
└─────────────────────────────────────┘
```

### Visual Indicators

- **Header**: "Generated Response" label with action buttons
- **Content**: Scrollable area with preserved formatting
- **Footer**: Output status with mode indicator (Ready/Direct)
- **Settings**: Direct Output toggle with explanatory text
- **Styling**: Consistent with shadcn/ui design system

### Settings Dialog Enhancements

```
┌─────────────────────────────────────┐
│ AI Settings                         │
├─────────────────────────────────────┤
│ API Key:     [******************]   │
│ Model:       [Gemini 1.5 Flash ▼]  │
│ Temperature: [0.7] (0=det, 1=crea)  │
│ Max Tokens:  [500]                  │
│ Direct Output: [○] Output raw API    │
│                response JSON instead│
│                of processed text    │
├─────────────────────────────────────┤
│ [Clear]              [Save Settings]│
└─────────────────────────────────────┘
```

## Technical Details

### Data Flow Architecture

1. **Input**: User enters prompt
2. **Processing**: AI API generates response
3. **Storage**: Response stored in `generatedText` state
4. **Output**: Automatic propagation to connected nodes via `onChange`
5. **Display**: Scrollable preview with enhanced UI

### Integration Points

#### Workflow Container
- Generate text node included in `needsOnChangeHandler` function
- Automatic `onChange` handler assignment during node creation
- Consistent with other node types (fileInput, codeExecution, etc.)

#### Node Registration
```tsx
const needsOnChangeHandler = (nodeType: string | undefined): boolean => {
  return [
    'fileInput', 'numberInput', 'transform', 'filter',
    'mathOperation', 'apiRequest', 'codeExecution',
    'databaseQuery', 'imageProcessing', 'nlp',
    'generateText'  // ← Included here
  ].includes(nodeType);
};
```

## Benefits

### For Users
- **Better Readability**: Long responses are now easily scrollable
- **Clear Status**: Visual indicators show when output is ready
- **Consistent Experience**: Same interaction pattern as other nodes
- **Preserved Formatting**: Text formatting and line breaks maintained
- **Flexible Output**: Choose between standard text or raw format
- **Workflow Integration**: Direct output mode for specialized use cases

### For Developers
- **Standardized API**: Consistent with other node implementations
- **Debugging Support**: Console logging for troubleshooting
- **Maintainable Code**: Follows established patterns
- **Extensible Design**: Easy to add more features
- **Raw Format Support**: Direct output for system integration
- **Persistent Settings**: User preferences saved automatically

## Usage Examples

### Basic Text Generation
1. Add Generate Text node to workflow
2. Configure AI provider and API key
3. Enter prompt and generate text
4. Response appears in scrollable preview
5. Connect to Text Output node to display result

### Workflow Integration
1. Connect Text Input → Generate Text → Text Output
2. Input flows through prompt field
3. Generated text automatically flows to output nodes
4. Real-time updates as content changes

### Long Response Handling
- Responses longer than preview area automatically become scrollable
- Full content preserved and accessible
- Copy functionality works with complete text
- Output nodes receive full content regardless of preview size

### Direct Output Mode Usage
1. Open Generate Text node settings
2. Enable "Direct Output" toggle
3. Generate any AI response
4. Connected nodes receive complete JSON response from AI API
5. Use for debugging, data analysis, or advanced processing workflows

#### Example Raw Output (Gemini API):
```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "Hello! How can I help you today?"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0,
      "safetyRatings": [...]
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 4,
    "candidatesTokenCount": 9,
    "totalTokenCount": 13
  }
}
```

## Future Enhancements

Potential improvements for future versions:
- **Resizable Preview**: Allow users to adjust preview height
- **Response History**: Keep track of previous generations
- **Streaming Support**: Real-time response display as it generates
- **Rich Text Preview**: Markdown rendering for formatted responses
- **Export Options**: Save responses to files

## Compatibility

- ✅ **Backward Compatible**: Existing workflows continue to work
- ✅ **Cross-Provider**: Works with all AI providers (Gemini, DeepSeek, OpenAI, Anthropic)
- ✅ **Responsive Design**: Adapts to different screen sizes
- ✅ **Theme Support**: Works with light and dark themes

## Testing

To test the enhancements:

### Basic Functionality
1. Create a new workflow with Generate Text node
2. Connect to Text Output node
3. Generate a long response (>200 characters)
4. Verify scrollable preview works
5. Confirm output appears in connected node
6. Test copy and regenerate functions

### Direct Output Mode
1. Open Generate Text node settings
2. Enable "Direct Output" toggle
3. Save settings and generate response
4. Verify footer shows "🔗 Direct" status
5. Check connected node receives complete JSON response from AI API
6. Verify JSON includes metadata like tokens, safety ratings, etc.
7. Toggle back to standard mode and verify normal text output
8. Confirm settings persist after page reload

## Dual Output Feature

### Overview
The generate-text-node now supports **dual output handles** for maximum workflow flexibility:

1. **"Full" Output Handle (Blue)**: Complete AI response with narration
2. **"Data" Output Handle (Orange)**: Extracted raw data without explanatory text

### Smart Data Extraction
Automatically identifies and extracts:
- Tables (markdown format)
- Lists (numbered, bulleted, asterisk)
- Code blocks
- JSON objects and arrays
- Key-value pairs
- CSV-like data

### Example Usage
**Prompt**: "Create 5 sample user records"

**AI Response**:
```
Here are 5 sample user records:

1. John Doe, <EMAIL>, 25, Developer
2. Jane Smith, <EMAIL>, 30, Designer
3. Bob Johnson, <EMAIL>, 35, Manager
4. Alice Brown, <EMAIL>, 28, Analyst
5. Charlie Wilson, <EMAIL>, 32, Engineer

These records include name, email, age, and job title.
```

**Output Distribution**:
- **Full Output**: Complete response with explanation
- **Data Output**: Only the 5 numbered records

### Visual Design
```
┌─────────────────────────────┐
│     Generate Text Node      │
│                             │
│ [Response Preview]          │
│                             │
└─────────────────────────────┘
                           Full ●
                           Data ●
```

## Summary

The enhanced generate-text-node provides a comprehensive AI text generation solution with:

- **Multi-Provider Support**: OpenAI, Anthropic, DeepSeek, and Google Gemini
- **Dual Output Handles**: Separate full response and extracted data outputs
- **Smart Data Extraction**: Automatic identification of structured content
- **Flexible Configuration**: Customizable models, temperature, and token limits
- **Enhanced UX**: Scrollable previews, copy functionality, and clear status indicators
- **Direct Output Mode**: Raw API response format for system integration
- **Persistent Settings**: User preferences saved per node
- **Error Handling**: Comprehensive error management and user feedback

## Converter Node Integration

### New Companion Node
The **Converter Node** has been added as a perfect companion to the Generate Text Node:

- **Purpose**: Clean up AI responses and extract structured data
- **Location**: Transform Nodes section in node selector
- **Function**: Removes narration and extracts raw data values

### Perfect Workflow Combination
```
Generate Text → Converter → Output Nodes
     ↓             ↓           ↓
  AI Response   Clean Data   Tables/APIs
```

### Example Use Case
**Prompt**: "make 5 data sample for table"

**Generate Text Output**:
```
Here are 5 sample data entries:
1. John, 25, Developer
2. Jane, 30, Designer
...
These entries include name, age, job.
```

**Converter Output**:
- **Text Handle**: Only the 5 data entries without narration
- **JSON Handle**: Structured JSON with metadata

### Benefits
- **Clean Data**: No AI narration in final output
- **Structured Format**: JSON output for APIs and databases
- **Flexible Routing**: Use both outputs for different purposes
- **Automated Processing**: Real-time conversion as AI generates content

These improvements make the generate-text-node a powerful and user-friendly component for AI-powered workflows while maintaining full compatibility with existing implementations.
