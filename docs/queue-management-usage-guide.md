# 🚀 Queue Management Usage Guide

## Overview

The Queue Management system allows you to monitor and control workflow executions running in the background. This guide shows you how to access and use the queue management features.

## 🎯 How to Access Queue Management

### Method 1: From Workflow Canvas
1. **Open any workflow** in the workflow canvas
2. **Look for the execution panel** in the bottom-right corner
3. **Click the Queue icon** (📋) in the execution panel header
4. This opens the **Background Execution Manager** dialog

### Method 2: From Main Navigation
1. **Navigate to the sidebar** in the main dashboard
2. **Expand "Workflows"** section
3. **Click "Queue Management"**
4. This opens the dedicated queue management page at `/queue-management`

### Method 3: Direct URL
- Navigate directly to: `http://localhost:3000/queue-management`

## 🔧 Queue Management Features

### 1. Queue Statistics Dashboard
- **Queued**: Number of workflows waiting to execute
- **Running**: Number of workflows currently executing
- **Completed**: Total completed executions
- **Failed**: Total failed executions

### 2. Background Execution Controls
- **Execute Now**: Queue workflow for immediate execution (high priority)
- **Schedule Execution**: Schedule workflow for future execution
  - Set priority (1-10, higher = more priority)
  - Set scheduled date/time
  - Pass custom variables as JSON

### 3. Execution History Table
- **Real-time status updates** every 5 seconds
- **Progress tracking** with visual progress bars
- **Execution details**: start time, duration, status
- **Actions**: View results, cancel queued executions

### 4. Result Viewing
- **Execution Results Viewer** with tabbed interface:
  - **Summary**: Execution metrics and status
  - **Table**: Paginated data view of results
  - **Logs**: Execution logs and debug information
  - **Export**: Download results in JSON, CSV, or Excel format

## 🚀 How to Test the System

### Step 1: Create a Simple Workflow
1. Go to `/workflow-canvas`
2. Add a few nodes (e.g., Text Input → Text Output)
3. Connect them with edges
4. Save the workflow

### Step 2: Test Background Execution
1. **Open the queue management** (using any method above)
2. **Click "Execute Now"** to queue the workflow
3. **Watch the queue statistics** update in real-time
4. **Monitor the execution** in the history table

### Step 3: Test Scheduled Execution
1. **Click "Schedule Execution"**
2. **Set a future time** (e.g., 2 minutes from now)
3. **Set priority** to 7
4. **Add variables**: `{"test": "scheduled execution"}`
5. **Click "Schedule"**
6. **Wait and watch** the execution start at the scheduled time

### Step 4: View Results
1. **Click the eye icon** (👁️) next to any completed execution
2. **Explore the results viewer**:
   - Check execution summary
   - Browse data table
   - Review logs
   - Export results

## 🔍 Troubleshooting

### Issue: Queue Management Button Not Visible
**Solution**: Make sure you're in a workflow canvas page with a valid workflow loaded.

### Issue: Executions Not Starting
**Possible Causes**:
1. **No nodes in workflow**: Add at least one node
2. **Invalid workflow structure**: Check for disconnected nodes
3. **Server error**: Check browser console for errors

### Issue: Node Execution Errors
**Common Fixes**:
1. **Check node configuration**: Ensure all required fields are filled
2. **Verify node connections**: Make sure data flows correctly
3. **Review execution logs**: Check the logs tab in results viewer

## 📊 Queue Processing Logic

### Priority System
- **Priority 1-10**: Higher numbers = higher priority
- **Same priority**: First-in-first-out (FIFO)
- **Immediate execution**: Uses priority 8
- **Scheduled execution**: Uses your specified priority

### Concurrent Processing
- **Max concurrent executions**: 3 workflows at once
- **Queue processing**: Checks every 1 second for ready executions
- **Retry logic**: Failed executions retry up to 3 times with lower priority

### Background Processing
- **Non-blocking**: UI remains responsive during execution
- **Real-time updates**: Status changes broadcast to all connected clients
- **Persistent storage**: All executions saved to database

## 🎯 Best Practices

### 1. Workflow Design
- **Keep workflows simple**: Easier to debug and faster to execute
- **Test locally first**: Use regular execution before background queuing
- **Handle errors gracefully**: Use "Continue on Error" for robust workflows

### 2. Queue Management
- **Use appropriate priorities**: Reserve high priorities (8-10) for urgent tasks
- **Monitor queue size**: Don't overwhelm the system with too many executions
- **Clean up old executions**: Regularly review and clean execution history

### 3. Scheduling
- **Account for timezone**: All times are in your local timezone
- **Buffer time**: Add extra time for queue processing delays
- **Test scheduling**: Try short intervals first before long-term schedules

## 🔧 Advanced Features

### Custom Variables
Pass data to your workflow execution:
```json
{
  "inputText": "Hello from scheduled execution",
  "processMode": "batch",
  "outputFormat": "json"
}
```

### Export Options
- **JSON**: Complete execution data with metadata
- **CSV**: Tabular data for spreadsheet analysis
- **Excel**: Structured data with formatting (coming soon)

### API Integration
The queue system exposes REST APIs for programmatic access:
- `POST /api/workflows/{id}/execute-background` - Queue execution
- `GET /api/workflows/{id}/execute-background` - Check status
- `DELETE /api/workflows/{id}/execute-background` - Cancel execution

## 🎉 Success Indicators

You'll know the system is working when you see:
1. ✅ **Queue statistics updating** in real-time
2. ✅ **Executions appearing** in the history table
3. ✅ **Progress bars moving** during execution
4. ✅ **Status changes** from queued → running → completed
5. ✅ **Results available** for viewing and export

## 📞 Need Help?

If you encounter issues:
1. **Check browser console** for JavaScript errors
2. **Review execution logs** in the results viewer
3. **Verify workflow structure** in the canvas
4. **Test with simple workflows** first
5. **Check server logs** for backend errors

The queue management system provides powerful workflow automation capabilities while maintaining a user-friendly interface for monitoring and control.
