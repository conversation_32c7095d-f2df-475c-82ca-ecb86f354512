# DeepSeek AI Integration

This document describes the DeepSeek AI integration in the workflow application's generate-text-node.

## Overview

DeepSeek AI has been integrated as a new AI provider option in the generate-text-node component, alongside OpenAI and Anthropic. DeepSeek provides high-quality language models with competitive pricing and performance.

## Features

### Available Models
- **deepseek-chat**: General-purpose conversational AI model
- **deepseek-coder**: Specialized model for code generation and programming tasks

### API Compatibility
DeepSeek AI uses an OpenAI-compatible API, making integration straightforward and familiar for developers.

## Setup

### 1. Get API Key
1. Visit [DeepSeek Platform](https://platform.deepseek.com/)
2. Sign up for an account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the API key (starts with `sk-`)

### 2. Configure in Application
1. Open a workflow in the workflow canvas
2. Add or select a "Generate Text" node
3. Click the settings (gear) icon
4. Select "DeepSeek Chat" or "DeepSeek Coder" from the model dropdown
5. Enter your DeepSeek API key
6. Configure temperature and max tokens as needed
7. Click "Save Settings"

## API Details

### Endpoint
```
https://api.deepseek.com/v1/chat/completions
```

### Request Format
```json
{
  "model": "deepseek-chat",
  "messages": [
    {
      "role": "user",
      "content": "Your prompt here"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

### Response Format
```json
{
  "choices": [
    {
      "message": {
        "content": "Generated response text"
      }
    }
  ]
}
```

## Configuration Options

### Temperature
- Range: 0.0 to 2.0
- Default: 0.7
- Lower values = more focused and deterministic
- Higher values = more creative and random

### Max Tokens
- Range: 1 to 8192
- Default: 1000
- Controls the maximum length of the generated response

## Error Handling

The integration includes comprehensive error handling for:
- Invalid API keys
- Network connectivity issues
- Rate limiting
- Model-specific errors
- Malformed requests

## Security

- API keys are stored locally in browser localStorage
- Keys are not transmitted to the application server
- All API calls are made directly from the client to DeepSeek
- Keys can be cleared using the "Clear Settings" button

## Pricing

DeepSeek AI offers competitive pricing. Check their [pricing page](https://platform.deepseek.com/pricing) for current rates.

## Troubleshooting

### Common Issues

1. **Invalid API Key Error**
   - Verify your API key is correct
   - Ensure the key starts with `sk-`
   - Check that your DeepSeek account has sufficient credits

2. **Network Errors**
   - Check internet connectivity
   - Verify firewall settings allow HTTPS requests to api.deepseek.com

3. **Rate Limiting**
   - DeepSeek has rate limits based on your plan
   - Wait a moment before retrying
   - Consider upgrading your plan for higher limits

4. **Model Not Available**
   - Ensure you're using supported model names: `deepseek-chat` or `deepseek-coder`
   - Check DeepSeek documentation for model availability

## Testing

A test script is available at `test-deepseek.js` to verify API connectivity:

```bash
node test-deepseek.js
```

Remember to set a valid API key in the script before running.

## Support

For DeepSeek-specific issues:
- [DeepSeek Documentation](https://platform.deepseek.com/docs)
- [DeepSeek Support](https://platform.deepseek.com/support)

For integration issues, check the browser console for detailed error messages.
