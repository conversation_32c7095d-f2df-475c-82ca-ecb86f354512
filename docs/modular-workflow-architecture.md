# Modular Workflow Architecture

## Overview

The workflow system has been refactored to use a modular, plugin-like architecture that makes it easy to add new nodes without modifying core files. This eliminates conflicts and improves maintainability.

## Key Benefits

### 🚀 **Scalability**
- Easy to add new nodes without touching core workflow container
- No more merge conflicts when multiple developers add nodes
- Clean separation of concerns

### 🔧 **Maintainability** 
- Each node is self-contained with its own metadata
- Standardized interfaces across all nodes
- Centralized node registry for easy management

### ⚡ **Performance**
- Dynamic loading of node components
- Better code splitting per node category
- Reduced bundle size for unused nodes

### 👨‍💻 **Developer Experience**
- Clear patterns for node development
- Type-safe interfaces
- Hot-swappable node system

## Architecture Components

### 1. Node Interface (`lib/workflow/node-interface.ts`)
Defines standard interfaces that all nodes must implement:

```typescript
interface NodeMetadata {
  type: string;           // Unique identifier
  label: string;          // Display name
  description: string;    // Brief description
  icon: LucideIcon;      // Icon component
  category: NodeCategory; // Organization category
  needsOnChangeHandler: boolean; // Handler requirement
  isDynamic: boolean;     // Dynamic loading flag
}
```

### 2. Node Registry (`lib/workflow/node-registry.ts`)
Central registry that manages all available nodes:

```typescript
class WorkflowNodeRegistry {
  getAllNodes(): NodeMetadata[]
  getNodesByCategory(category: NodeCategory): NodeMetadata[]
  getNodeComponent(type: string): Promise<ComponentType>
  registerNode(registration: NodeRegistration): void
}
```

### 3. Node Utilities (`lib/workflow/node-utils.ts`)
Helper functions for node management:

```typescript
// Create new nodes
createNewNode(type: string, position: Position, handlers: Handlers): Node

// Process workflow nodes
processWorkflowNodes(savedNodes: any[], handlers: Handlers): Node[]

// Sanitize for saving
sanitizeNodesForSave(nodes: Node[]): any[]
```

### 4. Node Registration (`components/workflow/nodes/index.ts`)
Centralized registration of all node types:

```typescript
const basicNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'textInput',
      label: 'Text Input',
      description: 'Input text data manually',
      icon: TextIcon,
      category: 'input',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./text-input-node')
  }
];
```

## How to Add a New Node

### Step 1: Create the Node Component

Create your node component in `components/workflow/nodes/your-node.tsx`:

```typescript
"use client";

import { memo } from "react";
import { Handle, Position } from "reactflow";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

const YourNode = memo(({ data, id }: StandardNodeProps) => {
  // Your node implementation
  return (
    <div className="p-4 border rounded-md shadow-sm bg-background">
      {/* Your node UI */}
      
      <Handle type="target" position={Position.Left} id="input" />
      <Handle type="source" position={Position.Right} id="output" />
    </div>
  );
});

export default YourNode;
```

### Step 2: Register the Node

Add your node to `components/workflow/nodes/index.ts`:

```typescript
const yourNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'yourNode',
      label: 'Your Node',
      description: 'Description of what your node does',
      icon: YourIcon,
      category: 'advanced', // or 'input', 'output', 'transform', 'ai', 'data'
      needsOnChangeHandler: true, // if your node needs onChange handlers
      isDynamic: true // for dynamic loading
    },
    loader: () => import('./your-node')
  }
];

// Add to registerAllNodes function
export function registerAllNodes(): void {
  // ... existing registrations
  yourNodes.forEach(node => nodeRegistry.registerNode(node));
}
```

### Step 3: That's It!

Your node will automatically appear in the node selector and be available for use. No need to modify:
- ❌ workflow-container.tsx
- ❌ node-selector.tsx  
- ❌ Any core files

## Node Categories

Nodes are organized into categories:

- **input**: Data input and sources
- **output**: Display and export nodes
- **transform**: Data transformation and processing
- **advanced**: Complex processing and integration
- **ai**: AI and machine learning nodes
- **data**: Database and data source nodes

## Migration from Old System

The old hardcoded system has been replaced with:

### Before (Old System)
```typescript
// Had to modify workflow-container.tsx
const nodeTypes: NodeTypes = {
  textInput: TextInputNode,
  newNode: NewNode, // ← Manual addition required
};

// Had to modify getNodeLabel function
const getNodeLabel = (nodeType: string): string => {
  switch (nodeType) {
    case 'newNode': return 'New Node'; // ← Manual addition required
  }
};

// Had to modify needsOnChangeHandler function
const needsOnChangeHandler = (nodeType: string): boolean => {
  return ['newNode'].includes(nodeType); // ← Manual addition required
};
```

### After (New System)
```typescript
// Just register the node - everything else is automatic
const newNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'newNode',
      label: 'New Node',
      needsOnChangeHandler: true,
      // ... other metadata
    },
    loader: () => import('./new-node')
  }
];
```

## Performance Optimizations

1. **Dynamic Loading**: Nodes are loaded only when needed
2. **Code Splitting**: Each node can be in its own bundle
3. **Registry Caching**: Loaded components are cached
4. **Lazy Registration**: Nodes can be registered on-demand

## Best Practices

1. **Use TypeScript**: Leverage the provided interfaces
2. **Follow Naming**: Use kebab-case for node types
3. **Add Metadata**: Provide complete metadata for better UX
4. **Handle Errors**: Implement proper error boundaries
5. **Test Thoroughly**: Test node registration and functionality

## Example: Complete New Node

See `components/workflow/nodes/sample-new-node.tsx` for a complete example of a node built with the new architecture.

This modular system ensures your workflow container remains clean and maintainable as you add more nodes to your project.
