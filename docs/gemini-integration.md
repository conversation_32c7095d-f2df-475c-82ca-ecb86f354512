# Google Gemini AI Integration

This document describes the Google Gemini AI integration in the workflow application's generate-text-node.

## Overview

Google Gemini AI has been integrated as a new AI provider option in the generate-text-node component, alongside OpenAI, Anthropic, and DeepSeek. Gemini provides state-of-the-art multimodal AI capabilities with excellent performance and competitive pricing.

## Features

### Available Models
- **gemini-1.5-flash**: Fast, efficient model optimized for speed and cost-effectiveness
- **gemini-1.5-pro**: Advanced model with superior reasoning capabilities and longer context

### API Features
- Native Google AI API integration
- Support for advanced generation parameters (temperature, topP, topK)
- High-quality text generation with multimodal capabilities
- Competitive pricing and generous free tier

## Setup

### 1. Get API Key
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Select an existing project or create a new one
5. Copy the API key (starts with `AIza`)

### 2. Configure in Application
1. Open a workflow in the workflow canvas
2. Add or select a "Generate Text" node
3. Click the settings (gear) icon
4. Select "Gemini 1.5 Flash" or "Gemini 1.5 Pro" from the model dropdown
5. Enter your Google AI API key
6. Configure temperature and max tokens as needed
7. Click "Save Settings"

## API Details

### Endpoint
```
https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={apiKey}
```

### Request Format
```json
{
  "contents": [{
    "parts": [{
      "text": "Your prompt here"
    }]
  }],
  "generationConfig": {
    "temperature": 0.7,
    "maxOutputTokens": 1000,
    "topP": 0.8,
    "topK": 10
  }
}
```

### Response Format
```json
{
  "candidates": [{
    "content": {
      "parts": [{
        "text": "Generated response text"
      }]
    }
  }]
}
```

## Configuration Options

### Temperature
- Range: 0.0 to 2.0
- Default: 0.7
- Lower values = more focused and deterministic
- Higher values = more creative and random

### Max Output Tokens
- Range: 1 to 8192
- Default: 1000
- Controls the maximum length of the generated response

### Top-P (Nucleus Sampling)
- Range: 0.0 to 1.0
- Default: 0.8
- Controls diversity via nucleus sampling

### Top-K
- Range: 1 to 40
- Default: 10
- Controls diversity by limiting token choices

## Model Comparison

### Gemini 1.5 Flash
- **Best for**: Fast responses, cost-effective usage, simple tasks
- **Strengths**: Speed, efficiency, good quality-to-cost ratio
- **Use cases**: Quick text generation, simple Q&A, content creation

### Gemini 1.5 Pro
- **Best for**: Complex reasoning, detailed analysis, high-quality output
- **Strengths**: Advanced reasoning, longer context, superior quality
- **Use cases**: Complex analysis, detailed explanations, creative writing

## Error Handling

The integration includes comprehensive error handling for:
- Invalid API keys
- Network connectivity issues
- Rate limiting
- Model-specific errors
- Malformed requests
- Empty responses

## Security

- API keys are stored locally in browser localStorage
- Keys are not transmitted to the application server
- All API calls are made directly from the client to Google AI
- Keys can be cleared using the "Clear Settings" button

## Pricing

Google Gemini offers competitive pricing with a generous free tier:
- **Free Tier**: 15 requests per minute, 1 million tokens per day
- **Paid Plans**: Available for higher usage requirements

Check [Google AI Pricing](https://ai.google.dev/pricing) for current rates.

## Troubleshooting

### Common Issues

1. **Invalid API Key Error**
   - Verify your API key is correct
   - Ensure the key starts with `AIza`
   - Check that your Google Cloud project has the Generative AI API enabled

2. **Network Errors**
   - Check internet connectivity
   - Verify firewall settings allow HTTPS requests to generativelanguage.googleapis.com

3. **Rate Limiting**
   - Free tier has limits: 15 requests/minute, 1M tokens/day
   - Wait before retrying or upgrade to paid plan

4. **Model Not Available**
   - Ensure you're using supported model names: `gemini-1.5-flash` or `gemini-1.5-pro`
   - Check Google AI documentation for model availability

5. **Empty Response**
   - Check if content was filtered due to safety policies
   - Try rephrasing your prompt
   - Review Google's content policies

## Testing

A test script is available at `test-gemini.js` to verify API connectivity:

```bash
node test-gemini.js
```

Remember to set a valid API key in the script before running.

## Support

For Google Gemini-specific issues:
- [Google AI Documentation](https://ai.google.dev/docs)
- [Google AI Support](https://developers.google.com/support)
- [Google AI Studio](https://aistudio.google.com/)

For integration issues, check the browser console for detailed error messages.

## Integration Status

✅ **Fully Integrated**: Google Gemini is now fully integrated into the workflow application alongside:
- DeepSeek AI (deepseek-chat, deepseek-coder)
- OpenAI (gpt-3.5-turbo, gpt-4)
- Anthropic Claude (claude-3-haiku, claude-3-sonnet, claude-3-opus)

All AI providers are accessible through the same unified interface in the generate-text-node component.
