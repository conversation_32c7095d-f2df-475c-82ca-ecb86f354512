# AI Integrations Overview

This document provides a comprehensive overview of all AI provider integrations in the workflow application's generate-text-node component.

## Supported AI Providers

The application now supports **4 major AI providers** with **9 different models**:

### 🚀 Google Gemini (Default)
- **gemini-1.5-flash** - Fast, efficient model optimized for speed
- **gemini-1.5-pro** - Advanced model with superior reasoning capabilities
- **API Key Format**: `AIza...`
- **Get API Key**: [Google AI Studio](https://aistudio.google.com/app/apikey)
- **Pricing**: Generous free tier (15 req/min, 1M tokens/day)

### ⚡ DeepSeek AI
- **deepseek-chat** - General-purpose conversational AI model
- **deepseek-coder** - Specialized model for code generation
- **API Key Format**: `sk-...`
- **Get API Key**: [DeepSeek Platform](https://platform.deepseek.com/)
- **Pricing**: Competitive pricing with good performance

### 🤖 OpenAI
- **gpt-3.5-turbo** - Fast and cost-effective model
- **gpt-4** - Most capable model for complex tasks
- **API Key Format**: `sk-...`
- **Get API Key**: [OpenAI Platform](https://platform.openai.com/)
- **Pricing**: Pay-per-use with various tiers

### 🧠 Anthropic Claude
- **claude-3-haiku** - Fast and lightweight model
- **claude-3-sonnet** - Balanced performance and capability
- **claude-3-opus** - Most powerful model for complex reasoning
- **API Key Format**: `sk-ant-...`
- **Get API Key**: [Anthropic Console](https://console.anthropic.com/)
- **Pricing**: Competitive with strong safety features

## Quick Setup Guide

### 1. Access Settings
1. Open any workflow in the workflow canvas
2. Add or select a "Generate Text" node
3. Click the settings (⚙️) icon

### 2. Choose Your Provider
Select from the dropdown:
- **Gemini 1.5 Flash** (recommended for speed)
- **Gemini 1.5 Pro** (recommended for quality)
- **DeepSeek Chat** (good balance)
- **DeepSeek Coder** (for programming)
- **GPT-3.5 Turbo** (fast OpenAI)
- **GPT-4** (premium OpenAI)
- **Claude 3 Haiku** (fast Anthropic)
- **Claude 3 Sonnet** (balanced Anthropic)
- **Claude 3 Opus** (premium Anthropic)

### 3. Configure API Key
Enter your API key in the format:
- `AIza...` for Google Gemini
- `sk-...` for DeepSeek/OpenAI
- `sk-ant-...` for Anthropic

### 4. Adjust Parameters
- **Temperature**: 0.0 (deterministic) to 2.0 (creative)
- **Max Tokens**: 1 to 8192 (response length limit)

## Model Comparison

| Provider | Model | Speed | Quality | Cost | Best For |
|----------|-------|-------|---------|------|----------|
| Google | Gemini 1.5 Flash | ⚡⚡⚡ | ⭐⭐⭐ | 💰 | General use, speed |
| Google | Gemini 1.5 Pro | ⚡⚡ | ⭐⭐⭐⭐ | 💰💰 | Complex reasoning |
| DeepSeek | DeepSeek Chat | ⚡⚡ | ⭐⭐⭐ | 💰 | Conversations |
| DeepSeek | DeepSeek Coder | ⚡⚡ | ⭐⭐⭐⭐ | 💰 | Programming |
| OpenAI | GPT-3.5 Turbo | ⚡⚡⚡ | ⭐⭐⭐ | 💰💰 | Quick tasks |
| OpenAI | GPT-4 | ⚡ | ⭐⭐⭐⭐⭐ | 💰💰💰 | Premium quality |
| Anthropic | Claude 3 Haiku | ⚡⚡⚡ | ⭐⭐⭐ | 💰💰 | Fast responses |
| Anthropic | Claude 3 Sonnet | ⚡⚡ | ⭐⭐⭐⭐ | 💰💰💰 | Balanced use |
| Anthropic | Claude 3 Opus | ⚡ | ⭐⭐⭐⭐⭐ | 💰💰💰💰 | Complex analysis |

## API Architecture

### Request Flow
1. User enters prompt in generate-text-node
2. Component determines provider based on selected model
3. Formats request according to provider's API specification
4. Makes direct API call from browser to provider
5. Processes response and displays generated text

### Security Features
- ✅ API keys stored locally in browser localStorage
- ✅ No server-side API key storage
- ✅ Direct client-to-provider communication
- ✅ Keys can be cleared with "Clear Settings" button
- ✅ Secure HTTPS connections to all providers

### Error Handling
- ✅ Invalid API key detection
- ✅ Network connectivity issues
- ✅ Rate limiting handling
- ✅ Model-specific error messages
- ✅ Graceful fallback for failed requests

## Testing

Test scripts are available for each provider:
- `test-gemini.js` - Google Gemini API testing
- `test-deepseek.js` - DeepSeek API testing

Run tests with:
```bash
node test-gemini.js
node test-deepseek.js
```

## Troubleshooting

### Common Issues
1. **Invalid API Key**: Check format and provider
2. **Rate Limits**: Wait or upgrade plan
3. **Network Errors**: Check connectivity and firewall
4. **Empty Responses**: Review content policies

### Getting Help
- Check browser console for detailed error messages
- Refer to provider-specific documentation
- Verify API key permissions and quotas

## Future Enhancements

Potential future integrations:
- Cohere AI models
- Hugging Face models
- Local model support (Ollama)
- Custom API endpoints

## Documentation

Detailed documentation for each provider:
- [Google Gemini Integration](./GEMINI_INTEGRATION.md)
- [DeepSeek Integration](./DEEPSEEK_INTEGRATION.md)

## Summary

The workflow application now provides a unified interface to access the best AI models from leading providers, giving users flexibility to choose the right model for their specific needs while maintaining security and ease of use.
