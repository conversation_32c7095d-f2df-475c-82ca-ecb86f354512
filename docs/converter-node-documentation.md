# Enhanced Converter Node Documentation

## Overview

The **Enhanced Converter Node** is a powerful data processing component designed to extract and clean structured data from AI responses. Based on the n8n community best practices, it handles nested JSON structures, removes narration, and provides flexible extraction options for different AI API formats.

## Inspired by n8n Community

This implementation is inspired by the n8n community solution for extracting JSON from AI responses, addressing the common issue where AI APIs return nested structures that need proper extraction.

## Key Features

### 🧹 **Smart Data Cleaning**
- Removes AI narration phrases ("Here are...", "Below is...", etc.)
- Strips explanatory text and context
- Extracts only the core data content

### 🔍 **Intelligent Data Detection**
- **Tables**: Markdown format with `|` separators
- **Lists**: Numbered (1., 2., 3.), bulleted (-, *, •)
- **Code Blocks**: Content within ``` markers
- **JSON Objects**: Valid JSON structures
- **Key-Value Pairs**: Colon-separated data
- **CSV-like Data**: Comma-separated values

### 🔗 **Dual Output Handles**
1. **"Text" Handle (Blue)**: Cleaned text without narration
2. **"JSON" Handle (Green)**: Structured data in JSON format

### ⚙️ **Configurable Processing**
- Auto-process mode for real-time conversion
- Manual processing with convert button
- Settings dialog for customization

### 🎯 **Flexible Extraction Modes**
- **Auto Detect**: Automatically determines the best extraction method
- **Nested JSON (AI APIs)**: Handles common AI API response structures
- **JSON Content**: Extracts JSON objects from within text
- **Text Only**: Simple text cleaning without JSON parsing
- **Custom Path**: User-defined JSON path extraction (e.g., `message.content`, `choices[0].text`)

### 🔧 **AI API Compatibility**
Supports common AI API response formats:
- **OpenAI**: `choices[0].message.content`
- **Anthropic**: `content[0].text`
- **Array Responses**: `[0].message.content`
- **Direct Content**: `content`
- **Message Wrapper**: `message.content`

## Visual Design

```
┌─────────────────────────────┐
│ 🔄 Converter    [table] ⚙️  │
├─────────────────────────────┤
│ Input: [AI Response Preview]│
│                             │
│ [🔄 Convert]               │
│                             │
│ Cleaned Text: [Clean Data] │
│ Extracted Data: (5 items)  │
└─────────────────────────────┘
                          Text ●
                          JSON ●
```

## Example Usage

### Example 1: Nested JSON Extraction (AI API Response)

**Input (OpenAI API Response)**:
```json
{
  "choices": [
    {
      "message": {
        "content": "Here are 5 sample data entries:\n1. John Doe, <EMAIL>, 25\n2. Jane Smith, <EMAIL>, 30"
      }
    }
  ]
}
```

**Settings**: Extraction Target = "Nested JSON (AI APIs)" or "Auto Detect"

**Text Output (Cleaned)**:
```
John Doe, <EMAIL>, 25
Jane Smith, <EMAIL>, 30
```

**JSON Output (Structured)**:
```json
{
  "type": "numbered_list",
  "data": [
    "John Doe, <EMAIL>, 25",
    "Jane Smith, <EMAIL>, 30"
  ],
  "metadata": {
    "originalLength": 85,
    "extractedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### Example 2: Custom Path Extraction

**Input (Custom API Response)**:
```json
{
  "result": {
    "ai_response": {
      "text": "1. Apple\n2. Banana\n3. Orange"
    }
  }
}
```

**Settings**:
- Extraction Target = "Custom Path"
- Custom JSON Path = "result.ai_response.text"

**Text Output**:
```
Apple
Banana
Orange
```

### Example 3: Text Only Mode

**Input (Simple AI Response)**:
```
Here are 5 sample data entries for a user table:

1. John Doe, <EMAIL>, 25, Developer
2. Jane Smith, <EMAIL>, 30, Designer
3. Bob Johnson, <EMAIL>, 35, Manager
4. Alice Brown, <EMAIL>, 28, Analyst
5. Charlie Wilson, <EMAIL>, 32, Engineer

These entries include name, email, age, and job title for each user.
```

**Settings**: Extraction Target = "Text Only"

**Text Output (Cleaned)**:
```
John Doe, <EMAIL>, 25, Developer
Jane Smith, <EMAIL>, 30, Designer
Bob Johnson, <EMAIL>, 35, Manager
Alice Brown, <EMAIL>, 28, Analyst
Charlie Wilson, <EMAIL>, 32, Engineer
```

## Data Type Detection

### 📊 **Tables**
**Input**: Markdown table format
```
| Name | Email | Age |
|------|-------|-----|
| John | <EMAIL> | 25 |
| Jane | <EMAIL> | 30 |
```

**Output**: Array of table rows
```json
{
  "type": "table",
  "data": [
    ["Name", "Email", "Age"],
    ["John", "<EMAIL>", "25"],
    ["Jane", "<EMAIL>", "30"]
  ]
}
```

### 📝 **Lists**
**Numbered Lists**: `1. Item`, `2. Item`
**Bullet Lists**: `- Item`, `* Item`, `• Item`

### 💻 **Code Blocks**
**Input**:
```
```javascript
function hello() {
  return "world";
}
```
```

**Output**: Code content without markers

### 🔧 **JSON Objects**
**Input**: Valid JSON structures
**Output**: Parsed JSON objects

### 🗂️ **Key-Value Pairs**
**Input**: `Key: Value` format
**Output**: Object with key-value structure

## Technical Implementation

### Data Extraction Algorithm
```typescript
const extractRawData = (text: string) => {
  // 1. Remove narration patterns
  const narrationPatterns = [
    /^(here\s+(are|is)|below\s+(are|is))[^.!?]*[.!?]/gmi,
    /^(these\s+|this\s+|the\s+following)[^.!?]*[.!?]/gmi,
    // ... more patterns
  ];

  // 2. Detect data structures
  const patterns = [
    /```[\s\S]*?```/g,        // Code blocks
    /\|.*\|[\s\S]*?\|.*\|/g,  // Tables
    /^\d+\..*$/gm,            // Numbered lists
    /^[-*•]\s*(.+)$/gm,       // Bullet lists
    /\{[\s\S]*?\}/g,          // JSON objects
  ];

  // 3. Extract and structure data
  // 4. Return cleaned text and structured JSON
};
```

### Workflow Integration
```typescript
// Text output (cleaned data)
data.onChange(cleanedText);

// JSON output (structured data)
data.onJsonChange(JSON.stringify(structuredData, null, 2));
```

## Settings Configuration

### Extraction Target Options

1. **Auto Detect** (Recommended)
   - Automatically tries nested JSON extraction first
   - Falls back to content extraction if no JSON structure found
   - Best for mixed input types

2. **Nested JSON (AI APIs)**
   - Specifically handles AI API response structures
   - Supports OpenAI, Anthropic, and similar formats
   - Extracts content from nested message structures

3. **JSON Content**
   - Looks for JSON objects within text content
   - Extracts and parses JSON structures
   - Useful for responses containing embedded JSON

4. **Text Only**
   - Simple text cleaning without JSON parsing
   - Removes narration and explanatory text
   - Best for plain text responses

5. **Custom Path**
   - User-defined JSON path extraction
   - Supports dot notation: `message.content`
   - Supports array indices: `choices[0].text`
   - Examples: `result.data`, `response[0].message.content`

### Custom Path Examples

| API Format | Custom Path | Description |
|------------|-------------|-------------|
| OpenAI | `choices[0].message.content` | Standard OpenAI format |
| Anthropic | `content[0].text` | Anthropic Claude format |
| Custom API | `result.ai_response.text` | Custom nested structure |
| Array Response | `[0].message.content` | Array-based response |
| Direct Content | `content` | Simple content field |

## Usage Instructions

### 1. **Add Converter Node**
- Click "Add Node" button
- Select "Transform Nodes" → "Converter"
- Node appears with dual output handles

### 2. **Connect Input**
- Connect AI response output to Converter input
- Node automatically receives AI-generated content

### 3. **Configure Settings**
- Click settings (⚙️) icon
- Select appropriate **Extraction Target**
- Set **Custom JSON Path** if using custom mode
- Enable/disable **Auto Process** mode

### 4. **Process Data**
- **Auto Mode**: Processes automatically when input changes
- **Manual Mode**: Click "Convert" button to process

### 5. **Use Outputs**
- **Text Handle**: Connect to nodes needing clean text
- **JSON Handle**: Connect to nodes needing structured data

## Best Practices

### ✅ **When to Use Converter**
- **Data Extraction**: Remove AI narration from responses
- **Table Processing**: Extract table data for charts/databases
- **List Cleaning**: Get clean list items without formatting
- **JSON Parsing**: Structure data for API consumption
- **Workflow Automation**: Standardize AI outputs

### 📝 **Optimal Input Formats**
For best results, use AI prompts that generate:
- Clear table structures
- Numbered or bulleted lists
- JSON objects
- Code blocks
- Key-value pairs

### 🔄 **Processing Modes**
- **Auto-Process**: Real-time conversion for dynamic workflows
- **Manual Process**: Controlled conversion for review/validation

## Integration Examples

### Example 1: Table Data Pipeline
```
Generate Text → Converter → Table Output
     ↓             ↓           ↓
  "Create 5     Clean CSV    Visual
   user data"    format      Table
```

### Example 2: JSON API Workflow
```
Generate Text → Converter → API Request
     ↓             ↓           ↓
  "Generate     Structured   POST to
   JSON data"    JSON        API
```

### Example 3: List Processing
```
Generate Text → Converter → Filter → Text Output
     ↓             ↓          ↓         ↓
  "List items"  Clean list  Filter   Display
                            items    results
```

## Error Handling

- **Invalid Input**: Returns original text if no structure detected
- **Processing Errors**: Graceful fallback to input content
- **Empty Input**: Shows "Waiting for input..." message
- **Malformed Data**: Attempts best-effort extraction

## Performance

- **Real-time Processing**: Sub-second conversion for most inputs
- **Memory Efficient**: Processes data without storing large buffers
- **Scalable**: Handles inputs from small snippets to large documents

## Future Enhancements

- **Custom Patterns**: User-defined extraction rules
- **Format Conversion**: Auto-convert between CSV, JSON, XML
- **Data Validation**: Verify extracted data structure
- **Preview Mode**: Show extraction preview before processing
- **Batch Processing**: Handle multiple inputs simultaneously

---

The Converter Node bridges the gap between AI-generated content and structured data processing, enabling sophisticated data workflows with clean, usable outputs.
