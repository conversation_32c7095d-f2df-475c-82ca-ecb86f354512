# Marketplace API Reference

## Base URL
```
Production: https://yourplatform.com/api/marketplace
Development: http://localhost:3000/api/marketplace
```

## Authentication
All API requests require authentication via Bearer token:
```
Authorization: Bearer <your-api-token>
```

## Rate Limiting
- **Public endpoints**: 100 requests per minute
- **Authenticated endpoints**: 1000 requests per minute
- **Developer endpoints**: 500 requests per minute

## Response Format
All API responses follow this structure:
```json
{
  "success": true,
  "data": {},
  "error": null,
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  }
}
```

## Endpoints

### Node Discovery

#### GET `/nodes`
Search and list nodes with filtering options.

**Parameters:**
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `search` | string | Search in name/description | - |
| `category` | string | Filter by category | - |
| `tier` | string | Filter by tier (free, premium, enterprise) | - |
| `verified` | boolean | Show only verified nodes | false |
| `featured` | boolean | Show only featured nodes | false |
| `sortBy` | string | Sort order (popularity, rating, newest, price_low, price_high, name) | popularity |
| `page` | integer | Page number (1-based) | 1 |
| `limit` | integer | Items per page (max 100) | 20 |

**Example Request:**
```bash
curl -X GET "https://yourplatform.com/api/marketplace/nodes?search=ai&category=ai_ml&tier=premium&page=1&limit=10" \
  -H "Authorization: Bearer your-token"
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "openai-gpt-node",
        "name": "OpenAI GPT Node",
        "version": "1.2.0",
        "description": "Advanced AI text generation using OpenAI GPT models",
        "author": {
          "name": "AI Innovations",
          "email": "<EMAIL>",
          "avatar": "https://example.com/avatar.jpg"
        },
        "category": "ai_ml",
        "tier": "premium",
        "price": 9.99,
        "tags": ["ai", "gpt", "openai", "text-generation"],
        "icon": "https://example.com/icon.svg",
        "screenshots": ["https://example.com/screenshot1.jpg"],
        "verified": true,
        "featured": true,
        "rating": 4.8,
        "reviewCount": 156,
        "downloads": 12450,
        "weeklyDownloads": 890,
        "lastUpdated": "2024-01-15T10:30:00Z",
        "createdAt": "2023-08-10T15:20:00Z"
      }
    ],
    "total": 45,
    "page": 1,
    "totalPages": 5
  }
}
```

#### GET `/nodes/featured`
Get curated featured nodes.

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "openai-gpt-node",
      "name": "OpenAI GPT Node",
      // ... full node object
    }
  ]
}
```

#### GET `/nodes/popular`
Get popular nodes based on downloads and ratings.

**Parameters:**
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `limit` | integer | Number of nodes to return (max 50) | 10 |

#### GET `/nodes/new`
Get recently published nodes.

**Parameters:**
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `limit` | integer | Number of nodes to return (max 50) | 10 |

#### GET `/nodes/{nodeId}`
Get detailed information about a specific node.

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "openai-gpt-node",
    "name": "OpenAI GPT Node",
    "version": "1.2.0",
    "description": "Advanced AI text generation using OpenAI GPT models",
    "longDescription": "Complete description with usage examples...",
    "author": {
      "name": "AI Innovations",
      "email": "<EMAIL>",
      "website": "https://ai-innovations.com",
      "avatar": "https://example.com/avatar.jpg"
    },
    "category": "ai_ml",
    "tier": "premium",
    "price": 9.99,
    "dependencies": [
      {
        "name": "openai",
        "version": "^4.0.0",
        "optional": false
      }
    ],
    "permissions": [
      {
        "type": "network",
        "description": "Access OpenAI API",
        "required": true
      }
    ],
    "compatibility": {
      "minVersion": "1.0.0",
      "maxVersion": null
    },
    "changelog": [
      {
        "version": "1.2.0",
        "date": "2024-01-15T10:30:00Z",
        "changes": [
          "Added GPT-4 Turbo support",
          "Improved error handling",
          "Performance optimizations"
        ]
      }
    ]
  }
}
```

### Node Installation

#### POST `/nodes/{nodeId}/download`
Get download URL and metadata for node installation.

**Example Response:**
```json
{
  "success": true,
  "data": {
    "downloadUrl": "https://cdn.yourplatform.com/nodes/openai-gpt-node-1.2.0.zip",
    "checksum": "sha256:abc123...",
    "size": 1048576,
    "expiresAt": "2024-01-15T11:30:00Z"
  }
}
```

#### POST `/nodes/{nodeId}/install`
Report installation status.

**Request Body:**
```json
{
  "status": "installing" | "installed" | "error",
  "error": "Error message if status is error"
}
```

#### POST `/nodes/{nodeId}/usage`
Track node usage for analytics.

**Request Body:**
```json
{
  "workflowId": "workflow-uuid",
  "timestamp": "2024-01-15T10:30:00Z",
  "duration": 1500,
  "success": true,
  "error": null
}
```

### Reviews & Ratings

#### GET `/nodes/{nodeId}/reviews`
Get reviews for a specific node.

**Parameters:**
| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `page` | integer | Page number | 1 |
| `limit` | integer | Reviews per page (max 50) | 10 |
| `sortBy` | string | Sort order (newest, oldest, rating_high, rating_low, helpful) | newest |

**Example Response:**
```json
{
  "success": true,
  "data": {
    "reviews": [
      {
        "id": "review-uuid",
        "userId": "user-uuid",
        "userName": "John Doe",
        "userAvatar": "https://example.com/avatar.jpg",
        "rating": 5,
        "title": "Excellent AI integration!",
        "comment": "This node works perfectly with our workflow...",
        "helpful": 12,
        "verified": true,
        "createdAt": "2024-01-10T14:20:00Z"
      }
    ],
    "total": 156,
    "averageRating": 4.8,
    "ratingDistribution": {
      "5": 120,
      "4": 25,
      "3": 8,
      "2": 2,
      "1": 1
    }
  }
}
```

#### POST `/nodes/{nodeId}/reviews`
Submit a review for a node.

**Request Body:**
```json
{
  "rating": 5,
  "title": "Excellent AI integration!",
  "comment": "This node works perfectly with our workflow. The OpenAI integration is seamless and the documentation is comprehensive."
}
```

#### POST `/reviews/{reviewId}/helpful`
Mark a review as helpful.

### Payment Processing

#### POST `/payments/intent`
Create a payment intent for node purchase.

**Request Body:**
```json
{
  "nodeId": "openai-gpt-node",
  "currency": "USD"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "pi_1234567890",
    "clientSecret": "pi_1234567890_secret_abc123",
    "amount": 999,
    "currency": "USD",
    "nodeId": "openai-gpt-node",
    "status": "requires_payment_method"
  }
}
```

#### POST `/payments/{paymentIntentId}/confirm`
Confirm a payment after successful payment method collection.

**Example Response:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "nodeId": "openai-gpt-node",
    "purchaseId": "purchase-uuid"
  }
}
```

### Subscriptions

#### GET `/subscriptions`
Get user's active subscriptions.

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "sub-uuid",
      "planId": "ai-pack-monthly",
      "nodeIds": ["openai-gpt-node", "claude-node", "gemini-node"],
      "status": "active",
      "currentPeriodStart": "2024-01-01T00:00:00Z",
      "currentPeriodEnd": "2024-02-01T00:00:00Z",
      "cancelAtPeriodEnd": false,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST `/subscriptions`
Create a new subscription.

**Request Body:**
```json
{
  "planId": "ai-pack-monthly"
}
```

#### POST `/subscriptions/{subscriptionId}/cancel`
Cancel a subscription.

### User Library

#### GET `/user/nodes`
Get user's purchased and installed nodes.

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "openai-gpt-node",
      "name": "OpenAI GPT Node",
      "version": "1.2.0",
      "installedVersion": "1.2.0",
      "installationStatus": "installed",
      "enabled": true,
      "purchasedAt": "2024-01-01T10:00:00Z",
      "installedAt": "2024-01-01T10:05:00Z",
      "usageCount": 45,
      "lastUsed": "2024-01-15T09:30:00Z"
    }
  ]
}
```

#### GET `/user/purchases`
Get user's purchase history.

**Example Response:**
```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "openai-gpt-node",
        "name": "OpenAI GPT Node",
        "purchaseId": "purchase-uuid",
        "amount": 9.99,
        "currency": "USD",
        "purchasedAt": "2024-01-01T10:00:00Z"
      }
    ],
    "subscriptions": [
      {
        "id": "sub-uuid",
        "planId": "ai-pack-monthly",
        "amount": 15.00,
        "currency": "USD",
        "status": "active"
      }
    ]
  }
}
```

### Developer API

#### POST `/developer/nodes`
Publish a new node (developers only).

**Request Body (multipart/form-data):**
```
name: "My Awesome Node"
version: "1.0.0"
description: "A powerful node for data processing"
longDescription: "Detailed description..."
category: "data_processing"
tier: "premium"
price: 5.99
tags: ["data", "processing", "csv"]
dependencies: [{"name": "lodash", "version": "^4.0.0"}]
permissions: [{"type": "storage", "description": "Read/write files"}]
icon: <file>
screenshots: <file[]>
package: <file>
```

#### PUT `/developer/nodes/{nodeId}`
Update an existing node.

#### GET `/developer/nodes`
Get developer's published nodes.

#### GET `/developer/metrics`
Get developer analytics and revenue data.

**Example Response:**
```json
{
  "success": true,
  "data": {
    "developerId": "dev-uuid",
    "totalNodes": 5,
    "totalDownloads": 15420,
    "activeUsers": 1250,
    "revenue": {
      "total": 2450.75,
      "monthly": 450.25,
      "yearly": 2450.75
    },
    "ratings": {
      "average": 4.6,
      "distribution": {
        "5": 850,
        "4": 200,
        "3": 50,
        "2": 10,
        "1": 5
      }
    },
    "usageStats": {
      "daily": [45, 52, 38, 67, 71, 55, 49],
      "weekly": [350, 420, 380, 445, 510, 475, 390],
      "monthly": [1850, 2100, 1950, 2250, 2400, 2150, 1980]
    }
  }
}
```

### Categories & Tags

#### GET `/categories`
Get all available categories with node counts.

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "category": "ai_ml",
      "name": "AI & Machine Learning",
      "count": 45,
      "featured": true
    },
    {
      "category": "data_processing",
      "name": "Data Processing",
      "count": 78,
      "featured": true
    }
  ]
}
```

#### GET `/tags/popular`
Get popular tags with usage counts.

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "tag": "ai",
      "count": 125
    },
    {
      "tag": "data",
      "count": 98
    }
  ]
}
```

### Analytics

#### GET `/stats`
Get marketplace statistics (public).

**Example Response:**
```json
{
  "success": true,
  "data": {
    "totalNodes": 1250,
    "totalDownloads": 125000,
    "totalDevelopers": 450,
    "categoriesCount": {
      "ai_ml": 45,
      "data_processing": 78,
      "api_integrations": 120
    },
    "recentActivity": [
      {
        "date": "2024-01-15",
        "downloads": 450,
        "newNodes": 5
      }
    ]
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 422 | Unprocessable Entity - Validation failed |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |

## SDKs and Libraries

### JavaScript/TypeScript
```bash
npm install @yourplatform/marketplace-sdk
```

```typescript
import { MarketplaceAPI } from '@yourplatform/marketplace-sdk';

const api = new MarketplaceAPI({
  apiKey: 'your-api-key',
  baseUrl: 'https://yourplatform.com/api/marketplace'
});

const nodes = await api.searchNodes({
  category: 'ai_ml',
  tier: 'premium'
});
```

### Python
```bash
pip install yourplatform-marketplace
```

```python
from yourplatform_marketplace import MarketplaceAPI

api = MarketplaceAPI(api_key='your-api-key')
nodes = api.search_nodes(category='ai_ml', tier='premium')
```
