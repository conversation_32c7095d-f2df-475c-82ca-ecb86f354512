# Developer Guide - Node Plugin Marketplace

## 🚀 Getting Started

This guide will help you create, publish, and monetize custom workflow nodes in the marketplace.

## 📋 Prerequisites

- Node.js 18+ and npm/yarn
- TypeScript knowledge
- React experience
- Understanding of workflow concepts

## 🏗️ Node Development

### 1. Node Structure

Every marketplace node follows the standardized structure:

```typescript
// my-awesome-node.tsx
"use client";

import { useState, useEffect, memo } from "react";
import { Handle, Position } from "reactflow";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MyIcon, ArrowLeftIcon, ArrowRightIcon, AlertCircleIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

/**
 * My Awesome Node - Standardized Structure
 * Description of what this node does
 */
const MyAwesomeNode = memo(({ data, id }: StandardNodeProps) => {
  const [inputValue, setInputValue] = useState(data.inputValue || "");
  const [output, setOutput] = useState("");
  const [error, setError] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // Process input data
  const processData = async () => {
    setIsProcessing(true);
    setError("");
    
    try {
      // Your node logic here
      const result = await myAwesomeFunction(inputValue);
      setOutput(result);
      
      // Update node data for connected nodes
      if (data.onDataChange) {
        data.onDataChange(id, result);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <MyIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "My Awesome Node"}
          </Label>
          {isProcessing && (
            <div className="h-2 w-2 rounded-full bg-yellow-500 ml-auto animate-pulse" />
          )}
          {output && !error && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" />
          )}
          {error && (
            <div className="h-2 w-2 rounded-full bg-red-500 ml-auto" />
          )}
        </div>

        {/* Input Indicator */}
        {data.inputValue && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Processing input</span>
            <ArrowRightIcon className="h-3 w-3" />
          </div>
        )}

        {/* Main Content */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Input</Label>
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Enter your data..."
            className="text-sm"
          />
          <Button 
            onClick={processData} 
            disabled={isProcessing}
            className="w-full text-sm"
          >
            {isProcessing ? "Processing..." : "Process"}
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="flex items-center gap-2 p-2 rounded-md bg-red-50 dark:bg-red-950/20">
            <AlertCircleIcon className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-700 dark:text-red-400">{error}</span>
          </div>
        )}

        {/* Output Display */}
        {output && (
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Output</Label>
            <div className="p-2 bg-muted rounded-md text-sm font-mono">
              {output}
            </div>
          </div>
        )}
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

MyAwesomeNode.displayName = "MyAwesomeNode";

export default MyAwesomeNode;
```

### 2. Node Manifest

Create a `package.json` for your node:

```json
{
  "name": "my-awesome-node",
  "version": "1.0.0",
  "description": "An awesome node for data processing",
  "main": "index.tsx",
  "author": "Your Name <<EMAIL>>",
  "license": "MIT",
  "keywords": ["workflow", "data", "processing"],
  "dependencies": {
    "lodash": "^4.17.21"
  },
  "peerDependencies": {
    "react": "^18.0.0",
    "reactflow": "^11.0.0"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  },
  "marketplace": {
    "category": "data_processing",
    "tier": "premium",
    "price": 4.99,
    "permissions": [
      {
        "type": "network",
        "description": "Access external APIs for data processing",
        "required": true
      }
    ],
    "icon": "./assets/icon.svg",
    "screenshots": [
      "./assets/screenshot1.png",
      "./assets/screenshot2.png"
    ]
  }
}
```

### 3. Node Configuration

Create a configuration interface for your node:

```typescript
// config.ts
export interface MyAwesomeNodeConfig {
  apiKey?: string;
  endpoint?: string;
  timeout?: number;
  retries?: number;
}

export const defaultConfig: MyAwesomeNodeConfig = {
  endpoint: "https://api.example.com",
  timeout: 5000,
  retries: 3
};
```

### 4. Testing Your Node

Create comprehensive tests:

```typescript
// __tests__/my-awesome-node.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import MyAwesomeNode from '../my-awesome-node';

describe('MyAwesomeNode', () => {
  const mockProps = {
    data: {
      label: "Test Node",
      onDataChange: jest.fn()
    },
    id: "test-node-1"
  };

  test('renders correctly', () => {
    render(<MyAwesomeNode {...mockProps} />);
    expect(screen.getByText('Test Node')).toBeInTheDocument();
  });

  test('processes input data', async () => {
    render(<MyAwesomeNode {...mockProps} />);
    
    const input = screen.getByPlaceholderText('Enter your data...');
    const button = screen.getByText('Process');
    
    fireEvent.change(input, { target: { value: 'test input' } });
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(mockProps.data.onDataChange).toHaveBeenCalledWith(
        'test-node-1',
        expect.any(String)
      );
    });
  });

  test('handles errors gracefully', async () => {
    // Mock error scenario
    jest.spyOn(global, 'fetch').mockRejectedValueOnce(new Error('API Error'));
    
    render(<MyAwesomeNode {...mockProps} />);
    
    const button = screen.getByText('Process');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(screen.getByText(/API Error/)).toBeInTheDocument();
    });
  });
});
```

## 📦 Publishing Process

### 1. Prepare Your Package

```bash
# Create package directory
mkdir my-awesome-node
cd my-awesome-node

# Initialize package
npm init -y

# Install dependencies
npm install lodash
npm install --save-dev @types/lodash typescript

# Build your node
npm run build
```

### 2. Package Structure

```
my-awesome-node/
├── package.json
├── README.md
├── CHANGELOG.md
├── src/
│   ├── index.tsx
│   ├── config.ts
│   └── utils.ts
├── assets/
│   ├── icon.svg
│   ├── screenshot1.png
│   └── screenshot2.png
├── __tests__/
│   └── index.test.tsx
└── dist/
    └── index.js
```

### 3. Submit to Marketplace

```typescript
// Using the API
const formData = new FormData();
formData.append('name', 'My Awesome Node');
formData.append('version', '1.0.0');
formData.append('description', 'An awesome node for data processing');
formData.append('category', 'data_processing');
formData.append('tier', 'premium');
formData.append('price', '4.99');
formData.append('tags', JSON.stringify(['data', 'processing', 'api']));
formData.append('package', packageFile);
formData.append('icon', iconFile);
formData.append('screenshots', screenshotFiles);

const response = await fetch('/api/marketplace/developer/nodes', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`
  },
  body: formData
});
```

### 4. Review Process

1. **Automated Validation**: Code analysis and security checks
2. **Manual Review**: Human review for quality and compliance
3. **Testing**: Automated testing in sandbox environment
4. **Approval**: Node becomes available in marketplace

## 💰 Monetization

### Pricing Strategies

#### Free Nodes
- **Use Case**: Community building, portfolio showcase
- **Benefits**: High adoption, user feedback, reputation building
- **Examples**: Basic utilities, simple integrations

#### Premium Nodes ($1-10)
- **Use Case**: Advanced features, popular integrations
- **Benefits**: Steady revenue, professional recognition
- **Examples**: AI/ML nodes, advanced APIs, data processing

#### Enterprise Nodes ($20-100)
- **Use Case**: Complex enterprise integrations
- **Benefits**: High revenue per sale, enterprise relationships
- **Examples**: Salesforce, SAP, custom enterprise systems

### Revenue Optimization

```typescript
// Track usage for pricing insights
export const trackUsage = (nodeId: string, feature: string) => {
  analytics.track('node_feature_used', {
    nodeId,
    feature,
    timestamp: new Date(),
    userId: getCurrentUserId()
  });
};

// A/B test pricing
export const getPricing = (nodeId: string) => {
  const variant = getABTestVariant(nodeId);
  return variant === 'A' ? 4.99 : 5.99;
};
```

## 🔒 Security Best Practices

### 1. Input Validation

```typescript
import { z } from 'zod';

const inputSchema = z.object({
  data: z.string().min(1).max(10000),
  options: z.object({
    format: z.enum(['json', 'csv', 'xml']),
    encoding: z.string().optional()
  }).optional()
});

export const validateInput = (input: unknown) => {
  try {
    return inputSchema.parse(input);
  } catch (error) {
    throw new Error(`Invalid input: ${error.message}`);
  }
};
```

### 2. Secure API Calls

```typescript
export const secureApiCall = async (url: string, data: any) => {
  // Validate URL
  if (!url.startsWith('https://')) {
    throw new Error('Only HTTPS URLs are allowed');
  }
  
  // Rate limiting
  await rateLimiter.check(url);
  
  // Make request with timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000);
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'WorkflowNode/1.0'
      },
      body: JSON.stringify(data),
      signal: controller.signal
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } finally {
    clearTimeout(timeoutId);
  }
};
```

### 3. Error Handling

```typescript
export class NodeError extends Error {
  constructor(
    message: string,
    public code: string,
    public recoverable: boolean = true
  ) {
    super(message);
    this.name = 'NodeError';
  }
}

export const handleError = (error: unknown): NodeError => {
  if (error instanceof NodeError) {
    return error;
  }
  
  if (error instanceof Error) {
    return new NodeError(error.message, 'UNKNOWN_ERROR');
  }
  
  return new NodeError('An unexpected error occurred', 'UNKNOWN_ERROR');
};
```

## 📊 Analytics & Optimization

### 1. Performance Monitoring

```typescript
export const withPerformanceTracking = (fn: Function) => {
  return async (...args: any[]) => {
    const startTime = performance.now();
    
    try {
      const result = await fn(...args);
      const duration = performance.now() - startTime;
      
      analytics.track('node_performance', {
        nodeId: getCurrentNodeId(),
        duration,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      analytics.track('node_performance', {
        nodeId: getCurrentNodeId(),
        duration,
        success: false,
        error: error.message
      });
      
      throw error;
    }
  };
};
```

### 2. Usage Analytics

```typescript
export const trackNodeUsage = (nodeId: string, action: string, metadata?: any) => {
  analytics.track('node_usage', {
    nodeId,
    action,
    metadata,
    timestamp: new Date(),
    userId: getCurrentUserId(),
    workflowId: getCurrentWorkflowId()
  });
};

// Usage examples
trackNodeUsage('my-awesome-node', 'data_processed', { recordCount: 100 });
trackNodeUsage('my-awesome-node', 'api_called', { endpoint: '/process' });
trackNodeUsage('my-awesome-node', 'error_occurred', { errorType: 'validation' });
```

## 🚀 Advanced Features

### 1. Configuration UI

```typescript
// config-dialog.tsx
export const ConfigDialog = ({ config, onSave }: ConfigDialogProps) => {
  return (
    <Dialog>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Node Configuration</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label>API Key</Label>
            <Input
              type="password"
              value={config.apiKey}
              onChange={(e) => setConfig({...config, apiKey: e.target.value})}
            />
          </div>
          <div>
            <Label>Timeout (ms)</Label>
            <Input
              type="number"
              value={config.timeout}
              onChange={(e) => setConfig({...config, timeout: parseInt(e.target.value)})}
            />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={() => onSave(config)}>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
```

### 2. Real-time Updates

```typescript
export const useRealtimeData = (nodeId: string) => {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    const ws = new WebSocket(`wss://api.example.com/nodes/${nodeId}/stream`);
    
    ws.onmessage = (event) => {
      const newData = JSON.parse(event.data);
      setData(newData);
    };
    
    return () => ws.close();
  }, [nodeId]);
  
  return data;
};
```

### 3. Batch Processing

```typescript
export const processBatch = async (items: any[], batchSize: number = 10) => {
  const results = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await Promise.all(
      batch.map(item => processItem(item))
    );
    results.push(...batchResults);
    
    // Progress update
    const progress = Math.round((i + batch.length) / items.length * 100);
    onProgressUpdate?.(progress);
  }
  
  return results;
};
```

## 📚 Resources

### Documentation
- [Node Interface Reference](./node-interface.md)
- [UI Component Library](./ui-components.md)
- [Testing Guidelines](./testing.md)
- [Security Checklist](./security-checklist.md)

### Tools
- [Node Template Generator](https://github.com/yourplatform/node-template)
- [Development CLI](https://github.com/yourplatform/dev-cli)
- [Testing Utilities](https://github.com/yourplatform/test-utils)

### Community
- [Developer Discord](https://discord.gg/yourplatform-dev)
- [GitHub Discussions](https://github.com/yourplatform/marketplace/discussions)
- [Monthly Developer Calls](https://calendar.google.com/yourplatform-dev)

### Support
- **Technical Questions**: <EMAIL>
- **Publishing Issues**: <EMAIL>
- **Revenue Questions**: <EMAIL>
