# Marketplace Testing Guide

## 🧪 Testing Overview

This guide provides comprehensive testing procedures for the Node Plugin Marketplace system to ensure reliability, security, and performance.

## 📋 Testing Checklist

### ✅ Phase 1: Foundation Testing (Current)

#### Marketplace UI Testing
- [ ] **Navigation Integration**
  - [ ] Marketplace link appears in main navigation
  - [ ] Active state highlighting works correctly
  - [ ] Mobile navigation includes marketplace
  - [ ] Breadcrumb navigation functions properly

- [ ] **Marketplace Page Loading**
  - [ ] Page loads without errors at `/marketplace`
  - [ ] Loading states display correctly
  - [ ] Error boundaries handle failures gracefully
  - [ ] Performance metrics are acceptable (<3s load time)

- [ ] **Node Discovery & Search**
  - [ ] Search functionality works with keywords
  - [ ] Category filtering operates correctly
  - [ ] Tier filtering (Free/Premium/Enterprise) functions
  - [ ] Sort options work (popularity, rating, newest, price)
  - [ ] Pagination handles large result sets
  - [ ] Empty states display appropriately

- [ ] **Node Display**
  - [ ] Node cards render with correct information
  - [ ] Screenshots and icons load properly
  - [ ] Pricing displays accurately
  - [ ] Rating and download counts show correctly
  - [ ] Author information is visible
  - [ ] Tags and categories are displayed

#### Node Management Testing
- [ ] **Installation Flow**
  - [ ] Free node installation works
  - [ ] Installation status updates correctly
  - [ ] Progress indicators function
  - [ ] Error handling for failed installations
  - [ ] Success notifications appear

- [ ] **Node Manager Interface**
  - [ ] Installed nodes list displays correctly
  - [ ] Enable/disable toggle functions
  - [ ] Uninstall process works
  - [ ] Usage statistics show accurately
  - [ ] Update notifications appear when available

- [ ] **Plugin Registry**
  - [ ] Nodes register correctly in the system
  - [ ] Plugin metadata is stored properly
  - [ ] Component loading works efficiently
  - [ ] Memory management prevents leaks
  - [ ] Event system functions correctly

#### API Testing
- [ ] **Node Discovery API**
  - [ ] `/api/marketplace/nodes` returns correct data
  - [ ] Search parameters work as expected
  - [ ] Pagination functions properly
  - [ ] Response times are acceptable (<500ms)
  - [ ] Error handling works for invalid requests

- [ ] **Featured Nodes API**
  - [ ] `/api/marketplace/nodes/featured` returns curated content
  - [ ] Featured status is respected
  - [ ] Response format is correct
  - [ ] Caching works appropriately

#### Database Testing
- [ ] **Schema Validation**
  - [ ] All marketplace tables exist
  - [ ] Relationships are properly defined
  - [ ] Indexes are created for performance
  - [ ] Constraints prevent invalid data

- [ ] **Data Integrity**
  - [ ] Node metadata saves correctly
  - [ ] User relationships work properly
  - [ ] Cascading deletes function as expected
  - [ ] Data validation prevents corruption

## 🔧 Manual Testing Procedures

### 1. Marketplace Browsing Test

**Objective**: Verify the marketplace browsing experience works correctly.

**Steps**:
1. Navigate to `/marketplace`
2. Verify page loads with sample nodes
3. Test search functionality:
   - Search for "AI" - should return AI-related nodes
   - Search for "data" - should return data processing nodes
   - Search for non-existent term - should show empty state
4. Test category filtering:
   - Select "AI & ML" category
   - Verify only AI/ML nodes are shown
   - Clear filter and verify all nodes return
5. Test tier filtering:
   - Filter by "Premium" tier
   - Verify only premium nodes are shown
   - Test multiple tier selections
6. Test sorting:
   - Sort by "Rating" - verify order is correct
   - Sort by "Newest" - verify recent nodes appear first
   - Sort by "Price" - verify price ordering
7. Test view modes:
   - Switch between grid and list views
   - Verify layout changes appropriately
   - Verify all information is still visible

**Expected Results**:
- All search and filter operations work smoothly
- Results update without page refresh
- Loading states appear during operations
- Empty states show helpful messages
- Performance is responsive (<1s for filter operations)

### 2. Node Installation Test

**Objective**: Verify node installation process works correctly.

**Steps**:
1. Navigate to marketplace
2. Find a free node (e.g., "Webhook Listener")
3. Click "Install" button
4. Verify installation status updates:
   - Button shows "Installing..." with loading indicator
   - Status changes to "Installed" with checkmark
   - Toast notification appears confirming installation
5. Navigate to Node Manager tab
6. Verify installed node appears in list:
   - Node is listed with correct metadata
   - Status shows "Installed"
   - Enable toggle is active
7. Test node in workflow:
   - Create new workflow
   - Verify installed node appears in node palette
   - Add node to workflow canvas
   - Verify node renders correctly

**Expected Results**:
- Installation completes without errors
- Status updates are immediate and accurate
- Node becomes available in workflow editor
- All metadata is preserved correctly

### 3. Node Management Test

**Objective**: Verify node management functionality works correctly.

**Steps**:
1. Navigate to Node Manager
2. Verify statistics display correctly:
   - Total nodes count is accurate
   - Enabled/disabled counts are correct
   - Update count shows available updates
3. Test node toggle functionality:
   - Disable an enabled node
   - Verify status updates immediately
   - Verify node disappears from workflow palette
   - Re-enable the node
   - Verify node reappears in workflow palette
4. Test node uninstallation:
   - Select a test node for uninstallation
   - Click uninstall button
   - Confirm uninstallation in dialog
   - Verify node is removed from list
   - Verify node is removed from workflow palette

**Expected Results**:
- All statistics are accurate and update in real-time
- Toggle operations work immediately
- Uninstallation removes node completely
- No errors occur during any operations

### 4. Error Handling Test

**Objective**: Verify error scenarios are handled gracefully.

**Steps**:
1. Test network failure scenarios:
   - Disconnect internet
   - Try to load marketplace
   - Verify error message appears
   - Reconnect and verify recovery
2. Test invalid API responses:
   - Mock API to return 500 error
   - Verify error boundaries catch issues
   - Verify user-friendly error messages
3. Test malformed data:
   - Mock API to return invalid JSON
   - Verify application doesn't crash
   - Verify fallback behavior works

**Expected Results**:
- Application remains stable during errors
- User-friendly error messages appear
- Recovery mechanisms work when possible
- No data corruption occurs

## 🤖 Automated Testing

### Unit Tests

```bash
# Run all marketplace unit tests
npm test -- --testPathPattern=marketplace

# Run specific test suites
npm test -- marketplace/plugin-registry.test.ts
npm test -- marketplace/api.test.ts
npm test -- marketplace/components.test.tsx
```

### Integration Tests

```bash
# Run marketplace integration tests
npm run test:integration -- --grep="marketplace"

# Test API endpoints
npm run test:api -- marketplace
```

### End-to-End Tests

```bash
# Run full marketplace E2E tests
npm run test:e2e -- --spec="marketplace/**"

# Run specific E2E scenarios
npm run test:e2e -- --spec="marketplace/installation.spec.ts"
```

### Performance Tests

```bash
# Run performance benchmarks
npm run test:performance -- marketplace

# Load testing
npm run test:load -- --target=marketplace
```

## 📊 Performance Testing

### Metrics to Monitor

1. **Page Load Performance**
   - Initial page load: <3 seconds
   - Search operations: <1 second
   - Filter operations: <500ms
   - Node installation: <5 seconds

2. **Memory Usage**
   - Plugin registry memory usage
   - Component mounting/unmounting
   - Memory leak detection
   - Garbage collection efficiency

3. **API Performance**
   - Node search API: <500ms
   - Node download API: <2 seconds
   - Database query performance
   - Concurrent user handling

### Performance Test Scripts

```javascript
// performance-test.js
const { chromium } = require('playwright');

async function testMarketplacePerformance() {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  // Measure page load time
  const startTime = Date.now();
  await page.goto('http://localhost:3000/marketplace');
  await page.waitForSelector('[data-testid="marketplace-loaded"]');
  const loadTime = Date.now() - startTime;
  
  console.log(`Marketplace load time: ${loadTime}ms`);
  
  // Measure search performance
  const searchStart = Date.now();
  await page.fill('[data-testid="search-input"]', 'AI');
  await page.waitForSelector('[data-testid="search-results"]');
  const searchTime = Date.now() - searchStart;
  
  console.log(`Search time: ${searchTime}ms`);
  
  await browser.close();
}
```

## 🔒 Security Testing

### Security Test Checklist

- [ ] **Input Validation**
  - [ ] Search inputs are sanitized
  - [ ] API parameters are validated
  - [ ] File uploads are restricted
  - [ ] SQL injection prevention

- [ ] **Authentication & Authorization**
  - [ ] API endpoints require proper authentication
  - [ ] User permissions are enforced
  - [ ] Session management is secure
  - [ ] Rate limiting is implemented

- [ ] **Plugin Security**
  - [ ] Plugin validation works correctly
  - [ ] Sandboxing prevents malicious code
  - [ ] Permission system is enforced
  - [ ] Code signing verification works

### Security Test Scripts

```javascript
// security-test.js
describe('Marketplace Security', () => {
  test('prevents XSS in search', async () => {
    const maliciousInput = '<script>alert("xss")</script>';
    const response = await api.searchNodes({ search: maliciousInput });
    
    // Verify input is sanitized
    expect(response.data.nodes).toBeDefined();
    expect(response.data.nodes.length).toBe(0);
  });
  
  test('requires authentication for protected endpoints', async () => {
    const response = await fetch('/api/marketplace/developer/nodes', {
      method: 'POST',
      body: JSON.stringify({ name: 'test' })
    });
    
    expect(response.status).toBe(401);
  });
});
```

## 📱 Cross-Platform Testing

### Browser Compatibility

Test on the following browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile Safari (iOS)
- [ ] Chrome Mobile (Android)

### Device Testing

Test on the following device categories:
- [ ] Desktop (1920x1080, 1366x768)
- [ ] Tablet (768x1024, 1024x768)
- [ ] Mobile (375x667, 414x896)
- [ ] Large screens (2560x1440, 3840x2160)

### Responsive Design Testing

```javascript
// responsive-test.js
const viewports = [
  { width: 375, height: 667 },   // iPhone SE
  { width: 768, height: 1024 },  // iPad
  { width: 1366, height: 768 },  // Laptop
  { width: 1920, height: 1080 }  // Desktop
];

for (const viewport of viewports) {
  test(`Marketplace works at ${viewport.width}x${viewport.height}`, async () => {
    await page.setViewportSize(viewport);
    await page.goto('/marketplace');
    
    // Verify layout is not broken
    const isLayoutBroken = await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      return Array.from(elements).some(el => 
        el.scrollWidth > document.documentElement.clientWidth
      );
    });
    
    expect(isLayoutBroken).toBe(false);
  });
}
```

## 🐛 Bug Reporting

### Bug Report Template

```markdown
## Bug Report

**Environment:**
- Browser: Chrome 120.0.0
- OS: macOS 14.0
- Screen Resolution: 1920x1080
- Node Version: 18.17.0

**Steps to Reproduce:**
1. Navigate to /marketplace
2. Search for "AI"
3. Click on first result
4. Click "Install" button

**Expected Behavior:**
Node should install successfully

**Actual Behavior:**
Installation fails with error message

**Screenshots:**
[Attach screenshots]

**Console Errors:**
```
Error: Failed to fetch node package
    at installNode (marketplace.tsx:45)
```

**Additional Context:**
This happens only with premium nodes
```

### Critical Bug Criteria

Bugs that require immediate attention:
- Security vulnerabilities
- Data corruption issues
- Complete feature failures
- Performance degradation >50%
- Cross-browser compatibility issues

## 📈 Testing Metrics

### Success Criteria

- [ ] **Functionality**: 100% of core features work correctly
- [ ] **Performance**: All operations complete within target times
- [ ] **Reliability**: <0.1% error rate in normal operations
- [ ] **Security**: No security vulnerabilities found
- [ ] **Usability**: Users can complete tasks without confusion
- [ ] **Compatibility**: Works on all supported browsers/devices

### Testing Coverage Goals

- Unit test coverage: >90%
- Integration test coverage: >80%
- E2E test coverage: >70%
- Performance test coverage: 100% of critical paths
- Security test coverage: 100% of attack vectors

## 🚀 Continuous Testing

### CI/CD Integration

```yaml
# .github/workflows/marketplace-tests.yml
name: Marketplace Tests

on:
  push:
    paths:
      - 'lib/marketplace/**'
      - 'components/marketplace/**'
      - 'app/marketplace/**'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm test -- --testPathPattern=marketplace
      
      - name: Run integration tests
        run: npm run test:integration -- --grep="marketplace"
      
      - name: Run E2E tests
        run: npm run test:e2e -- --spec="marketplace/**"
      
      - name: Performance tests
        run: npm run test:performance -- marketplace
```

### Monitoring & Alerts

Set up monitoring for:
- API response times
- Error rates
- User engagement metrics
- Performance regressions
- Security incidents

---

**Remember**: Testing is an ongoing process. As new features are added, update this testing guide and ensure comprehensive coverage of all functionality.
