# Phase 2 Setup Guide: Payment Integration

## 🎯 **Overview**

This guide will help you set up Stripe payment integration for your Node Plugin Marketplace. After completing this setup, users will be able to purchase premium nodes and subscribe to plans.

## 📋 **Prerequisites**

- ✅ Phase 1 marketplace implementation complete
- ✅ Stripe account (create at [stripe.com](https://stripe.com))
- ✅ SSL certificate for production (required by Stripe)

## 🔧 **Step 1: Stripe Account Setup**

### **1.1 Create Stripe Account**
1. Go to [stripe.com](https://stripe.com) and create an account
2. Complete business verification (required for live payments)
3. Set up your business profile and bank account

### **1.2 Get API Keys**
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
2. Copy your **Publishable key** (starts with `pk_test_` or `pk_live_`)
3. Copy your **Secret key** (starts with `sk_test_` or `sk_live_`)
4. Keep these secure - never commit secret keys to version control

### **1.3 Create Webhook Endpoint**
1. Go to [Stripe Webhooks](https://dashboard.stripe.com/webhooks)
2. Click "Add endpoint"
3. Set URL to: `https://yourdomain.com/api/payments/webhooks`
4. Select these events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `checkout.session.completed`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
5. Copy the **Webhook signing secret** (starts with `whsec_`)

## 🛍️ **Step 2: Create Subscription Products**

### **2.1 Developer Plans**
Create these products in [Stripe Products](https://dashboard.stripe.com/products):

1. **Developer Basic** - $10/month
   - Name: "Developer Basic"
   - Description: "Perfect for individual developers"
   - Recurring: Monthly
   - Copy the Price ID (starts with `price_`)

2. **Developer Pro** - $25/month
   - Name: "Developer Pro"
   - Description: "For professional developers and small teams"
   - Recurring: Monthly
   - Copy the Price ID

3. **Enterprise Team** - $100/month
   - Name: "Enterprise Team"
   - Description: "For teams and organizations"
   - Recurring: Monthly
   - Copy the Price ID

### **2.2 User Plans**
1. **Premium User** - $15/month
   - Name: "Premium User"
   - Description: "Access to premium nodes and features"
   - Recurring: Monthly
   - Copy the Price ID

2. **Enterprise User** - $50/month
   - Name: "Enterprise User"
   - Description: "Full access for enterprise users"
   - Recurring: Monthly
   - Copy the Price ID

## ⚙️ **Step 3: Environment Configuration**

### **3.1 Update Environment Variables**
Copy `.env.example` to `.env.local` and add your Stripe keys:

```bash
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# Stripe Price IDs
STRIPE_DEVELOPER_BASIC_PRICE_ID="price_your_basic_price_id"
STRIPE_DEVELOPER_PRO_PRICE_ID="price_your_pro_price_id"
STRIPE_ENTERPRISE_TEAM_PRICE_ID="price_your_enterprise_price_id"
STRIPE_USER_PREMIUM_PRICE_ID="price_your_premium_price_id"
STRIPE_USER_ENTERPRISE_PRICE_ID="price_your_enterprise_price_id"
```

### **3.2 Verify Configuration**
Run this command to verify your Stripe setup:

```bash
npm run dev
```

Check the console for any Stripe configuration errors.

## 🧪 **Step 4: Testing**

### **4.1 Test Mode**
- Use test API keys (starting with `pk_test_` and `sk_test_`)
- Use test card numbers from [Stripe Testing](https://stripe.com/docs/testing)
- Common test cards:
  - Success: `****************`
  - Decline: `****************`
  - 3D Secure: `40000***********`

### **4.2 Test Payment Flow**
1. Navigate to marketplace
2. Find a premium node
3. Click "Purchase"
4. Use test card: `****************`
5. Use any future expiry date and CVC
6. Complete payment
7. Verify node appears in "My Library"

### **4.3 Test Subscription Flow**
1. Go to "Subscription" tab
2. Select a plan
3. Use test card for payment
4. Verify subscription is active
5. Test cancellation flow

## 🚀 **Step 5: Production Deployment**

### **5.1 Switch to Live Mode**
1. Complete Stripe account verification
2. Replace test keys with live keys (`pk_live_` and `sk_live_`)
3. Update webhook endpoint to production URL
4. Test with real payment methods

### **5.2 Security Checklist**
- ✅ SSL certificate installed
- ✅ Environment variables secured
- ✅ Webhook signature verification enabled
- ✅ API keys not exposed in client code
- ✅ Payment data not stored locally

### **5.3 Compliance**
- ✅ Privacy policy updated for payment data
- ✅ Terms of service include refund policy
- ✅ GDPR compliance for EU customers
- ✅ PCI compliance through Stripe

## 📊 **Step 6: Monitoring & Analytics**

### **6.1 Stripe Dashboard**
Monitor these metrics in your Stripe Dashboard:
- Payment success rate
- Failed payment reasons
- Subscription churn rate
- Revenue trends
- Customer lifetime value

### **6.2 Application Monitoring**
Track these events in your application:
- Payment intent creation
- Successful purchases
- Failed payments
- Subscription activations
- Cancellations

## 🔧 **Step 7: Advanced Features**

### **7.1 Proration**
Enable automatic proration for subscription upgrades:

```typescript
// In subscription update
const subscription = await stripe.subscriptions.update(subscriptionId, {
  items: [{ price: newPriceId }],
  proration_behavior: 'create_prorations',
});
```

### **7.2 Trial Periods**
Add trial periods to subscriptions:

```typescript
const subscription = await stripe.subscriptions.create({
  customer: customerId,
  items: [{ price: priceId }],
  trial_period_days: 14,
});
```

### **7.3 Coupons & Discounts**
Create promotional codes in Stripe Dashboard and apply them:

```typescript
const subscription = await stripe.subscriptions.create({
  customer: customerId,
  items: [{ price: priceId }],
  coupon: 'LAUNCH50', // 50% off coupon
});
```

## 🆘 **Troubleshooting**

### **Common Issues**

1. **"Stripe not configured" error**
   - Check environment variables are set
   - Verify API keys are correct
   - Restart development server

2. **Webhook signature verification failed**
   - Check webhook secret is correct
   - Verify endpoint URL is accessible
   - Check webhook events are selected

3. **Payment intent creation fails**
   - Verify node exists in database
   - Check user authentication
   - Validate payment amount

4. **Subscription creation fails**
   - Verify price IDs are correct
   - Check customer creation
   - Validate payment method

### **Debug Mode**
Enable Stripe debug logging:

```typescript
// In lib/stripe/config.ts
const stripe = new Stripe(secretKey, {
  apiVersion: '2023-10-16',
  typescript: true,
  telemetry: false, // Disable in production
});
```

## 📞 **Support**

### **Stripe Support**
- [Stripe Documentation](https://stripe.com/docs)
- [Stripe Support](https://support.stripe.com)
- [Stripe Community](https://github.com/stripe)

### **Implementation Support**
- Check the implementation documentation
- Review the testing guide
- Use the provided debugging tools

## ✅ **Success Checklist**

- [ ] Stripe account created and verified
- [ ] API keys configured in environment
- [ ] Webhook endpoint created and tested
- [ ] Subscription products created
- [ ] Price IDs configured
- [ ] Test payments working
- [ ] Test subscriptions working
- [ ] Production deployment ready
- [ ] Monitoring and analytics set up

## 🎉 **Congratulations!**

Your marketplace now has full payment processing capabilities! Users can:
- ✅ Purchase premium nodes with credit cards
- ✅ Subscribe to monthly plans
- ✅ Manage their subscriptions
- ✅ View payment history
- ✅ Receive email receipts

Your platform is now ready to generate revenue and compete with industry leaders!

---

**Next Steps**: Consider implementing Phase 3 features like private repositories, AI recommendations, and advanced analytics.
