# Dual Output Feature for Generate Text Node

## Overview

The Generate Text Node now supports **dual output handles** to provide both complete AI responses and extracted raw data separately. This allows for more flexible workflow design where different types of data can be routed to different processing nodes.

## Features

### 🔗 **Two Output Handles**

1. **"Full" Output Handle (Blue)**: 
   - Provides complete AI response with narration
   - Same as existing behavior
   - Includes explanatory text, context, and formatting

2. **"Data" Output Handle (Orange)**:
   - Provides extracted raw data only
   - Removes narration and explanatory text
   - Focuses on structured data content

### 🎯 **Smart Data Extraction**

The system automatically identifies and extracts:
- **Tables** (markdown format with `|` separators)
- **Lists** (numbered, bulleted, asterisk)
- **Code blocks** (content within ``` markers)
- **JSON objects** and arrays
- **Key-value pairs**
- **CSV-like data** (comma-separated values)

### 📋 **Example Use Case**

**Prompt**: "Create 5 sample data entries for a user table"

**AI Response**:
```
Here are 5 sample data entries for a user table:

1. <PERSON>, <EMAIL>, 25, <PERSON><PERSON><PERSON>
2. <PERSON>, <EMAIL>, 30, <PERSON>  
3. <PERSON>, <EMAIL>, 35, <PERSON>
4. <PERSON>, <EMAIL>, 28, Analyst
5. <PERSON>, <EMAIL>, 32, Engineer

These entries include name, email, age, and job title for each user.
```

**Output Comparison**:

- **Full Output**: Complete response with explanation
- **Data Output**: Only the 5 numbered entries without narration

## Visual Design

### Node Appearance
```
┌─────────────────────────────┐
│     Generate Text Node      │
│                             │
│ [Prompt Input Area]         │
│                             │
│ [Generated Response]        │
│                             │
│ ○ Settings                  │
└─────────────────────────────┘
                           Full ●
                           Data ●
```

### Handle Labels
- **Full**: Blue handle with "Full" label
- **Data**: Orange handle with "Data" label

## Technical Implementation

### Data Extraction Algorithm

```typescript
const extractRawData = (text: string): string => {
  // Pattern matching for structured data
  const patterns = [
    /```[\s\S]*?```/g,        // Code blocks
    /\|.*\|[\s\S]*?\|.*\|/g,  // Tables
    /^\d+\..*$/gm,            // Numbered lists
    /^-.*$/gm,                // Bullet lists
    /^\*.*$/gm,               // Asterisk lists
    /\{[\s\S]*?\}/g,          // JSON objects
    /\[[\s\S]*?\]/g,          // Arrays
  ];
  
  // Extract and filter content
  // Remove narration keywords
  // Return clean data
};
```

### Workflow Integration

```typescript
// Main output (existing behavior)
data.onChange(fullResponse);

// Raw data output (new feature)  
data.onRawDataChange(extractedData);
```

## Usage Instructions

### 1. **Add Generate Text Node**
   - Drag from sidebar or use node selector
   - Node appears with two output handles

### 2. **Connect Outputs**
   - **Full Handle**: Connect to nodes needing complete response
   - **Data Handle**: Connect to nodes needing only raw data

### 3. **Generate Content**
   - Enter prompt requesting structured data
   - Click generate button
   - Both outputs update automatically

### 4. **View Results**
   - Connected nodes receive appropriate data type
   - Full output includes narration
   - Data output contains only extracted content

## Best Practices

### ✅ **When to Use Data Output**
- **Data Processing**: Feeding into tables, charts, databases
- **Automation**: Structured data for further processing
- **Clean Input**: When narration would interfere with parsing
- **API Integration**: Raw data for system-to-system communication

### ✅ **When to Use Full Output**
- **Human Reading**: Complete responses with context
- **Documentation**: Explanatory content needed
- **Debugging**: Full AI response for troubleshooting
- **Mixed Content**: Both data and explanation required

### 📝 **Prompt Design Tips**

For better data extraction, use prompts like:
- "Create 5 data samples for..."
- "Generate a table with..."
- "List the following items..."
- "Provide JSON data for..."

## Compatibility

- **Backward Compatible**: Existing workflows continue to work
- **Progressive Enhancement**: New feature doesn't break old connections
- **Flexible Routing**: Can use one or both outputs as needed

## Future Enhancements

Potential improvements:
- **Custom Extraction Patterns**: User-defined regex patterns
- **Format Conversion**: Auto-convert between JSON, CSV, XML
- **Data Validation**: Verify extracted data structure
- **Preview Mode**: Show extraction preview before connecting

---

This dual output feature significantly enhances workflow flexibility by separating content presentation from data processing, enabling more sophisticated automation and data handling workflows.
