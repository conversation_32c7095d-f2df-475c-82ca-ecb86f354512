const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Default settings structure that should match the database
const defaultSettings = {
  payments: {
    enabled: false,
    provider: 'disabled',
    dokuEnabled: false,
    testMode: true,
    currency: 'idr',
    commissionRate: 0.25,
    minimumAmount: 10000,
    maximumAmount: 100000000,
    dokuClientId: '',
    dokuSecretKey: '',
    dokuEnvironment: 'sandbox',
    dokuNotificationUrl: '',
  },
  billing: {
    enabled: false,
    invoiceGeneration: false,
    automaticBilling: false,
    billingCycle: 'monthly',
    gracePeriodDays: 7,
    reminderDays: [7, 3, 1],
  },
  subscriptions: {
    enabled: false,
    allowFreePlan: true,
    allowUpgrades: false,
    allowDowngrades: false,
    prorationEnabled: false,
    trialPeriodDays: 14,
    cancelationPolicy: 'end_of_period',
  },
  marketplace: {
    enabled: true,
    paidNodesEnabled: false,
    freeNodesEnabled: true,
    nodeApprovalRequired: true,
    allowNodeUploads: true,
    maxNodeSize: 10,
    allowedFileTypes: ['.js', '.ts', '.json'],
    featuredNodesEnabled: true,
    reviewSystemEnabled: true,
    ratingSystemEnabled: true,
  },
  developer: {
    enabled: true,
    nodePublishingEnabled: true,
    analyticsEnabled: true,
    revenueShareEnabled: false,
    maxNodesPerDeveloper: 50,
    approvalProcessEnabled: true,
    sandboxTestingEnabled: true,
  },
  workflows: {
    // Basic Settings
    enabled: true,
    maxWorkflowsPerUser: 100,
    maxNodesPerWorkflow: 50,
    sharingEnabled: true,
    exportEnabled: true,
    importEnabled: true,

    // Execution Engine Settings
    executionTimeoutMinutes: 30,
    maxConcurrentExecutions: 5,
    defaultExecutionMode: 'optimized',
    retryAttempts: 3,
    continueOnError: false,
    debugModeEnabled: false,

    // Scheduling Settings
    schedulingEnabled: true,
    maxScheduledWorkflows: 20,
    schedulingIntervalMinutes: 1,

    // Performance Settings
    maxConcurrentNodes: 10,
    nodeExecutionTimeoutSeconds: 300,
    memoryLimitMB: 512,
    cpuLimitPercent: 80,

    // Storage & Logging
    executionHistoryRetentionDays: 30,
    logLevel: 'info',
    maxLogSizeMB: 100,
    enableExecutionMetrics: true,

    // Security Settings
    sandboxEnabled: true,
    allowExternalConnections: true,
    allowFileSystemAccess: false,
    allowNetworkAccess: true,

    // Advanced Features
    webhooksEnabled: true,
    apiIntegrationEnabled: true,
    customNodeUploadEnabled: true,
    workflowTemplatesEnabled: true,
  },
  email: {
    // Basic Configuration
    enabled: false,
    provider: 'disabled',
    fromEmail: '<EMAIL>',
    fromName: 'Workflow App',
    replyToEmail: '<EMAIL>',

    // Email Features
    verificationEnabled: false,
    notificationsEnabled: false,
    marketingEmailsEnabled: false,
    transactionalEmailsEnabled: true,
    emailTemplatesEnabled: true,

    // SMTP Settings
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpSecure: false,
    smtpUsername: '',
    smtpPassword: '',
    smtpTLS: true,

    // SendGrid Settings
    sendgridApiKey: '',
    sendgridTemplateId: '',
    sendgridWebhookEnabled: false,

    // Mailgun Settings
    mailgunApiKey: '',
    mailgunDomain: '',
    mailgunRegion: 'us',
    mailgunWebhookEnabled: false,

    // Amazon SES Settings
    sesAccessKeyId: '',
    sesSecretAccessKey: '',
    sesRegion: 'us-east-1',
    sesConfigurationSet: '',

    // Postmark Settings
    postmarkApiKey: '',
    postmarkTemplateId: '',
    postmarkWebhookEnabled: false,

    // Resend Settings
    resendApiKey: '',
    resendWebhookEnabled: false,

    // Email Delivery Settings
    maxRetryAttempts: 3,
    retryDelayMinutes: 5,
    bounceHandlingEnabled: true,
    unsubscribeHandlingEnabled: true,

    // Rate Limiting
    rateLimitEnabled: true,
    maxEmailsPerHour: 100,
    maxEmailsPerDay: 1000,

    // Email Tracking
    trackOpens: true,
    trackClicks: true,
    trackUnsubscribes: true,

    // Email Queue
    queueEnabled: true,
    queueMaxSize: 1000,
    queueProcessingInterval: 5,

    // Email Templates
    welcomeEmailEnabled: true,
    passwordResetEmailEnabled: true,
    emailVerificationEnabled: true,
    notificationEmailEnabled: true,

    // Email Security
    dkimEnabled: false,
    spfEnabled: false,
    dmarcEnabled: false,

    // Email Analytics
    analyticsEnabled: true,
    retentionDays: 90,

    // Testing & Development
    testModeEnabled: false,
    testEmailAddress: '<EMAIL>',
    logEmailsEnabled: true,
  },
  security: {
    twoFactorEnabled: false,
    passwordRequirements: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
    },
    sessionTimeoutMinutes: 480,
    maxLoginAttempts: 5,
    lockoutDurationMinutes: 15,
  },
  api: {
    rateLimitEnabled: true,
    requestsPerMinute: 100,
    requestsPerHour: 1000,
    apiKeysEnabled: false,
    webhooksEnabled: false,
    corsEnabled: true,
    allowedOrigins: ['http://localhost:3000'],
  },
  storage: {
    // Provider Configuration
    provider: 'local',
    enabled: true,

    // File Upload Settings
    maxFileSize: 50,
    maxTotalStorage: 10,
    allowedFileTypes: ['.js', '.ts', '.json', '.md', '.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.zip'],
    blockedFileTypes: ['.exe', '.bat', '.cmd', '.scr', '.com', '.pif'],
    allowExecutableFiles: false,

    // Storage Optimization
    compressionEnabled: true,
    compressionQuality: 85,
    autoOptimizeImages: true,
    generateThumbnails: true,
    thumbnailSizes: [150, 300, 600],

    // Backup & Retention
    backupEnabled: false,
    backupFrequency: 'daily',
    retentionDays: 90,
    autoCleanupEnabled: true,

    // Security
    virusScanEnabled: false,
    encryptionEnabled: false,
    accessLoggingEnabled: true,
    publicAccessEnabled: false,

    // AWS S3 Settings
    awsRegion: 'us-east-1',
    awsBucket: '',
    awsAccessKeyId: '',
    awsSecretAccessKey: '',
    awsCloudFrontEnabled: false,
    awsCloudFrontDomain: '',

    // Google Cloud Storage Settings
    gcpProjectId: '',
    gcpBucket: '',
    gcpKeyFile: '',
    gcpRegion: 'us-central1',

    // Azure Blob Storage Settings
    azureAccountName: '',
    azureAccountKey: '',
    azureContainer: '',
    azureRegion: 'East US',

    // DigitalOcean Spaces Settings
    doSpacesKey: '',
    doSpacesSecret: '',
    doSpacesEndpoint: '',
    doSpacesBucket: '',
    doSpacesRegion: 'nyc3',

    // Cloudinary Settings
    cloudinaryCloudName: '',
    cloudinaryApiKey: '',
    cloudinaryApiSecret: '',
    cloudinaryFolder: 'uploads',
  },

  database: {
    // Connection Settings
    provider: 'sqlite',
    host: 'localhost',
    port: 5432,
    database: 'nextjs_app',
    username: '',
    password: '',
    ssl: false,

    // Connection Pool Settings
    maxConnections: 10,
    minConnections: 2,
    connectionTimeout: 30,
    idleTimeout: 600,

    // Performance Settings
    queryTimeout: 30,
    slowQueryThreshold: 1000,
    enableQueryLogging: false,
    enableSlowQueryLogging: true,
    cacheEnabled: true,
    cacheTTL: 300,

    // Backup Settings
    backupEnabled: true,
    backupFrequency: 'daily',
    backupRetentionDays: 30,
    backupCompression: true,
    backupLocation: 'local',

    // Maintenance Settings
    autoVacuumEnabled: true,
    autoAnalyzeEnabled: true,
    maintenanceWindow: '02:00-04:00',
    indexOptimizationEnabled: true,

    // Security Settings
    encryptionAtRest: false,
    encryptionInTransit: true,
    auditLoggingEnabled: false,
    accessLoggingEnabled: true,

    // Monitoring Settings
    performanceMonitoringEnabled: true,
    alertsEnabled: false,
    diskSpaceThreshold: 85,
    connectionThreshold: 80,

    // Migration Settings
    autoMigrationsEnabled: false,
    migrationTimeout: 300,
    rollbackEnabled: true,

    // Replication Settings
    replicationEnabled: false,
    readReplicaEnabled: false,
    replicationLag: 5,
  },
  analytics: {
    enabled: true,
    trackingEnabled: true,
    dataRetentionDays: 365,
    anonymizeData: true,
    exportEnabled: true,
    realtimeEnabled: true,
  },
  maintenance: {
    maintenanceMode: false,
    maintenanceMessage: 'System is under maintenance. Please try again later.',
    allowedIPs: [],
    scheduledMaintenance: null,
  },
  features: {
    betaFeaturesEnabled: false,
    experimentalFeaturesEnabled: false,
    debugModeEnabled: false,
    performanceMonitoringEnabled: true,
  },
  ui: {
    // Theme & Appearance
    darkModeEnabled: true,
    themeMode: 'system',
    colorScheme: 'default',
    accentColor: '#3b82f6',
    borderRadius: 'medium',
    fontFamily: 'system',
    fontSize: 'medium',

    // Layout & Navigation
    compactModeEnabled: false,
    sidebarCollapsed: false,
    sidebarPosition: 'left',
    navigationStyle: 'sidebar',
    breadcrumbsEnabled: true,
    pageTransitions: true,

    // Animations & Effects
    animationsEnabled: true,
    animationSpeed: 'normal',
    reducedMotion: false,
    hoverEffects: true,
    loadingAnimations: true,

    // Notifications & Feedback
    notificationsEnabled: true,
    notificationPosition: 'top-right',
    notificationDuration: 5,
    soundEnabled: false,
    hapticFeedback: false,

    // Content & Display
    density: 'comfortable',
    showTooltips: true,
    showKeyboardShortcuts: true,
    autoSaveEnabled: true,
    confirmDialogs: true,

    // Accessibility
    highContrastMode: false,
    focusIndicators: true,
    screenReaderOptimized: false,
    keyboardNavigationEnabled: true,

    // Localization
    language: 'en',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '24h',
    timezone: 'UTC',
    currency: 'USD',
    numberFormat: 'US',

    // Performance & Data
    lazyLoadingEnabled: true,
    imageOptimization: true,
    cacheEnabled: true,
    offlineMode: false,

    // Developer & Debug
    debugMode: false,
    showPerformanceMetrics: false,
    enableDevTools: false,

    // Customization
    customCSS: '',
    logoUrl: '',
    faviconUrl: '',
    brandColors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#8b5cf6',
    },
  },
};

function getValueType(value) {
  if (typeof value === 'boolean') return 'boolean';
  if (typeof value === 'number') return 'number';
  if (Array.isArray(value)) return 'array';
  if (typeof value === 'object') return 'object';
  return 'string';
}

function stringifyValue(value) {
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  return String(value);
}

async function fixSettings() {
  console.log('🔧 Fixing application settings...');

  try {
    // Get all existing settings
    const existingSettings = await prisma.appSettings.findMany();
    const existingMap = new Map();

    existingSettings.forEach(setting => {
      const key = `${setting.category}.${setting.key}`;
      existingMap.set(key, setting);
    });

    let created = 0;
    let updated = 0;

    // Process each category
    for (const [category, categorySettings] of Object.entries(defaultSettings)) {
      console.log(`📂 Processing ${category} settings...`);

      for (const [key, value] of Object.entries(categorySettings)) {
        const settingKey = `${category}.${key}`;
        const existing = existingMap.get(settingKey);

        const settingData = {
          category,
          key,
          value: stringifyValue(value),
          type: getValueType(value),
          description: `${category} ${key} setting`,
          isSystem: true,
        };

        if (existing) {
          // Update if type or value is different
          if (existing.type !== settingData.type) {
            await prisma.appSettings.update({
              where: { id: existing.id },
              data: {
                type: settingData.type,
                value: settingData.value,
                updatedAt: new Date(),
              },
            });
            updated++;
            console.log(`  ✅ Updated ${settingKey} (type: ${existing.type} → ${settingData.type})`);
          }
        } else {
          // Create new setting
          await prisma.appSettings.create({
            data: {
              id: `${category}_${key}`,
              ...settingData,
            },
          });
          created++;
          console.log(`  ➕ Created ${settingKey}`);
        }
      }
    }

    console.log(`\n✅ Settings fix completed!`);
    console.log(`   📊 Created: ${created} settings`);
    console.log(`   🔄 Updated: ${updated} settings`);

  } catch (error) {
    console.error('❌ Error fixing settings:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixSettings().catch(console.error);
