#!/usr/bin/env tsx

/**
 * Complete marketplace demo script
 * Seeds sample data and runs comprehensive tests
 */

import { seedSampleNodes } from './seed-sample-nodes';
import { MarketplaceSystemTester } from './test-marketplace-system';

async function runMarketplaceDemo() {
  console.log('🚀 Starting Marketplace System Demo');
  console.log('====================================\n');

  try {
    // Step 1: Seed sample nodes
    console.log('Step 1: Seeding sample marketplace nodes...');
    await seedSampleNodes();
    console.log('✅ Sample nodes seeded successfully!\n');

    // Step 2: Run comprehensive tests
    console.log('Step 2: Running comprehensive system tests...');
    const tester = new MarketplaceSystemTester();
    await tester.runAllTests();

    console.log('\n🎉 Marketplace demo completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Visit http://localhost:3000/marketplace to see the nodes');
    console.log('2. Try installing the free nodes');
    console.log('3. Create a workflow and test execution');
    console.log('4. Visit http://localhost:3000/developer to upload your own nodes');

  } catch (error) {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  }
}

// Run the demo
runMarketplaceDemo();
