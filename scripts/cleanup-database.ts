#!/usr/bin/env tsx

/**
 * Database cleanup script
 * Run with: npx tsx scripts/cleanup-database.ts
 */

import { PrismaClient } from '@prisma/client';
import { DatabaseCleanup } from '../lib/performance/query-optimization';

const prisma = new PrismaClient();

async function main() {
  console.log('🧹 Starting database cleanup...\n');

  try {
    // Get current database stats
    console.log('📊 Current database statistics:');
    const stats = await DatabaseCleanup.getDatabaseStats();
    console.log(`- Users: ${stats.userCount}`);
    console.log(`- Logs: ${stats.logCount}`);
    console.log(`- Login History: ${stats.loginHistoryCount}`);
    console.log(`- Roles: ${stats.roleCount}`);
    console.log(`- Workflows: ${stats.workflowCount}\n`);

    // Clean old logs (older than 90 days)
    console.log('🗑️  Cleaning old logs (older than 90 days)...');
    const cleanedLogs = await DatabaseCleanup.cleanOldLogs(90);
    console.log(`✅ Cleaned ${cleanedLogs} old log entries\n`);

    // Clean old login history (older than 180 days)
    console.log('🗑️  Cleaning old login history (older than 180 days)...');
    const cleanedLoginHistory = await DatabaseCleanup.cleanOldLoginHistory(180);
    console.log(`✅ Cleaned ${cleanedLoginHistory} old login history entries\n`);

    // Get updated stats
    console.log('📊 Updated database statistics:');
    const updatedStats = await DatabaseCleanup.getDatabaseStats();
    console.log(`- Users: ${updatedStats.userCount}`);
    console.log(`- Logs: ${updatedStats.logCount} (reduced by ${stats.logCount - updatedStats.logCount})`);
    console.log(`- Login History: ${updatedStats.loginHistoryCount} (reduced by ${stats.loginHistoryCount - updatedStats.loginHistoryCount})`);
    console.log(`- Roles: ${updatedStats.roleCount}`);
    console.log(`- Workflows: ${updatedStats.workflowCount}\n`);

    console.log('✨ Database cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during database cleanup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Cleanup script failed:', error);
    process.exit(1);
  });
}

export { main as cleanupDatabase };
