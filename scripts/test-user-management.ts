// Test script for User Management System

import { seedUserManagementSystem, checkUserManagementHealth } from '../lib/user-management/seed';

async function testUserManagementSystem() {
  console.log('🧪 Testing User Management System...\n');

  try {
    // Check initial health
    console.log('1. Checking system health...');
    const initialHealth = await checkUserManagementHealth();
    console.log('Initial health:', initialHealth);

    // Seed the system
    console.log('\n2. Seeding system...');
    const seedResult = await seedUserManagementSystem();
    console.log('Seed result:', seedResult);

    // Check health after seeding
    console.log('\n3. Checking health after seeding...');
    const finalHealth = await checkUserManagementHealth();
    console.log('Final health:', finalHealth);

    console.log('\n✅ User Management System test completed successfully!');
    
    if (finalHealth.healthy && finalHealth.stats) {
      console.log('\n📊 System Statistics:');
      console.log(`- Permissions: ${finalHealth.stats.permissions}`);
      console.log(`- Roles: ${finalHealth.stats.roles}`);
      console.log(`- Users: ${finalHealth.stats.users}`);
      console.log(`- User Roles: ${finalHealth.stats.userRoles}`);
      console.log(`- Role Permissions: ${finalHealth.stats.rolePermissions}`);
      console.log(`- Has Super Admin: ${finalHealth.stats.hasSuperAdmin ? 'Yes' : 'No'}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testUserManagementSystem();
