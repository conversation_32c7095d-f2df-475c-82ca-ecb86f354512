import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugMarketplace() {
  console.log('🔍 Debugging marketplace installation issue...\n');

  try {
    // Check nodes
    const nodes = await prisma.nodePlugin.findMany({
      include: {
        nodeCodes: true,
        author: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`📦 Found ${nodes.length} nodes:`);
    for (const node of nodes) {
      console.log(`  - ${node.name} (${node.tier})`);
      console.log(`    ID: ${node.id}`);
      console.log(`    Author: ${node.author.name}`);
      console.log(`    Code versions: ${node.nodeCodes.length}`);
      if (node.nodeCodes.length > 0) {
        node.nodeCodes.forEach(code => {
          console.log(`      - v${code.version} (${code.code.length} chars)`);
        });
      }
      console.log('');
    }

    // Check if we have any free nodes with code
    const freeNodesWithCode = nodes.filter(node => 
      node.tier === 'free' && node.nodeCodes.length > 0
    );

    console.log(`✅ Free nodes with code: ${freeNodesWithCode.length}`);

    if (freeNodesWithCode.length === 0) {
      console.log('❌ No free nodes with code found. Creating sample node with code...');
      
      // Create a simple free node with code
      const sampleNodeCode = `
// Sample Text Processor Node
const nodeDefinition = {
  id: 'sample-text-processor',
  name: 'Sample Text Processor',
  description: 'A simple text processing node for testing',
  category: 'text-processing',
  inputs: [
    {
      id: 'text',
      name: 'Input Text',
      type: 'string',
      required: true
    }
  ],
  outputs: [
    {
      id: 'result',
      name: 'Processed Text',
      type: 'string'
    }
  ],
  execute: async (inputs) => {
    const text = inputs.text || '';
    return {
      result: text.toUpperCase()
    };
  }
};

// Export for Web Worker
if (typeof self !== 'undefined') {
  self.postMessage({ type: 'definition', data: nodeDefinition });
}
`;

      // Find a free node to add code to
      const freeNode = nodes.find(node => node.tier === 'free');
      if (freeNode) {
        const checksum = require('crypto').createHash('sha256').update(sampleNodeCode).digest('hex');
        
        await prisma.nodeCode.create({
          data: {
            nodeId: freeNode.id,
            version: '1.0.0',
            code: sampleNodeCode,
            dependencies: JSON.stringify([]),
            permissions: JSON.stringify([]),
            checksum: checksum
          }
        });

        console.log(`✅ Added code to node: ${freeNode.name}`);
      }
    }

    // Check users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true
      }
    });

    console.log(`👥 Found ${users.length} users:`);
    users.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - Verified: ${user.emailVerified}`);
    });

    // Check installations
    const installations = await prisma.installedNode.findMany({
      include: {
        node: {
          select: {
            name: true
          }
        },
        user: {
          select: {
            name: true
          }
        }
      }
    });

    console.log(`🔧 Found ${installations.length} installations:`);
    installations.forEach(install => {
      console.log(`  - ${install.node.name} by ${install.user.name} (${install.status})`);
    });

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugMarketplace();
