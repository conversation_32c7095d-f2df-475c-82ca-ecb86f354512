import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugInstalledNodes() {
  console.log('🔍 Debugging installed nodes visibility...\n');

  try {
    // 1. Check all installed nodes in database
    console.log('1️⃣ Checking database installed nodes...');
    const allInstallations = await prisma.installedNode.findMany({
      include: {
        node: {
          select: {
            id: true,
            name: true,
            version: true,
            category: true,
            tier: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`   Found ${allInstallations.length} total installations:`);
    allInstallations.forEach(install => {
      console.log(`   - ${install.node.name} (${install.status}) - User: ${install.user.name} - Enabled: ${install.enabled}`);
    });

    // 2. Check node codes
    console.log('\n2️⃣ Checking node codes...');
    const nodeCodes = await prisma.nodeCode.findMany({
      include: {
        node: {
          select: {
            name: true,
            version: true
          }
        }
      }
    });

    console.log(`   Found ${nodeCodes.length} node codes:`);
    nodeCodes.forEach(code => {
      console.log(`   - ${code.node.name} v${code.version} (${code.code.length} chars)`);
    });

    // 3. Check specific test user installations
    console.log('\n3️⃣ Checking test user installations...');
    const testUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      }
    });

    if (testUser) {
      console.log(`   Test user found: ${testUser.name} (${testUser.email})`);
      
      const userInstallations = await prisma.installedNode.findMany({
        where: { userId: testUser.id },
        include: {
          node: {
            include: {
              nodeCodes: true
            }
          }
        }
      });

      console.log(`   User has ${userInstallations.length} installations:`);
      userInstallations.forEach(install => {
        console.log(`   - ${install.node.name}:`);
        console.log(`     Status: ${install.status}`);
        console.log(`     Enabled: ${install.enabled}`);
        console.log(`     Version: ${install.version}`);
        console.log(`     Node codes: ${install.node.nodeCodes.length}`);
        console.log(`     Install path: ${install.installPath}`);
      });

      // 4. Test API endpoint simulation
      console.log('\n4️⃣ Simulating API endpoint response...');
      const enabledInstallations = userInstallations.filter(install => 
        install.status === 'installed' && install.enabled
      );

      console.log(`   Enabled installations: ${enabledInstallations.length}`);
      enabledInstallations.forEach(install => {
        console.log(`   - ${install.node.name} should appear in canvas`);
      });

      // 5. Check what the API would return
      console.log('\n5️⃣ Checking API response format...');
      const apiResponse = {
        installedNodes: userInstallations.map(installation => ({
          id: installation.id,
          nodeId: installation.nodeId,
          version: installation.version,
          status: installation.status,
          enabled: installation.enabled,
          config: installation.config,
          installedAt: installation.installedAt.toISOString(),
          updatedAt: installation.updatedAt.toISOString(),
          lastUsed: installation.lastUsed?.toISOString(),
          node: {
            id: installation.node.id,
            name: installation.node.name,
            description: installation.node.description,
            icon: installation.node.icon,
            category: installation.node.category,
            tier: installation.node.tier,
            version: installation.node.version,
            tags: installation.node.tags,
            author: {
              id: installation.node.authorId,
              name: 'Test Author',
              email: '<EMAIL>'
            },
            verified: installation.node.verified,
            rating: installation.node.rating,
            downloads: installation.node.downloads
          }
        }))
      };

      console.log('   API Response structure:');
      console.log(JSON.stringify(apiResponse, null, 2));

    } else {
      console.log('   No test user found');
    }

    // 6. Check marketplace nodes
    console.log('\n6️⃣ Checking marketplace nodes...');
    const marketplaceNodes = await prisma.nodePlugin.findMany({
      include: {
        nodeCodes: true,
        author: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`   Found ${marketplaceNodes.length} marketplace nodes:`);
    marketplaceNodes.forEach(node => {
      console.log(`   - ${node.name} (${node.tier}) - Codes: ${node.nodeCodes.length}`);
    });

    console.log('\n✅ Debug complete!');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugInstalledNodes();
