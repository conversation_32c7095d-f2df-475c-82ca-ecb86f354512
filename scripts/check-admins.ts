import { prisma } from '../lib/prisma';

async function checkAdmins() {
  try {
    console.log('🔍 Checking admin users...\n');
    
    const admins = await prisma.user.findMany({
      where: { 
        OR: [
          { isAdmin: true }, 
          { isSuperAdmin: true }
        ] 
      },
      select: { 
        id: true, 
        email: true, 
        name: true, 
        username: true,
        isAdmin: true, 
        isSuperAdmin: true,
        status: true,
        createdAt: true
      }
    });
    
    console.log(`Found ${admins.length} admin users:`);
    
    admins.forEach((admin, index) => {
      console.log(`\n${index + 1}. ${admin.name || admin.username}`);
      console.log(`   Email: ${admin.email}`);
      console.log(`   Status: ${admin.status}`);
      console.log(`   Admin: ${admin.isAdmin}`);
      console.log(`   Super Admin: ${admin.isSuperAdmin}`);
      console.log(`   Created: ${admin.createdAt.toLocaleDateString()}`);
    });
    
    if (admins.length === 0) {
      console.log('\n❌ No admin users found!');
      console.log('💡 Run the seeding script to create default admin user.');
    } else {
      console.log('\n✅ Admin users are available!');
      console.log('\n📝 Login credentials for super admin:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: admin123!');
    }
    
  } catch (error) {
    console.error('❌ Error checking admin users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdmins();
