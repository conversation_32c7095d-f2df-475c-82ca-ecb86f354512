import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createDemoUser() {
  try {
    // Check if demo user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('Demo user already exists');
      return;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('Demo123!', 12);

    // Create demo user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Demo User',
        username: 'demo_user',
        emailVerified: true, // Mark as verified for testing
        bio: 'This is a demo user account for testing purposes.',
      }
    });

    console.log('Demo user created successfully:', {
      id: user.id,
      email: user.email,
      name: user.name,
      username: user.username
    });

    console.log('\nLogin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Demo123!');

  } catch (error) {
    console.error('Error creating demo user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDemoUser();
