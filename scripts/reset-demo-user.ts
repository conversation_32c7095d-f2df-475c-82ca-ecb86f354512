import { prisma } from '../lib/prisma';
import bcrypt from 'bcryptjs';

async function resetDemoUser() {
  try {
    console.log('Deleting existing demo user...');

    // Delete the existing demo user if it exists
    await prisma.user.deleteMany({
      where: {
        email: '<EMAIL>',
      },
    });

    console.log('Demo user deleted successfully');

    // Create a new demo user
    console.log('Creating new demo user...');
    const hashedPassword = await bcrypt.hash('password123', 12);

    const newUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Demo User',
        username: 'demo_user',
        emailVerified: true, // Mark as verified for testing
        bio: 'This is a demo user account for testing purposes.',
      }
    });

    console.log('Demo user created successfully:', {
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      name: newUser.name,
      emailVerified: newUser.emailVerified,
    });

    console.log('Demo user reset completed successfully');
  } catch (error) {
    console.error('Error resetting demo user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
resetDemoUser();
