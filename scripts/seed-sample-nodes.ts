/**
 * <PERSON><PERSON>t to seed sample marketplace nodes for testing
 */

import { PrismaClient } from '@prisma/client';
import { NodeCategory, NodeTier } from '@/lib/marketplace/types';

const prisma = new PrismaClient();

const sampleNodes = [
  {
    id: 'text-processor-pro',
    name: 'Text Processor Pro',
    version: '1.2.0',
    description: 'Advanced text processing with regex, formatting, and validation',
    longDescription: 'A powerful text processing node that supports regular expressions, text formatting, validation, and transformation. Perfect for data cleaning and text manipulation workflows.',
    category: NodeCategory.TEXT_PROCESSING,
    tier: NodeTier.FREE,
    price: 0,
    tags: ['text', 'regex', 'formatting', 'validation'],
    dependencies: [],
    permissions: ['text-processing'],
    icon: '/api/placeholder/64/64',
    screenshots: ['/api/placeholder/400/300', '/api/placeholder/400/300'],
    verified: true,
    featured: true,
    rating: 4.8,
    reviewCount: 156,
    downloads: 2847,
    weeklyDownloads: 89,
    code: `
function executeNode(env) {
  const { inputs, nodeData } = env;
  const { text, operation, pattern, replacement } = inputs;

  let result = text || '';

  switch (operation) {
    case 'uppercase':
      result = result.toUpperCase();
      break;
    case 'lowercase':
      result = result.toLowerCase();
      break;
    case 'trim':
      result = result.trim();
      break;
    case 'regex_replace':
      if (pattern && replacement !== undefined) {
        const regex = new RegExp(pattern, 'g');
        result = result.replace(regex, replacement);
      }
      break;
    case 'word_count':
      result = result.split(/\\s+/).filter(word => word.length > 0).length;
      break;
    default:
      result = text;
  }

  return {
    processedText: result,
    originalLength: (text || '').length,
    processedLength: result.toString().length,
    timestamp: new Date().toISOString()
  };
}
    `,
    inputs: [
      { id: 'text', name: 'Input Text', type: 'string', required: true },
      { id: 'operation', name: 'Operation', type: 'select', options: ['uppercase', 'lowercase', 'trim', 'regex_replace', 'word_count'], required: true },
      { id: 'pattern', name: 'Regex Pattern', type: 'string', required: false },
      { id: 'replacement', name: 'Replacement', type: 'string', required: false }
    ],
    outputs: [
      { id: 'processedText', name: 'Processed Text', type: 'string' },
      { id: 'originalLength', name: 'Original Length', type: 'number' },
      { id: 'processedLength', name: 'Processed Length', type: 'number' },
      { id: 'timestamp', name: 'Timestamp', type: 'string' }
    ]
  },
  {
    id: 'data-validator',
    name: 'Data Validator',
    version: '2.1.0',
    description: 'Validate data formats, types, and constraints',
    longDescription: 'Comprehensive data validation node supporting email, URL, phone number, date, and custom validation rules. Essential for data quality workflows.',
    category: NodeCategory.DATA_PROCESSING,
    tier: NodeTier.FREE,
    price: 0,
    tags: ['validation', 'data-quality', 'email', 'url'],
    dependencies: [],
    permissions: ['data-validation'],
    icon: '/api/placeholder/64/64',
    screenshots: ['/api/placeholder/400/300'],
    verified: true,
    featured: true,
    rating: 4.6,
    reviewCount: 89,
    downloads: 1456,
    weeklyDownloads: 45,
    code: `
function executeNode(env) {
  const { inputs } = env;
  const { data, validationType, customPattern } = inputs;

  let isValid = false;
  let errorMessage = '';

  try {
    switch (validationType) {
      case 'email':
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        isValid = emailRegex.test(data);
        if (!isValid) errorMessage = 'Invalid email format';
        break;
      case 'url':
        try {
          new URL(data);
          isValid = true;
        } catch {
          isValid = false;
          errorMessage = 'Invalid URL format';
        }
        break;
      case 'phone':
        const phoneRegex = /^[\\+]?[1-9]?[0-9]{7,15}$/;
        isValid = phoneRegex.test(data.replace(/[\\s\\-\\(\\)]/g, ''));
        if (!isValid) errorMessage = 'Invalid phone number format';
        break;
      case 'custom':
        if (customPattern) {
          const regex = new RegExp(customPattern);
          isValid = regex.test(data);
          if (!isValid) errorMessage = 'Data does not match custom pattern';
        }
        break;
      default:
        isValid = data && data.length > 0;
        if (!isValid) errorMessage = 'Data is empty or null';
    }
  } catch (error) {
    isValid = false;
    errorMessage = error.message;
  }

  return {
    isValid,
    errorMessage,
    validatedData: isValid ? data : null,
    validationType,
    timestamp: new Date().toISOString()
  };
}
    `,
    inputs: [
      { id: 'data', name: 'Data to Validate', type: 'string', required: true },
      { id: 'validationType', name: 'Validation Type', type: 'select', options: ['email', 'url', 'phone', 'custom'], required: true },
      { id: 'customPattern', name: 'Custom Regex Pattern', type: 'string', required: false }
    ],
    outputs: [
      { id: 'isValid', name: 'Is Valid', type: 'boolean' },
      { id: 'errorMessage', name: 'Error Message', type: 'string' },
      { id: 'validatedData', name: 'Validated Data', type: 'string' },
      { id: 'validationType', name: 'Validation Type', type: 'string' },
      { id: 'timestamp', name: 'Timestamp', type: 'string' }
    ]
  },
  {
    id: 'json-transformer',
    name: 'JSON Transformer',
    version: '1.0.5',
    description: 'Transform and manipulate JSON data structures',
    longDescription: 'Advanced JSON processing node for parsing, transforming, filtering, and manipulating JSON data. Supports JSONPath queries and custom transformations.',
    category: NodeCategory.DATA_PROCESSING,
    tier: NodeTier.PREMIUM,
    price: 4.99,
    tags: ['json', 'transform', 'parse', 'jsonpath'],
    dependencies: [],
    permissions: ['json-processing'],
    icon: '/api/placeholder/64/64',
    screenshots: ['/api/placeholder/400/300', '/api/placeholder/400/300'],
    verified: true,
    featured: false,
    rating: 4.9,
    reviewCount: 67,
    downloads: 892,
    weeklyDownloads: 23,
    code: `
function executeNode(env) {
  const { inputs } = env;
  const { jsonData, operation, path, newValue } = inputs;

  let result;
  let parsedData;

  try {
    parsedData = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
  } catch (error) {
    return {
      error: 'Invalid JSON input',
      success: false
    };
  }

  try {
    switch (operation) {
      case 'extract':
        result = path ? getValueByPath(parsedData, path) : parsedData;
        break;
      case 'update':
        result = setValueByPath(parsedData, path, newValue);
        break;
      case 'delete':
        result = deleteValueByPath(parsedData, path);
        break;
      case 'keys':
        result = Object.keys(parsedData);
        break;
      case 'values':
        result = Object.values(parsedData);
        break;
      default:
        result = parsedData;
    }

    return {
      result: result,
      success: true,
      operation: operation,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      error: error.message,
      success: false,
      operation: operation
    };
  }
}

function getValueByPath(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

function setValueByPath(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => current[key] = current[key] || {}, obj);
  target[lastKey] = value;
  return obj;
}

function deleteValueByPath(obj, path) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => current && current[key], obj);
  if (target) delete target[lastKey];
  return obj;
}
    `,
    inputs: [
      { id: 'jsonData', name: 'JSON Data', type: 'string', required: true },
      { id: 'operation', name: 'Operation', type: 'select', options: ['extract', 'update', 'delete', 'keys', 'values'], required: true },
      { id: 'path', name: 'JSON Path', type: 'string', required: false },
      { id: 'newValue', name: 'New Value', type: 'string', required: false }
    ],
    outputs: [
      { id: 'result', name: 'Result', type: 'any' },
      { id: 'success', name: 'Success', type: 'boolean' },
      { id: 'error', name: 'Error Message', type: 'string' },
      { id: 'operation', name: 'Operation', type: 'string' },
      { id: 'timestamp', name: 'Timestamp', type: 'string' }
    ]
  },
  {
    id: 'http-request',
    name: 'HTTP Request',
    version: '3.0.1',
    description: 'Make HTTP requests with full customization',
    longDescription: 'Professional HTTP client node supporting all HTTP methods, custom headers, authentication, and response processing. Perfect for API integrations.',
    category: NodeCategory.API_INTEGRATION,
    tier: NodeTier.FREE,
    price: 0,
    tags: ['http', 'api', 'request', 'rest'],
    dependencies: [],
    permissions: ['network-access'],
    icon: '/api/placeholder/64/64',
    screenshots: ['/api/placeholder/400/300'],
    verified: true,
    featured: true,
    rating: 4.7,
    reviewCount: 234,
    downloads: 5678,
    weeklyDownloads: 156,
    code: `
async function executeNode(env) {
  const { inputs } = env;
  const { url, method, headers, body, timeout } = inputs;

  try {
    const requestOptions = {
      method: method || 'GET',
      headers: headers ? JSON.parse(headers) : {},
    };

    if (body && ['POST', 'PUT', 'PATCH'].includes(method)) {
      requestOptions.body = body;
      if (!requestOptions.headers['Content-Type']) {
        requestOptions.headers['Content-Type'] = 'application/json';
      }
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout || 30000);

    const response = await fetch(url, {
      ...requestOptions,
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    const responseText = await response.text();
    let responseData;

    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }

    return {
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      headers: Object.fromEntries(response.headers.entries()),
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: 0,
      timestamp: new Date().toISOString()
    };
  }
}
    `,
    inputs: [
      { id: 'url', name: 'URL', type: 'string', required: true },
      { id: 'method', name: 'HTTP Method', type: 'select', options: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'], required: true },
      { id: 'headers', name: 'Headers (JSON)', type: 'string', required: false },
      { id: 'body', name: 'Request Body', type: 'string', required: false },
      { id: 'timeout', name: 'Timeout (ms)', type: 'number', required: false }
    ],
    outputs: [
      { id: 'success', name: 'Success', type: 'boolean' },
      { id: 'status', name: 'Status Code', type: 'number' },
      { id: 'statusText', name: 'Status Text', type: 'string' },
      { id: 'data', name: 'Response Data', type: 'any' },
      { id: 'headers', name: 'Response Headers', type: 'object' },
      { id: 'error', name: 'Error Message', type: 'string' },
      { id: 'timestamp', name: 'Timestamp', type: 'string' }
    ]
  }
];

async function seedSampleNodes() {
  console.log('🌱 Seeding sample marketplace nodes...');

  try {
    // Create a sample author user if it doesn't exist
    let author = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!author) {
      author = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'sample-developer',
          name: 'Sample Developer',
          bio: 'Sample developer for testing marketplace nodes',
          avatar: '/api/placeholder/64/64',
          emailVerified: true
        }
      });
      console.log('✅ Created sample author user');
    }

    // Create sample nodes
    for (const nodeData of sampleNodes) {
      const existingNode = await prisma.nodePlugin.findUnique({
        where: { id: nodeData.id }
      });

      if (existingNode) {
        console.log(`⏭️  Node ${nodeData.name} already exists, skipping...`);
        continue;
      }

      // Create the node
      const node = await prisma.nodePlugin.create({
        data: {
          id: nodeData.id,
          name: nodeData.name,
          version: nodeData.version,
          description: nodeData.description,
          longDescription: nodeData.longDescription,
          authorId: author.id,
          category: nodeData.category,
          tier: nodeData.tier,
          price: nodeData.price,
          tags: JSON.stringify(nodeData.tags),
          dependencies: JSON.stringify(nodeData.dependencies),
          permissions: JSON.stringify(nodeData.permissions),
          icon: nodeData.icon,
          screenshots: JSON.stringify(nodeData.screenshots),
          downloadUrl: `/api/nodes/${nodeData.id}/download`,
          verified: nodeData.verified,
          featured: nodeData.featured,
          rating: nodeData.rating,
          reviewCount: nodeData.reviewCount,
          downloads: nodeData.downloads,
          weeklyDownloads: nodeData.weeklyDownloads,
          compatibility: JSON.stringify({
            minVersion: "1.0.0",
            maxVersion: null
          }),
          changelog: JSON.stringify([{
            version: nodeData.version,
            date: new Date(),
            changes: ["Initial release"]
          }])
        }
      });

      // Create the node code
      await prisma.nodeCode.create({
        data: {
          nodeId: nodeData.id,
          version: nodeData.version,
          code: nodeData.code,
          dependencies: nodeData.dependencies,
          permissions: nodeData.permissions,
          checksum: Buffer.from(nodeData.code).toString('base64').slice(0, 32) // Simple checksum
        }
      });

      console.log(`✅ Created node: ${nodeData.name} (${nodeData.id})`);
    }

    console.log('🎉 Sample nodes seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding sample nodes:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedSampleNodes().catch(console.error);
}

export { seedSampleNodes, sampleNodes };
