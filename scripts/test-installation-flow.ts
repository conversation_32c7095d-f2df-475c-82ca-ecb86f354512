import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testInstallationFlow() {
  console.log('🧪 Testing marketplace installation flow...\n');

  try {
    // 1. Check available nodes in marketplace
    console.log('1️⃣ Checking marketplace nodes...');
    const marketplaceNodes = await prisma.nodePlugin.findMany({
      include: {
        nodeCodes: true,
        author: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`   Found ${marketplaceNodes.length} nodes in marketplace:`);
    marketplaceNodes.forEach(node => {
      console.log(`   - ${node.name} (${node.tier}) - Code versions: ${node.nodeCodes.length}`);
    });

    // 2. Check test user
    console.log('\n2️⃣ Checking test user...');
    const testUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    });

    if (!testUser) {
      console.log('   Creating test user...');
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          username: 'testuser',
          emailVerified: true
        }
      });
      console.log(`   ✅ Created test user: ${newUser.name}`);
    } else {
      console.log(`   ✅ Test user exists: ${testUser.name}`);
    }

    const userId = testUser?.id || (await prisma.user.findFirst({ where: { email: '<EMAIL>' } }))!.id;

    // 3. Check current installations
    console.log('\n3️⃣ Checking current installations...');
    const currentInstallations = await prisma.installedNode.findMany({
      where: { userId },
      include: {
        node: {
          select: {
            name: true,
            tier: true
          }
        }
      }
    });

    console.log(`   Current installations: ${currentInstallations.length}`);
    currentInstallations.forEach(install => {
      console.log(`   - ${install.node.name} (${install.status}) - Enabled: ${install.enabled}`);
    });

    // 4. Test installation of a free node
    console.log('\n4️⃣ Testing installation of free node...');
    const freeNodeWithCode = marketplaceNodes.find(node =>
      node.tier === 'free' && node.nodeCodes.length > 0
    );

    if (!freeNodeWithCode) {
      console.log('   ❌ No free nodes with code available for testing');
      return;
    }

    console.log(`   Testing installation of: ${freeNodeWithCode.name}`);

    // Check if already installed
    const existingInstallation = await prisma.installedNode.findFirst({
      where: {
        userId,
        nodeId: freeNodeWithCode.id
      }
    });

    if (existingInstallation) {
      console.log(`   Node already installed with status: ${existingInstallation.status}`);

      // Update to uninstalled for testing
      await prisma.installedNode.delete({
        where: { id: existingInstallation.id }
      });
      console.log('   Removed existing installation for testing');
    }

    // Simulate installation
    const installation = await prisma.installedNode.create({
      data: {
        userId,
        nodeId: freeNodeWithCode.id,
        version: freeNodeWithCode.version,
        status: 'installed',
        enabled: true,
        installPath: `/nodes/${freeNodeWithCode.id}`,
        config: JSON.stringify({})
      }
    });

    console.log(`   ✅ Installation created: ${installation.id}`);

    // 5. Verify installation
    console.log('\n5️⃣ Verifying installation...');
    const verifyInstallation = await prisma.installedNode.findUnique({
      where: { id: installation.id },
      include: {
        node: {
          select: {
            name: true,
            tier: true
          }
        }
      }
    });

    if (verifyInstallation) {
      console.log(`   ✅ Installation verified:`);
      console.log(`      Node: ${verifyInstallation.node.name}`);
      console.log(`      Status: ${verifyInstallation.status}`);
      console.log(`      Enabled: ${verifyInstallation.enabled}`);
      console.log(`      Version: ${verifyInstallation.version}`);
    }

    // 6. Test marketplace status detection
    console.log('\n6️⃣ Testing marketplace status detection...');
    const allUserInstallations = await prisma.installedNode.findMany({
      where: { userId },
      include: {
        node: {
          select: {
            name: true
          }
        }
      }
    });

    console.log(`   User has ${allUserInstallations.length} installed nodes:`);
    allUserInstallations.forEach(install => {
      console.log(`   - ${install.node.name}: ${install.status} (enabled: ${install.enabled})`);
    });

    // 7. Test node availability for canvas
    console.log('\n7️⃣ Testing node availability for canvas...');
    const enabledInstallations = allUserInstallations.filter(install =>
      install.status === 'installed' && install.enabled
    );

    console.log(`   Nodes available for canvas: ${enabledInstallations.length}`);
    enabledInstallations.forEach(install => {
      console.log(`   - ${install.node.name} (ready for workflow)`);
    });

    console.log('\n🎉 Installation flow test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testInstallationFlow();
