/**
 * Seed script for marketplace sample data
 */

import { PrismaClient } from '@prisma/client';
import { samplePlugins } from '../lib/marketplace/sample-plugins';

const prisma = new PrismaClient();

async function seedMarketplace() {
  console.log('🌱 Seeding marketplace with sample data...');

  try {
    // First, ensure we have a test user to be the author
    let testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'marketplace_admin',
          name: 'Marketplace Admin',
          bio: 'Official marketplace administrator',
          emailVerified: true,
          avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=MA'
        }
      });
      console.log('✅ Created test user for marketplace');
    }

    // Clear existing marketplace data
    await prisma.nodePlugin.deleteMany({});
    console.log('🧹 Cleared existing marketplace data');

    // Create sample plugins
    for (const plugin of samplePlugins) {
      await prisma.nodePlugin.create({
        data: {
          id: plugin.id,
          name: plugin.name,
          version: plugin.version,
          description: plugin.description,
          longDescription: plugin.longDescription,
          authorId: testUser.id,
          category: plugin.category,
          tier: plugin.tier,
          price: plugin.price,
          subscriptionType: plugin.subscriptionType,
          tags: JSON.stringify(plugin.tags),
          dependencies: JSON.stringify(plugin.dependencies),
          permissions: JSON.stringify(plugin.permissions),
          icon: plugin.icon,
          screenshots: JSON.stringify(plugin.screenshots),
          downloadUrl: plugin.downloadUrl,
          repositoryUrl: plugin.repositoryUrl,
          documentationUrl: plugin.documentationUrl,
          verified: plugin.verified,
          featured: plugin.featured,
          rating: plugin.rating,
          reviewCount: plugin.reviewCount,
          downloads: plugin.downloads,
          weeklyDownloads: plugin.weeklyDownloads,
          lastUpdated: plugin.lastUpdated,
          createdAt: plugin.createdAt,
          compatibility: JSON.stringify(plugin.compatibility),
          changelog: plugin.changelog ? JSON.stringify(plugin.changelog) : null
        }
      });
    }

    console.log(`✅ Created ${samplePlugins.length} sample plugins`);

    // Create some sample reviews
    const plugins = await prisma.nodePlugin.findMany({ take: 3 });
    
    for (const plugin of plugins) {
      await prisma.nodeReview.create({
        data: {
          nodeId: plugin.id,
          userId: testUser.id,
          rating: 5,
          title: 'Excellent plugin!',
          comment: 'This plugin works perfectly and has great documentation. Highly recommended!',
          helpful: 12,
          verified: true
        }
      });
    }

    console.log('✅ Created sample reviews');

    // Update plugin review counts
    for (const plugin of plugins) {
      await prisma.nodePlugin.update({
        where: { id: plugin.id },
        data: { reviewCount: 1 }
      });
    }

    console.log('🎉 Marketplace seeding completed successfully!');
    
    // Display summary
    const totalPlugins = await prisma.nodePlugin.count();
    const featuredPlugins = await prisma.nodePlugin.count({ where: { featured: true } });
    const verifiedPlugins = await prisma.nodePlugin.count({ where: { verified: true } });
    
    console.log('\n📊 Marketplace Summary:');
    console.log(`   Total Plugins: ${totalPlugins}`);
    console.log(`   Featured Plugins: ${featuredPlugins}`);
    console.log(`   Verified Plugins: ${verifiedPlugins}`);
    console.log('\n🚀 Ready for testing!');

  } catch (error) {
    console.error('❌ Error seeding marketplace:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedMarketplace()
  .catch((error) => {
    console.error('Seeding failed:', error);
    process.exit(1);
  });
