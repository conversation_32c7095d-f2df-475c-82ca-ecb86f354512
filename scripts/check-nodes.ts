import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkNodes() {
  try {
    const nodes = await prisma.nodePlugin.findMany({
      select: {
        id: true,
        name: true,
        category: true,
        tier: true
      }
    });

    console.log('📦 Found nodes:');
    nodes.forEach(node => {
      console.log(`  - ${node.name} (${node.category}, ${node.tier})`);
    });

    console.log(`\n📊 Total: ${nodes.length} nodes`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkNodes();
