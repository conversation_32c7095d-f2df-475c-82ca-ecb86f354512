// Simple in-memory rate limiter for development
// In production, you should use Redis or a similar solution

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class InMemoryRateLimit {
  private store = new Map<string, RateLimitEntry>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (entry.resetTime <= now) {
        this.store.delete(key);
      }
    }
  }

  async limit(identifier: string, limit: number = 10, windowMs: number = 15 * 60 * 1000) {
    const now = Date.now();
    const resetTime = now + windowMs;
    
    const existing = this.store.get(identifier);
    
    if (!existing || existing.resetTime <= now) {
      // First request or window expired
      this.store.set(identifier, { count: 1, resetTime });
      return {
        success: true,
        limit,
        remaining: limit - 1,
        reset: resetTime,
        count: 1
      };
    }
    
    // Increment count
    existing.count++;
    this.store.set(identifier, existing);
    
    const success = existing.count <= limit;
    const remaining = Math.max(0, limit - existing.count);
    
    return {
      success,
      limit,
      remaining,
      reset: existing.resetTime,
      count: existing.count
    };
  }

  // Get current status without incrementing
  async check(identifier: string): Promise<RateLimitEntry | null> {
    const entry = this.store.get(identifier);
    if (!entry || entry.resetTime <= Date.now()) {
      return null;
    }
    return entry;
  }

  // Reset a specific identifier
  async reset(identifier: string): Promise<void> {
    this.store.delete(identifier);
  }

  // Get store size for monitoring
  getStoreSize(): number {
    return this.store.size;
  }

  // Destroy the rate limiter
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.store.clear();
  }
}

// Create a singleton instance
export const ratelimit = new InMemoryRateLimit();

// Helper function to parse time strings like "15m", "1h", "30s"
export function parseTimeString(timeStr: string): number {
  const match = timeStr.match(/^(\d+)([smhd])$/);
  if (!match) {
    throw new Error(`Invalid time string: ${timeStr}`);
  }
  
  const value = parseInt(match[1], 10);
  const unit = match[2];
  
  switch (unit) {
    case 's': return value * 1000;
    case 'm': return value * 60 * 1000;
    case 'h': return value * 60 * 60 * 1000;
    case 'd': return value * 24 * 60 * 60 * 1000;
    default: throw new Error(`Invalid time unit: ${unit}`);
  }
}

// Enhanced rate limiter with configurable windows
export class ConfigurableRateLimit {
  private limiter = new InMemoryRateLimit();
  
  async limit(identifier: string, requests: number, window: string) {
    const windowMs = parseTimeString(window);
    return this.limiter.limit(identifier, requests, windowMs);
  }
  
  async check(identifier: string) {
    return this.limiter.check(identifier);
  }
  
  async reset(identifier: string) {
    return this.limiter.reset(identifier);
  }
}

// Export a configured instance
export const configurableRateLimit = new ConfigurableRateLimit();

// Rate limiting decorator for API routes
export function withRateLimit(requests: number, window: string) {
  return function (handler: Function) {
    return async function (request: Request, ...args: any[]) {
      const clientIP = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'anonymous';
      
      const identifier = `${clientIP}:${request.url}`;
      const result = await configurableRateLimit.limit(identifier, requests, window);
      
      if (!result.success) {
        return new Response(
          JSON.stringify({
            error: 'Too many requests',
            retryAfter: Math.round((result.reset - Date.now()) / 1000)
          }),
          {
            status: 429,
            headers: {
              'Content-Type': 'application/json',
              'X-RateLimit-Limit': result.limit.toString(),
              'X-RateLimit-Remaining': result.remaining.toString(),
              'X-RateLimit-Reset': result.reset.toString(),
              'Retry-After': Math.round((result.reset - Date.now()) / 1000).toString(),
            }
          }
        );
      }
      
      // Call the original handler
      const response = await handler(request, ...args);
      
      // Add rate limit headers to successful responses
      if (response instanceof Response) {
        response.headers.set('X-RateLimit-Limit', result.limit.toString());
        response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
        response.headers.set('X-RateLimit-Reset', result.reset.toString());
      }
      
      return response;
    };
  };
}

// Utility to get client IP from request
export function getClientIP(request: Request): string {
  return request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
         request.headers.get('x-real-ip') ||
         request.headers.get('cf-connecting-ip') ||
         'anonymous';
}
