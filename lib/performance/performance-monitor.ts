"use client";

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = false;

  constructor() {
    // Only enable in development or when explicitly enabled
    this.isEnabled = process.env.NODE_ENV === 'development' || 
                     typeof window !== 'undefined' && window.localStorage.getItem('performance-debug') === 'true';
  }

  startTimer(name: string): void {
    if (!this.isEnabled) return;

    this.metrics.set(name, {
      name,
      startTime: performance.now()
    });
  }

  endTimer(name: string): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance timer "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    // Log slow operations (> 100ms)
    if (duration > 100) {
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`);
    }

    return duration;
  }

  getMetric(name: string): PerformanceMetric | undefined {
    return this.metrics.get(name);
  }

  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  getSlowOperations(threshold: number = 100): PerformanceMetric[] {
    return this.getAllMetrics().filter(metric => 
      metric.duration && metric.duration > threshold
    );
  }

  clear(): void {
    this.metrics.clear();
  }

  enable(): void {
    this.isEnabled = true;
    if (typeof window !== 'undefined') {
      window.localStorage.setItem('performance-debug', 'true');
    }
  }

  disable(): void {
    this.isEnabled = false;
    if (typeof window !== 'undefined') {
      window.localStorage.removeItem('performance-debug');
    }
  }

  isDebugEnabled(): boolean {
    return this.isEnabled;
  }
}

// Global instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export function usePerformanceTimer(name: string) {
  const start = () => performanceMonitor.startTimer(name);
  const end = () => performanceMonitor.endTimer(name);
  
  return { start, end };
}

// Decorator for timing functions
export function timed(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const timerName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      performanceMonitor.startTimer(timerName);
      try {
        const result = await originalMethod.apply(this, args);
        return result;
      } finally {
        performanceMonitor.endTimer(timerName);
      }
    };

    return descriptor;
  };
}
