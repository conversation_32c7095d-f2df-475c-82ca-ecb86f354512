import nodemailer from 'nodemailer';

// In a real application, you would use environment variables for these settings
const SMTP_HOST = process.env.SMTP_HOST || 'smtp.example.com';
const SMTP_PORT = parseInt(process.env.SMTP_PORT || '587');
const SMTP_USER = process.env.SMTP_USER || '<EMAIL>';
const SMTP_PASSWORD = process.env.SMTP_PASSWORD || 'password';
const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>';
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

// For development, we'll use a test account from Ethereal
// In production, you would use a real SMTP service
let testAccount: nodemailer.TestAccount | null = null;

async function getTransporter() {
  if (process.env.NODE_ENV === 'production') {
    // Use real SMTP service in production
    return nodemailer.createTransport({
      host: SMTP_HOST,
      port: SMTP_PORT,
      secure: SMTP_PORT === 465,
      auth: {
        user: SMTP_USER,
        pass: SMTP_PASSWORD,
      },
    });
  } else {
    // Use Ethereal for development
    if (!testAccount) {
      testAccount = await nodemailer.createTestAccount();
    }
    
    return nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass,
      },
    });
  }
}

export async function sendVerificationEmail(email: string, token: string) {
  const transporter = await getTransporter();
  const verificationUrl = `${BASE_URL}/verify-email?token=${token}`;
  
  const info = await transporter.sendMail({
    from: FROM_EMAIL,
    to: email,
    subject: 'Verify your email address',
    text: `Please verify your email address by clicking on the following link: ${verificationUrl}`,
    html: `
      <div>
        <h1>Email Verification</h1>
        <p>Please verify your email address by clicking on the following link:</p>
        <p><a href="${verificationUrl}">${verificationUrl}</a></p>
      </div>
    `,
  });
  
  if (process.env.NODE_ENV !== 'production') {
    console.log('Verification email sent: %s', nodemailer.getTestMessageUrl(info));
  }
  
  return info;
}

export async function sendPasswordResetEmail(email: string, token: string) {
  const transporter = await getTransporter();
  const resetUrl = `${BASE_URL}/reset-password?token=${token}`;
  
  const info = await transporter.sendMail({
    from: FROM_EMAIL,
    to: email,
    subject: 'Reset your password',
    text: `Please reset your password by clicking on the following link: ${resetUrl}`,
    html: `
      <div>
        <h1>Password Reset</h1>
        <p>Please reset your password by clicking on the following link:</p>
        <p><a href="${resetUrl}">${resetUrl}</a></p>
        <p>If you didn't request a password reset, you can ignore this email.</p>
      </div>
    `,
  });
  
  if (process.env.NODE_ENV !== 'production') {
    console.log('Password reset email sent: %s', nodemailer.getTestMessageUrl(info));
  }
  
  return info;
}
