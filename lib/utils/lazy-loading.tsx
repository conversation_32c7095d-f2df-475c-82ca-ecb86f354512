"use client";

import React, { lazy, Suspense, ComponentType, ReactNode } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface LazyComponentOptions {
  fallback?: ReactNode;
  errorBoundary?: boolean;
}

/**
 * Create a lazy-loaded component with loading fallback
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyComponentOptions = {}
): ComponentType<React.ComponentProps<T>> {
  const LazyComponent = lazy(importFn);
  
  const fallback = options.fallback || <LoadingSpinner size="md" />;

  return function LazyWrapper(props: React.ComponentProps<T>) {
    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

/**
 * Preload a component for better performance
 */
export function preloadComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
): void {
  // Start loading the component in the background
  importFn().catch(error => {
    console.warn('Failed to preload component:', error);
  });
}

/**
 * Create a lazy route component
 */
export function createLazyRoute(
  importFn: () => Promise<{ default: ComponentType<any> }>,
  loadingText?: string
) {
  return createLazyComponent(importFn, {
    fallback: (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" text={loadingText || "Loading page..."} />
      </div>
    )
  });
}

/**
 * Intersection Observer based lazy loading for components
 */
export function useLazyLoad(
  ref: React.RefObject<HTMLElement>,
  callback: () => void,
  options: IntersectionObserverInit = {}
) {
  const defaultOptions: IntersectionObserverInit = {
    threshold: 0.1,
    rootMargin: '50px',
    ...options
  };

  React.useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            callback();
            observer.unobserve(element);
          }
        });
      },
      defaultOptions
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [ref, callback]);
}

/**
 * Lazy load images with placeholder
 */
export function LazyImage({
  src,
  alt,
  className,
  placeholder,
  ...props
}: React.ImgHTMLAttributes<HTMLImageElement> & {
  placeholder?: ReactNode;
}) {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isError, setIsError] = React.useState(false);

  return (
    <div className={`relative ${className}`}>
      {!isLoaded && !isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          {placeholder || <LoadingSpinner size="sm" />}
        </div>
      )}
      <img
        src={src}
        alt={alt}
        onLoad={() => setIsLoaded(true)}
        onError={() => setIsError(true)}
        className={`transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        {...props}
      />
      {isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
          Failed to load image
        </div>
      )}
    </div>
  );
}
