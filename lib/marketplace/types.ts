/**
 * Node Plugin Marketplace - Type Definitions
 * Core types for the plugin system and marketplace
 */

export enum NodeTier {
  FREE = 'free',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
  SUBSCRIPTION = 'subscription'
}

export enum NodeCategory {
  AI_ML = 'ai_ml',
  DATA_PROCESSING = 'data_processing',
  TEXT_PROCESSING = 'text_processing',
  API_INTEGRATION = 'api_integration',
  AUTOMATION = 'automation',
  UTILITY = 'utility',
  ENTERPRISE = 'enterprise',
  SOCIAL_MEDIA = 'social_media',
  FINANCE = 'finance',
  COMMUNICATION = 'communication',
  ANALYTICS = 'analytics'
}

export enum InstallationStatus {
  NOT_INSTALLED = 'not_installed',
  INSTALLING = 'installing',
  INSTALLED = 'installed',
  UPDATE_AVAILABLE = 'update_available',
  UPDATING = 'updating',
  ERROR = 'error',
  DISABLED = 'disabled'
}

export interface NodePermission {
  type: 'network' | 'storage' | 'system' | 'user_data';
  description: string;
  required: boolean;
}

export interface NodeDependency {
  name: string;
  version: string;
  optional?: boolean;
}

export interface NodePlugin {
  id: string;
  name: string;
  version: string;
  description: string;
  longDescription?: string;
  author: {
    name: string;
    email: string;
    website?: string;
    avatar?: string;
  };
  category: NodeCategory;
  tier: NodeTier;
  price?: number;
  subscriptionType?: 'monthly' | 'yearly';
  tags: string[];
  dependencies: NodeDependency[];
  permissions: NodePermission[];
  icon: string;
  screenshots: string[];
  downloadUrl: string;
  repositoryUrl?: string;
  documentationUrl?: string;
  verified: boolean;
  featured: boolean;
  rating: number;
  reviewCount: number;
  downloads: number;
  weeklyDownloads: number;
  lastUpdated: Date;
  createdAt: Date;
  compatibility: {
    minVersion: string;
    maxVersion?: string;
  };
  changelog?: {
    version: string;
    date: Date;
    changes: string[];
  }[];
}

export interface InstalledNode extends NodePlugin {
  installationStatus: InstallationStatus;
  installedVersion: string;
  installedAt: Date;
  enabled: boolean;
  usageCount: number;
  lastUsed?: Date;
}

export interface NodeReview {
  id: string;
  nodeId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  comment: string;
  helpful: number;
  createdAt: Date;
  verified: boolean;
}

export interface PricingModel {
  tier: NodeTier;
  price?: number;
  subscriptionType?: 'monthly' | 'yearly';
  trialPeriod?: number; // days
  features: string[];
  limitations?: {
    usageLimit?: number;
    timeLimit?: number;
    workflowLimit?: number;
  };
}

export interface PaymentIntent {
  id: string;
  nodeId: string;
  userId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed' | 'canceled';
  clientSecret: string;
  createdAt: Date;
}

export interface NodeSubscription {
  id: string;
  userId: string;
  planId: string;
  nodeIds: string[];
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  createdAt: Date;
}

export interface DeveloperMetrics {
  developerId: string;
  totalNodes: number;
  totalDownloads: number;
  activeUsers: number;
  revenue: {
    total: number;
    monthly: number;
    yearly: number;
  };
  ratings: {
    average: number;
    distribution: Record<number, number>;
  };
  usageStats: {
    daily: number[];
    weekly: number[];
    monthly: number[];
  };
}

export interface MarketplaceFilters {
  category?: NodeCategory;
  tier?: NodeTier;
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  verified?: boolean;
  featured?: boolean;
  search?: string;
  sortBy?: 'popularity' | 'rating' | 'newest' | 'price_low' | 'price_high' | 'name';
  page?: number;
  limit?: number;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  securityIssues: string[];
}

export interface NodeManifest {
  name: string;
  version: string;
  description: string;
  main: string;
  author: string;
  license: string;
  keywords: string[];
  dependencies: Record<string, string>;
  peerDependencies?: Record<string, string>;
  engines: {
    node: string;
    npm?: string;
  };
  marketplace: {
    category: NodeCategory;
    tier: NodeTier;
    price?: number;
    permissions: NodePermission[];
    icon: string;
    screenshots: string[];
  };
}

export interface PluginLoadResult {
  success: boolean;
  plugin?: any;
  error?: string;
  warnings?: string[];
}

export interface NodeUsageEvent {
  nodeId: string;
  userId: string;
  workflowId: string;
  timestamp: Date;
  duration?: number;
  success: boolean;
  error?: string;
}

export interface MarketplaceConfig {
  apiUrl: string;
  cdnUrl: string;
  paymentProvider: 'stripe' | 'paypal';
  allowedFileTypes: string[];
  maxFileSize: number;
  sandboxEnabled: boolean;
  reviewModerationEnabled: boolean;
}
