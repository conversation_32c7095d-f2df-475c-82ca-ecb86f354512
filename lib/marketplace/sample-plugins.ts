/**
 * Sample Plugin Nodes for Marketplace Demo
 * These are example nodes to demonstrate the marketplace functionality
 */

import { NodePlugin, NodeCategory, NodeTier } from './types';

export const samplePlugins: NodePlugin[] = [
  {
    id: 'openai-gpt-node',
    name: 'OpenAI GPT Node',
    version: '1.2.0',
    description: 'Advanced AI text generation using OpenAI GPT models',
    longDescription: 'Connect to OpenAI\'s powerful GPT models for text generation, completion, and conversation. Supports GPT-4, GPT-3.5-turbo, and custom fine-tuned models.',
    author: {
      name: 'AI Innovations',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=AI'
    },
    category: NodeCategory.AI_ML,
    tier: NodeTier.PREMIUM,
    price: 9.99,
    tags: ['ai', 'gpt', 'openai', 'text-generation', 'nlp'],
    dependencies: [
      { name: 'openai', version: '^4.0.0' }
    ],
    permissions: [
      { type: 'network', description: 'Access OpenAI API', required: true }
    ],
    icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=openai&backgroundColor=10b981',
    screenshots: [
      'https://via.placeholder.com/400x300/10b981/ffffff?text=OpenAI+GPT+Node',
      'https://via.placeholder.com/400x300/059669/ffffff?text=Configuration'
    ],
    downloadUrl: '/api/marketplace/download/openai-gpt-node-1.2.0.zip',
    repositoryUrl: 'https://github.com/ai-innovations/openai-gpt-node',
    documentationUrl: 'https://docs.ai-innovations.com/openai-gpt-node',
    verified: true,
    featured: true,
    rating: 4.8,
    reviewCount: 156,
    downloads: 12450,
    weeklyDownloads: 890,
    lastUpdated: new Date('2024-01-15'),
    createdAt: new Date('2023-08-10'),
    compatibility: {
      minVersion: '1.0.0'
    },
    changelog: [
      {
        version: '1.2.0',
        date: new Date('2024-01-15'),
        changes: ['Added GPT-4 Turbo support', 'Improved error handling', 'Performance optimizations']
      }
    ]
  },
  {
    id: 'slack-integration-node',
    name: 'Slack Integration',
    version: '2.1.3',
    description: 'Send messages, create channels, and manage Slack workspaces',
    longDescription: 'Complete Slack integration for workflow automation. Send messages to channels or users, create and manage channels, upload files, and respond to Slack events.',
    author: {
      name: 'Workflow Pro',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=WP'
    },
    category: NodeCategory.COMMUNICATION,
    tier: NodeTier.FREE,
    tags: ['slack', 'messaging', 'communication', 'notifications'],
    dependencies: [
      { name: '@slack/web-api', version: '^6.0.0' }
    ],
    permissions: [
      { type: 'network', description: 'Access Slack API', required: true }
    ],
    icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=slack&backgroundColor=4a154b',
    screenshots: [
      'https://via.placeholder.com/400x300/4a154b/ffffff?text=Slack+Integration',
      'https://via.placeholder.com/400x300/7c3aed/ffffff?text=Message+Builder'
    ],
    downloadUrl: '/api/marketplace/download/slack-integration-node-2.1.3.zip',
    repositoryUrl: 'https://github.com/workflowpro/slack-integration-node',
    verified: true,
    featured: true,
    rating: 4.6,
    reviewCount: 89,
    downloads: 8920,
    weeklyDownloads: 340,
    lastUpdated: new Date('2024-01-10'),
    createdAt: new Date('2023-05-20'),
    compatibility: {
      minVersion: '1.0.0'
    }
  },
  {
    id: 'advanced-csv-processor',
    name: 'Advanced CSV Processor',
    version: '1.5.2',
    description: 'Powerful CSV processing with filtering, transformation, and validation',
    longDescription: 'Process large CSV files with advanced filtering, data transformation, validation rules, and export capabilities. Supports custom JavaScript transformations and data quality checks.',
    author: {
      name: 'Data Tools Inc',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=DT'
    },
    category: NodeCategory.DATA_PROCESSING,
    tier: NodeTier.PREMIUM,
    price: 4.99,
    tags: ['csv', 'data-processing', 'transformation', 'validation'],
    dependencies: [
      { name: 'papaparse', version: '^5.0.0' },
      { name: 'lodash', version: '^4.0.0' }
    ],
    permissions: [
      { type: 'storage', description: 'Read and write CSV files', required: true }
    ],
    icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=csv&backgroundColor=dc2626',
    screenshots: [
      'https://via.placeholder.com/400x300/dc2626/ffffff?text=CSV+Processor',
      'https://via.placeholder.com/400x300/b91c1c/ffffff?text=Data+Preview'
    ],
    downloadUrl: '/api/marketplace/download/advanced-csv-processor-1.5.2.zip',
    verified: true,
    featured: false,
    rating: 4.4,
    reviewCount: 67,
    downloads: 5670,
    weeklyDownloads: 180,
    lastUpdated: new Date('2024-01-08'),
    createdAt: new Date('2023-09-15'),
    compatibility: {
      minVersion: '1.0.0'
    }
  },
  {
    id: 'webhook-listener',
    name: 'Webhook Listener',
    version: '1.0.8',
    description: 'Receive and process HTTP webhooks from external services',
    longDescription: 'Create webhook endpoints to receive data from external services. Supports authentication, payload validation, and automatic workflow triggering.',
    author: {
      name: 'Integration Hub',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=IH'
    },
    category: NodeCategory.API_INTEGRATION,
    tier: NodeTier.FREE,
    tags: ['webhook', 'api', 'integration', 'http'],
    dependencies: [],
    permissions: [
      { type: 'network', description: 'Create webhook endpoints', required: true }
    ],
    icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=webhook&backgroundColor=2563eb',
    screenshots: [
      'https://via.placeholder.com/400x300/2563eb/ffffff?text=Webhook+Listener'
    ],
    downloadUrl: '/api/marketplace/download/webhook-listener-1.0.8.zip',
    repositoryUrl: 'https://github.com/integrationhub/webhook-listener-node',
    verified: true,
    featured: false,
    rating: 4.2,
    reviewCount: 34,
    downloads: 3450,
    weeklyDownloads: 120,
    lastUpdated: new Date('2024-01-05'),
    createdAt: new Date('2023-11-01'),
    compatibility: {
      minVersion: '1.0.0'
    }
  },
  {
    id: 'enterprise-salesforce',
    name: 'Salesforce Enterprise Connector',
    version: '3.2.1',
    description: 'Complete Salesforce integration for enterprise workflows',
    longDescription: 'Enterprise-grade Salesforce integration with support for custom objects, bulk operations, real-time sync, and advanced security features.',
    author: {
      name: 'Enterprise Solutions',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=ES'
    },
    category: NodeCategory.ENTERPRISE,
    tier: NodeTier.ENTERPRISE,
    price: 49.99,
    tags: ['salesforce', 'crm', 'enterprise', 'integration'],
    dependencies: [
      { name: 'jsforce', version: '^2.0.0' }
    ],
    permissions: [
      { type: 'network', description: 'Access Salesforce API', required: true },
      { type: 'user_data', description: 'Access customer data', required: true }
    ],
    icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=salesforce&backgroundColor=0ea5e9',
    screenshots: [
      'https://via.placeholder.com/400x300/0ea5e9/ffffff?text=Salesforce+Enterprise',
      'https://via.placeholder.com/400x300/0284c7/ffffff?text=Object+Manager'
    ],
    downloadUrl: '/api/marketplace/download/enterprise-salesforce-3.2.1.zip',
    documentationUrl: 'https://docs.enterprise-solutions.com/salesforce-connector',
    verified: true,
    featured: true,
    rating: 4.9,
    reviewCount: 23,
    downloads: 890,
    weeklyDownloads: 45,
    lastUpdated: new Date('2024-01-12'),
    createdAt: new Date('2023-07-01'),
    compatibility: {
      minVersion: '1.0.0'
    }
  },
  {
    id: 'image-optimizer',
    name: 'Image Optimizer Pro',
    version: '2.0.4',
    description: 'Compress, resize, and optimize images for web and mobile',
    longDescription: 'Professional image optimization with support for multiple formats, batch processing, and intelligent compression algorithms.',
    author: {
      name: 'Media Tools',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=MT'
    },
    category: NodeCategory.UTILITY,
    tier: NodeTier.PREMIUM,
    price: 7.99,
    tags: ['image', 'optimization', 'compression', 'media'],
    dependencies: [
      { name: 'sharp', version: '^0.32.0' }
    ],
    permissions: [
      { type: 'storage', description: 'Read and write image files', required: true }
    ],
    icon: 'https://api.dicebear.com/7.x/shapes/svg?seed=image&backgroundColor=f59e0b',
    screenshots: [
      'https://via.placeholder.com/400x300/f59e0b/ffffff?text=Image+Optimizer'
    ],
    downloadUrl: '/api/marketplace/download/image-optimizer-2.0.4.zip',
    verified: true,
    featured: false,
    rating: 4.5,
    reviewCount: 78,
    downloads: 4320,
    weeklyDownloads: 210,
    lastUpdated: new Date('2024-01-07'),
    createdAt: new Date('2023-06-12'),
    compatibility: {
      minVersion: '1.0.0'
    }
  }
];

// Helper function to seed the database with sample plugins
export async function seedSamplePlugins() {
  // This would be used to populate the database with sample data
  // Implementation would depend on your database setup
  console.log('Sample plugins ready for seeding:', samplePlugins.length);
  return samplePlugins;
}
