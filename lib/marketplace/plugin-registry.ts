/**
 * Plugin Registry System
 * Manages dynamic loading, registration, and lifecycle of node plugins
 */

import { NodePlugin, InstalledNode, InstallationStatus, PluginLoadResult, ValidationResult } from './types';
import { StandardNodeProps } from '../workflow/node-interface';
import { memo } from 'react';

export class PluginRegistry {
  private static instance: PluginRegistry;
  private installedNodes = new Map<string, any>();
  private pluginMetadata = new Map<string, InstalledNode>();
  private loadedComponents = new Map<string, React.ComponentType<StandardNodeProps>>();
  private eventListeners = new Map<string, Function[]>();

  private constructor() {}

  static getInstance(): PluginRegistry {
    if (!PluginRegistry.instance) {
      PluginRegistry.instance = new PluginRegistry();
    }
    return PluginRegistry.instance;
  }

  /**
   * Register a new plugin node
   */
  async registerPlugin(plugin: NodePlugin, component: React.ComponentType<StandardNodeProps>): Promise<void> {
    try {
      // Validate plugin
      const validation = await this.validatePlugin(plugin);
      if (!validation.valid) {
        throw new Error(`Plugin validation failed: ${validation.errors.join(', ')}`);
      }

      // Wrap component with memo for performance
      const MemoizedComponent = memo(component);
      MemoizedComponent.displayName = `Plugin_${plugin.name}`;

      // Register in maps
      this.loadedComponents.set(plugin.id, MemoizedComponent);
      this.pluginMetadata.set(plugin.id, {
        ...plugin,
        installationStatus: InstallationStatus.INSTALLED,
        installedVersion: plugin.version,
        installedAt: new Date(),
        enabled: true,
        usageCount: 0
      });

      // Emit registration event
      this.emit('plugin:registered', plugin);

      console.log(`Plugin registered: ${plugin.name} (${plugin.id})`);
    } catch (error) {
      console.error(`Failed to register plugin ${plugin.id}:`, error);
      throw error;
    }
  }

  /**
   * Unregister a plugin
   */
  async unregisterPlugin(pluginId: string): Promise<void> {
    try {
      const plugin = this.pluginMetadata.get(pluginId);
      if (!plugin) {
        throw new Error(`Plugin ${pluginId} not found`);
      }

      // Check if plugin is in use
      const isInUse = await this.isPluginInUse(pluginId);
      if (isInUse) {
        throw new Error(`Cannot unregister plugin ${pluginId}: currently in use`);
      }

      // Remove from registry
      this.loadedComponents.delete(pluginId);
      this.pluginMetadata.delete(pluginId);
      this.installedNodes.delete(pluginId);

      // Emit unregistration event
      this.emit('plugin:unregistered', plugin);

      console.log(`Plugin unregistered: ${plugin.name} (${pluginId})`);
    } catch (error) {
      console.error(`Failed to unregister plugin ${pluginId}:`, error);
      throw error;
    }
  }

  /**
   * Get a registered plugin component
   */
  getPluginComponent(pluginId: string): React.ComponentType<StandardNodeProps> | undefined {
    return this.loadedComponents.get(pluginId);
  }

  /**
   * Get plugin metadata
   */
  getPluginMetadata(pluginId: string): InstalledNode | undefined {
    return this.pluginMetadata.get(pluginId);
  }

  /**
   * Get all installed plugins
   */
  getInstalledPlugins(): InstalledNode[] {
    return Array.from(this.pluginMetadata.values());
  }

  /**
   * Get all available plugin components for the node palette
   */
  getAvailableNodes(): Record<string, React.ComponentType<StandardNodeProps>> {
    const nodes: Record<string, React.ComponentType<StandardNodeProps>> = {};
    
    this.loadedComponents.forEach((component, pluginId) => {
      const metadata = this.pluginMetadata.get(pluginId);
      if (metadata && metadata.enabled) {
        nodes[pluginId] = component;
      }
    });

    return nodes;
  }

  /**
   * Enable/disable a plugin
   */
  async togglePlugin(pluginId: string, enabled: boolean): Promise<void> {
    const plugin = this.pluginMetadata.get(pluginId);
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} not found`);
    }

    if (!enabled) {
      // Check if plugin is in use before disabling
      const isInUse = await this.isPluginInUse(pluginId);
      if (isInUse) {
        throw new Error(`Cannot disable plugin ${pluginId}: currently in use`);
      }
    }

    plugin.enabled = enabled;
    this.pluginMetadata.set(pluginId, plugin);

    // Emit toggle event
    this.emit('plugin:toggled', { pluginId, enabled });

    console.log(`Plugin ${enabled ? 'enabled' : 'disabled'}: ${plugin.name}`);
  }

  /**
   * Update plugin usage statistics
   */
  updateUsageStats(pluginId: string): void {
    const plugin = this.pluginMetadata.get(pluginId);
    if (plugin) {
      plugin.usageCount++;
      plugin.lastUsed = new Date();
      this.pluginMetadata.set(pluginId, plugin);
    }
  }

  /**
   * Validate plugin before registration
   */
  private async validatePlugin(plugin: NodePlugin): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const securityIssues: string[] = [];

    // Basic validation
    if (!plugin.id || !plugin.name || !plugin.version) {
      errors.push('Plugin must have id, name, and version');
    }

    // Check for duplicate IDs
    if (this.pluginMetadata.has(plugin.id)) {
      errors.push(`Plugin with ID ${plugin.id} already exists`);
    }

    // Validate version format
    if (!/^\d+\.\d+\.\d+/.test(plugin.version)) {
      errors.push('Plugin version must follow semantic versioning (x.y.z)');
    }

    // Check permissions
    if (plugin.permissions.some(p => p.type === 'system' && !p.required)) {
      securityIssues.push('System permissions should be carefully reviewed');
    }

    // Validate dependencies
    for (const dep of plugin.dependencies) {
      if (!dep.name || !dep.version) {
        errors.push(`Invalid dependency: ${JSON.stringify(dep)}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      securityIssues
    };
  }

  /**
   * Check if plugin is currently in use in any workflows
   */
  private async isPluginInUse(pluginId: string): Promise<boolean> {
    // This would check against active workflows in the database
    // For now, return false as a placeholder
    // TODO: Implement actual usage checking
    return false;
  }

  /**
   * Event system for plugin lifecycle
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Load plugin from URL or file
   */
  async loadPlugin(pluginUrl: string): Promise<PluginLoadResult> {
    try {
      // This would implement the actual plugin loading logic
      // For now, return a placeholder
      return {
        success: false,
        error: 'Plugin loading not yet implemented'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get plugin statistics
   */
  getPluginStats() {
    const plugins = this.getInstalledPlugins();
    return {
      total: plugins.length,
      enabled: plugins.filter(p => p.enabled).length,
      disabled: plugins.filter(p => !p.enabled).length,
      byCategory: plugins.reduce((acc, plugin) => {
        acc[plugin.category] = (acc[plugin.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      byTier: plugins.reduce((acc, plugin) => {
        acc[plugin.tier] = (acc[plugin.tier] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };
  }
}

// Export singleton instance
export const pluginRegistry = PluginRegistry.getInstance();
