import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface NodeExecutionMetrics {
  nodeId: string;
  userId: string;
  executionTime: number;
  memoryUsage?: number;
  success: boolean;
  errorType?: string;
  inputSize?: number;
  outputSize?: number;
  timestamp: Date;
}

export interface NodePerformanceStats {
  nodeId: string;
  totalExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  medianExecutionTime: number;
  p95ExecutionTime: number;
  errorRate: number;
  commonErrors: Array<{ error: string; count: number }>;
  usageGrowth: Array<{ date: string; executions: number }>;
}

export class NodeAnalytics {
  private static instance: NodeAnalytics;

  static getInstance(): NodeAnalytics {
    if (!NodeAnalytics.instance) {
      NodeAnalytics.instance = new NodeAnalytics();
    }
    return NodeAnalytics.instance;
  }

  /**
   * Record a node execution
   */
  async recordExecution(metrics: NodeExecutionMetrics): Promise<void> {
    try {
      await prisma.nodeExecutionLog.create({
        data: {
          nodeId: metrics.nodeId,
          userId: metrics.userId,
          nodeType: 'marketplace', // or get from node definition
          status: metrics.success ? 'completed' : 'failed',
          error: metrics.errorType,
          duration: metrics.executionTime,
          startedAt: metrics.timestamp,
          completedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error recording node execution:', error);
    }
  }

  /**
   * Get performance statistics for a node
   */
  async getNodePerformanceStats(nodeId: string, days: number = 30): Promise<NodePerformanceStats> {
    try {
      const since = new Date();
      since.setDate(since.getDate() - days);

      const executions = await prisma.nodeExecutionLog.findMany({
        where: {
          nodeId,
          startedAt: { gte: since }
        },
        orderBy: { startedAt: 'asc' }
      });

      const totalExecutions = executions.length;
      const successfulExecutions = executions.filter(e => e.status === 'completed').length;
      const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;
      const errorRate = 100 - successRate;

      // Calculate execution time statistics
      const executionTimes = executions
        .filter(e => e.status === 'completed' && e.duration)
        .map(e => e.duration!)
        .sort((a, b) => a - b);

      const averageExecutionTime = executionTimes.length > 0
        ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length
        : 0;

      const medianExecutionTime = executionTimes.length > 0
        ? executionTimes[Math.floor(executionTimes.length / 2)]
        : 0;

      const p95ExecutionTime = executionTimes.length > 0
        ? executionTimes[Math.floor(executionTimes.length * 0.95)]
        : 0;

      // Calculate common errors
      const errorCounts = new Map<string, number>();
      executions
        .filter(e => e.status === 'failed' && e.error)
        .forEach(e => {
          const count = errorCounts.get(e.error!) || 0;
          errorCounts.set(e.error!, count + 1);
        });

      const commonErrors = Array.from(errorCounts.entries())
        .map(([error, count]) => ({ error, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Calculate usage growth (daily)
      const usageByDay = new Map<string, number>();
      executions.forEach(e => {
        const date = e.startedAt.toISOString().split('T')[0];
        const count = usageByDay.get(date) || 0;
        usageByDay.set(date, count + 1);
      });

      const usageGrowth = Array.from(usageByDay.entries())
        .map(([date, executions]) => ({ date, executions }))
        .sort((a, b) => a.date.localeCompare(b.date));

      return {
        nodeId,
        totalExecutions,
        successRate,
        averageExecutionTime,
        medianExecutionTime,
        p95ExecutionTime,
        errorRate,
        commonErrors,
        usageGrowth
      };

    } catch (error) {
      console.error('Error getting node performance stats:', error);
      return {
        nodeId,
        totalExecutions: 0,
        successRate: 0,
        averageExecutionTime: 0,
        medianExecutionTime: 0,
        p95ExecutionTime: 0,
        errorRate: 0,
        commonErrors: [],
        usageGrowth: []
      };
    }
  }

  /**
   * Get user's node usage statistics
   */
  async getUserNodeStats(userId: string, days: number = 30): Promise<{
    totalExecutions: number;
    uniqueNodesUsed: number;
    averageExecutionTime: number;
    successRate: number;
    mostUsedNodes: Array<{ nodeId: string; nodeName: string; executions: number }>;
    dailyUsage: Array<{ date: string; executions: number }>;
  }> {
    try {
      const since = new Date();
      since.setDate(since.getDate() - days);

      const executions = await prisma.nodeExecutionLog.findMany({
        where: {
          userId,
          startedAt: { gte: since }
        },
        orderBy: {
          startedAt: 'desc'
        }
      });

      const totalExecutions = executions.length;
      const uniqueNodesUsed = new Set(executions.map(e => e.nodeId)).size;
      const successfulExecutions = executions.filter(e => e.status === 'completed');
      const successRate = totalExecutions > 0 ? (successfulExecutions.length / totalExecutions) * 100 : 0;
      const averageExecutionTime = successfulExecutions.length > 0
        ? successfulExecutions.reduce((sum, e) => sum + (e.duration || 0), 0) / successfulExecutions.length
        : 0;

      // Most used nodes
      const nodeUsage = new Map<string, { name: string; count: number }>();
      executions.forEach(e => {
        const existing = nodeUsage.get(e.nodeId) || { name: e.nodeType || e.nodeId, count: 0 };
        nodeUsage.set(e.nodeId, { ...existing, count: existing.count + 1 });
      });

      const mostUsedNodes = Array.from(nodeUsage.entries())
        .map(([nodeId, data]) => ({ nodeId, nodeName: data.name, executions: data.count }))
        .sort((a, b) => b.executions - a.executions)
        .slice(0, 10);

      // Daily usage
      const dailyUsage = new Map<string, number>();
      executions.forEach(e => {
        const date = e.startedAt.toISOString().split('T')[0];
        const count = dailyUsage.get(date) || 0;
        dailyUsage.set(date, count + 1);
      });

      const dailyUsageArray = Array.from(dailyUsage.entries())
        .map(([date, executions]) => ({ date, executions }))
        .sort((a, b) => a.date.localeCompare(b.date));

      return {
        totalExecutions,
        uniqueNodesUsed,
        averageExecutionTime,
        successRate,
        mostUsedNodes,
        dailyUsage: dailyUsageArray
      };

    } catch (error) {
      console.error('Error getting user node stats:', error);
      return {
        totalExecutions: 0,
        uniqueNodesUsed: 0,
        averageExecutionTime: 0,
        successRate: 0,
        mostUsedNodes: [],
        dailyUsage: []
      };
    }
  }

  /**
   * Get system-wide analytics
   */
  async getSystemAnalytics(days: number = 30): Promise<{
    totalExecutions: number;
    totalUsers: number;
    totalNodes: number;
    averageExecutionTime: number;
    systemSuccessRate: number;
    topPerformingNodes: Array<{ nodeId: string; nodeName: string; successRate: number; executions: number }>;
    systemLoad: Array<{ date: string; executions: number; uniqueUsers: number }>;
  }> {
    try {
      const since = new Date();
      since.setDate(since.getDate() - days);

      const executions = await prisma.nodeExecutionLog.findMany({
        where: { startedAt: { gte: since } },
        orderBy: {
          startedAt: 'desc'
        }
      });

      const totalExecutions = executions.length;
      const totalUsers = new Set(executions.map(e => e.userId)).size;
      const totalNodes = new Set(executions.map(e => e.nodeId)).size;
      const successfulExecutions = executions.filter(e => e.status === 'completed');
      const systemSuccessRate = totalExecutions > 0 ? (successfulExecutions.length / totalExecutions) * 100 : 0;
      const averageExecutionTime = successfulExecutions.length > 0
        ? successfulExecutions.reduce((sum, e) => sum + (e.duration || 0), 0) / successfulExecutions.length
        : 0;

      // Top performing nodes
      const nodeStats = new Map<string, { name: string; total: number; successful: number }>();
      executions.forEach(e => {
        const existing = nodeStats.get(e.nodeId) || { name: e.nodeType || e.nodeId, total: 0, successful: 0 };
        nodeStats.set(e.nodeId, {
          ...existing,
          total: existing.total + 1,
          successful: existing.successful + (e.status === 'completed' ? 1 : 0)
        });
      });

      const topPerformingNodes = Array.from(nodeStats.entries())
        .map(([nodeId, stats]) => ({
          nodeId,
          nodeName: stats.name,
          successRate: stats.total > 0 ? (stats.successful / stats.total) * 100 : 0,
          executions: stats.total
        }))
        .filter(node => node.executions >= 10) // Only nodes with significant usage
        .sort((a, b) => b.successRate - a.successRate)
        .slice(0, 10);

      // System load by day
      const dailyStats = new Map<string, { executions: number; users: Set<string> }>();
      executions.forEach(e => {
        const date = e.startedAt.toISOString().split('T')[0];
        const existing = dailyStats.get(date) || { executions: 0, users: new Set() };
        dailyStats.set(date, {
          executions: existing.executions + 1,
          users: existing.users.add(e.userId)
        });
      });

      const systemLoad = Array.from(dailyStats.entries())
        .map(([date, stats]) => ({
          date,
          executions: stats.executions,
          uniqueUsers: stats.users.size
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      return {
        totalExecutions,
        totalUsers,
        totalNodes,
        averageExecutionTime,
        systemSuccessRate,
        topPerformingNodes,
        systemLoad
      };

    } catch (error) {
      console.error('Error getting system analytics:', error);
      return {
        totalExecutions: 0,
        totalUsers: 0,
        totalNodes: 0,
        averageExecutionTime: 0,
        systemSuccessRate: 0,
        topPerformingNodes: [],
        systemLoad: []
      };
    }
  }
}

export const nodeAnalytics = NodeAnalytics.getInstance();
