// User Management System - Database Seeder

import { prisma } from '@/lib/prisma';
import { SYSTEM_PERMISSIONS, SYSTEM_ROLES } from './permissions';

export async function seedUserManagementSystem() {
  console.log('🌱 Seeding User Management System...');

  try {
    // 1. Create system permissions
    console.log('Creating system permissions...');
    const createdPermissions = new Map<string, string>();

    for (const perm of SYSTEM_PERMISSIONS) {
      const permission = await prisma.permission.upsert({
        where: {
          name: `${perm.resource}:${perm.action}`,
        },
        update: {
          displayName: perm.displayName,
          description: perm.description,
        },
        create: {
          name: `${perm.resource}:${perm.action}`,
          displayName: perm.displayName,
          description: perm.description,
          resource: perm.resource,
          action: perm.action,
          isSystem: true,
        },
      });
      
      createdPermissions.set(`${perm.resource}:${perm.action}`, permission.id);
    }

    console.log(`✅ Created ${createdPermissions.size} permissions`);

    // 2. Create system roles
    console.log('Creating system roles...');
    const createdRoles = new Map<string, string>();

    for (const roleData of SYSTEM_ROLES) {
      const role = await prisma.role.upsert({
        where: {
          name: roleData.name,
        },
        update: {
          displayName: roleData.displayName,
          description: roleData.description,
        },
        create: {
          name: roleData.name,
          displayName: roleData.displayName,
          description: roleData.description,
          isSystem: true,
        },
      });

      createdRoles.set(roleData.name, role.id);

      // Clear existing permissions for this role
      await prisma.rolePermission.deleteMany({
        where: { roleId: role.id },
      });

      // Assign permissions to role
      for (const permissionName of roleData.permissions) {
        const permissionId = createdPermissions.get(permissionName);
        if (permissionId) {
          await prisma.rolePermission.create({
            data: {
              roleId: role.id,
              permissionId,
            },
          });
        }
      }
    }

    console.log(`✅ Created ${createdRoles.size} roles`);

    // 3. Update existing users to have default roles
    console.log('Assigning default roles to existing users...');
    
    const existingUsers = await prisma.user.findMany({
      include: {
        userRoles: true,
      },
    });

    const defaultUserRoleId = createdRoles.get('user');
    const adminRoleId = createdRoles.get('admin');
    const superAdminRoleId = createdRoles.get('super_admin');

    for (const user of existingUsers) {
      // Skip if user already has roles
      if (user.userRoles.length > 0) continue;

      let roleId: string | undefined;

      if (user.isSuperAdmin && superAdminRoleId) {
        roleId = superAdminRoleId;
      } else if (user.isAdmin && adminRoleId) {
        roleId = adminRoleId;
      } else if (defaultUserRoleId) {
        roleId = defaultUserRoleId;
      }

      if (roleId) {
        await prisma.userRole.create({
          data: {
            userId: user.id,
            roleId,
          },
        });
      }
    }

    console.log(`✅ Assigned roles to ${existingUsers.length} existing users`);

    // 4. Create a super admin user if none exists
    const superAdminExists = await prisma.user.findFirst({
      where: { isSuperAdmin: true },
    });

    if (!superAdminExists) {
      console.log('Creating default super admin user...');
      
      const bcrypt = await import('bcryptjs');
      const hashedPassword = await bcrypt.hash('admin123!', 12);

      const superAdmin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'superadmin',
          name: 'Super Administrator',
          password: hashedPassword,
          emailVerified: true,
          isSuperAdmin: true,
          isAdmin: true,
          status: 'ACTIVE',
        },
      });

      // Assign super admin role
      if (superAdminRoleId) {
        await prisma.userRole.create({
          data: {
            userId: superAdmin.id,
            roleId: superAdminRoleId,
          },
        });
      }

      console.log('✅ Created super admin user: <EMAIL> / admin123!');
    }

    console.log('🎉 User Management System seeded successfully!');
    
    return {
      success: true,
      permissions: createdPermissions.size,
      roles: createdRoles.size,
      users: existingUsers.length,
    };

  } catch (error) {
    console.error('❌ Error seeding User Management System:', error);
    throw error;
  }
}

// Function to reset the user management system (for development)
export async function resetUserManagementSystem() {
  console.log('🔄 Resetting User Management System...');

  try {
    // Delete in correct order to avoid foreign key constraints
    await prisma.userRole.deleteMany();
    await prisma.rolePermission.deleteMany();
    await prisma.userLog.deleteMany();
    await prisma.loginHistory.deleteMany();
    
    // Delete system roles and permissions
    await prisma.role.deleteMany({
      where: { isSystem: true },
    });
    
    await prisma.permission.deleteMany({
      where: { isSystem: true },
    });

    // Reset user management fields
    await prisma.user.updateMany({
      data: {
        status: 'ACTIVE',
        lastLoginAt: null,
        lastActiveAt: null,
        loginCount: 0,
        metadata: null,
        createdById: null,
      },
    });

    console.log('✅ User Management System reset successfully!');
    
    return { success: true };

  } catch (error) {
    console.error('❌ Error resetting User Management System:', error);
    throw error;
  }
}

// Function to check system health
export async function checkUserManagementHealth() {
  try {
    const [
      permissionCount,
      roleCount,
      userCount,
      userRoleCount,
      rolePermissionCount,
    ] = await Promise.all([
      prisma.permission.count(),
      prisma.role.count(),
      prisma.user.count(),
      prisma.userRole.count(),
      prisma.rolePermission.count(),
    ]);

    const superAdminExists = await prisma.user.findFirst({
      where: { isSuperAdmin: true },
    });

    return {
      healthy: true,
      stats: {
        permissions: permissionCount,
        roles: roleCount,
        users: userCount,
        userRoles: userRoleCount,
        rolePermissions: rolePermissionCount,
        hasSuperAdmin: !!superAdminExists,
      },
    };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
