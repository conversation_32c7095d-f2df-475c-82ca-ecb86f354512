/**
 * Doku Payment Gateway Configuration
 * Configuration for Doku payment integration
 */

// Doku configuration
export const dokuConfig = {
  // Client ID for Doku API (MCH-XXXX- for merchant, BRN-XXXX- for business)
  clientId: process.env.DOKU_CLIENT_ID || '',

  // Secret key for Doku API
  secretKey: process.env.DOKU_SECRET_KEY || '',

  // Environment (sandbox or production)
  environment: (process.env.DOKU_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',

  // Notification URL for webhooks
  notificationUrl: process.env.DOKU_NOTIFICATION_URL || '',

  // API endpoints
  apiEndpoints: {
    sandbox: {
      checkout: 'https://api-sandbox.doku.com/checkout/v1/payment',
      directApi: 'https://api-sandbox.doku.com',
    },
    production: {
      checkout: 'https://api.doku.com/checkout/v1/payment',
      directApi: 'https://api.doku.com',
    }
  },

  // Checkout JS URLs
  checkoutJs: {
    sandbox: 'https://sandbox.doku.com/jokul-checkout-js/v1/jokul-checkout-1.0.0.js',
    production: 'https://jokul.doku.com/jokul-checkout-js/v1/jokul-checkout-1.0.0.js'
  },

  // Default currency
  currency: 'IDR',

  // Commission rate (25% for marketplace)
  commissionRate: 0.25,

  // Minimum charge amount (in IDR)
  minimumChargeAmount: 10000, // IDR 10,000

  // Maximum charge amount (in IDR)
  maximumChargeAmount: 100000000, // IDR 100,000,000

  // Payment due date in minutes (default 60 minutes)
  defaultPaymentDueDate: 60,
};

// Get current API endpoint based on environment
export function getApiEndpoint(): string {
  return dokuConfig.apiEndpoints[dokuConfig.environment].checkout;
}

// Get current checkout JS URL based on environment
export function getCheckoutJsUrl(): string {
  return dokuConfig.checkoutJs[dokuConfig.environment];
}

// Validate Doku configuration
export function validateDokuConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!dokuConfig.clientId) {
    errors.push('DOKU_CLIENT_ID is required');
  } else {
    // Validate client ID format (MCH for merchant, BRN for business)
    const clientIdPattern = /^(MCH|BRN)-\d{4}-/;
    if (!clientIdPattern.test(dokuConfig.clientId)) {
      errors.push('DOKU_CLIENT_ID must start with MCH-XXXX- (merchant) or BRN-XXXX- (business)');
    }
  }

  if (!dokuConfig.secretKey) {
    errors.push('DOKU_SECRET_KEY is required');
  }

  if (!['sandbox', 'production'].includes(dokuConfig.environment)) {
    errors.push('DOKU_ENVIRONMENT must be either "sandbox" or "production"');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Validate payment amount
export function validatePaymentAmount(amount: number): { isValid: boolean; error?: string } {
  if (amount < dokuConfig.minimumChargeAmount) {
    return {
      isValid: false,
      error: `Amount must be at least IDR ${dokuConfig.minimumChargeAmount.toLocaleString()}`
    };
  }

  if (amount > dokuConfig.maximumChargeAmount) {
    return {
      isValid: false,
      error: `Amount must not exceed IDR ${dokuConfig.maximumChargeAmount.toLocaleString()}`
    };
  }

  return { isValid: true };
}

// Calculate commission
export function calculateCommission(amount: number): number {
  return Math.round(amount * dokuConfig.commissionRate);
}

// Format price for display
export function formatPrice(amount: number, currency: string = dokuConfig.currency): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

// Subscription plans configuration (updated for Doku/IDR)
export const subscriptionPlans = {
  // Free plan
  free: {
    id: 'free',
    name: 'Free',
    description: 'Get started with basic features',
    price: 0, // Free
    interval: 'month' as const,
    features: [
      'Access to free community nodes',
      'Create up to 3 workflows',
      'Basic workflow execution (100/month)',
      'Community support',
      'Standard node library'
    ],
    limits: {
      workflows: 3,
      executions: 100,
      nodes: 'free_only',
      storage: '100MB',
      support: 'community'
    }
  },

  // Developer plans
  developer_basic: {
    id: 'developer_basic',
    name: 'Developer Basic',
    description: 'Perfect for individual developers',
    price: 150000, // IDR 150,000/month (~$10)
    interval: 'month' as const,
    features: [
      'Publish up to 5 nodes',
      'Basic analytics dashboard',
      'Email support',
      'Revenue sharing (75%)',
      'Node version management'
    ],
    limits: {
      nodes: 5,
      analytics: 'basic',
      support: 'email',
      revenueShare: 0.75
    }
  },

  developer_pro: {
    id: 'developer_pro',
    name: 'Developer Pro',
    description: 'For professional developers',
    price: 300000, // IDR 300,000/month (~$20)
    interval: 'month' as const,
    features: [
      'Publish up to 20 nodes',
      'Advanced analytics',
      'Priority support',
      'Revenue sharing (80%)',
      'Custom node categories',
      'Beta features access'
    ],
    limits: {
      nodes: 20,
      analytics: 'advanced',
      support: 'priority',
      revenueShare: 0.80,
      customCategories: true,
      betaFeatures: true
    }
  },

  // Enterprise plans
  enterprise_team: {
    id: 'enterprise_team',
    name: 'Enterprise Team',
    description: 'For development teams',
    price: 1500000, // IDR 1,500,000/month (~$100)
    interval: 'month' as const,
    features: [
      'Unlimited node publishing',
      'Team collaboration tools',
      'Advanced analytics',
      'Dedicated support',
      'Revenue sharing (85%)',
      'White-label options',
      'Custom integrations'
    ],
    limits: {
      nodes: 'unlimited',
      analytics: 'enterprise',
      support: 'dedicated',
      revenueShare: 0.85,
      teamCollaboration: true,
      whiteLabel: true,
      customIntegrations: true
    }
  },

  // User subscription plans
  user_premium: {
    id: 'user_premium',
    name: 'Premium User',
    description: 'Access to premium nodes and features',
    price: 225000, // IDR 225,000/month (~$15)
    interval: 'month' as const,
    features: [
      'Access to all premium nodes',
      'Priority customer support',
      'Advanced workflow features',
      'Unlimited workflow executions',
      'Premium templates'
    ],
    limits: {
      workflows: 'unlimited',
      executions: 'unlimited',
      nodes: 'premium',
      storage: '10GB',
      support: 'priority',
      templates: 'premium'
    }
  },

  user_enterprise: {
    id: 'user_enterprise',
    name: 'Enterprise User',
    description: 'Full access for enterprise users',
    price: 750000, // IDR 750,000/month (~$50)
    interval: 'month' as const,
    features: [
      'Everything in Premium User',
      'Access to enterprise nodes',
      'Custom integrations',
      'Dedicated account manager',
      'Advanced security features',
      'Compliance tools'
    ],
    limits: {
      workflows: 'unlimited',
      executions: 'unlimited',
      nodes: 'enterprise',
      storage: 'unlimited',
      support: 'dedicated',
      templates: 'enterprise',
      customIntegrations: true,
      advancedSecurity: true,
      compliance: true
    }
  }
};

// Node pricing tiers (in IDR)
export const nodePricingTiers = {
  free: {
    min: 0,
    max: 0,
    commission: 0
  },
  premium: {
    min: 15000, // IDR 15,000 (~$1)
    max: 750000, // IDR 750,000 (~$50)
    commission: dokuConfig.commissionRate
  },
  enterprise: {
    min: 750000, // IDR 750,000 (~$50)
    max: 7500000, // IDR 7,500,000 (~$500)
    commission: dokuConfig.commissionRate
  }
};

// Payment method types supported by Doku
export const supportedPaymentMethods = [
  'VIRTUAL_ACCOUNT_BCA',
  'VIRTUAL_ACCOUNT_BANK_MANDIRI',
  'VIRTUAL_ACCOUNT_BANK_SYARIAH_MANDIRI',
  'VIRTUAL_ACCOUNT_BRI',
  'VIRTUAL_ACCOUNT_BNI',
  'VIRTUAL_ACCOUNT_DOKU',
  'VIRTUAL_ACCOUNT_PERMATA',
  'VIRTUAL_ACCOUNT_CIMB',
  'VIRTUAL_ACCOUNT_DANAMON',
  'ONLINE_TO_OFFLINE_ALFA',
  'CREDIT_CARD',
  'QRIS',
  'EMONEY_OVO',
  'EMONEY_SHOPEE_PAY',
  'EMONEY_DOKU',
  'EMONEY_DANA',
  'DIRECT_DEBIT_BRI',
  'PEER_TO_PEER_AKULAKU',
  'PEER_TO_PEER_KREDIVO',
  'PEER_TO_PEER_INDODANA',
  'JENIUS_PAY'
] as const;

// Webhook event types
export const webhookEventTypes = [
  'payment.success',
  'payment.failed',
  'payment.pending',
  'payment.expired'
] as const;
