/**
 * Doku Signature Generation Utility
 * Handles signature generation and validation for Doku API requests
 */

import crypto from 'crypto';
import { dokuConfig } from './config';

/**
 * Generate HMAC SHA256 signature for Doku API requests
 */
export function generateSignature(
  httpMethod: string,
  endpointUrl: string,
  accessToken: string,
  requestBody: string,
  timestamp: string
): string {
  // Create the string to sign
  const stringToSign = `${httpMethod}\n${endpointUrl}\n${accessToken}\n${requestBody}\n${timestamp}`;
  
  // Generate HMAC SHA256 signature
  const signature = crypto
    .createHmac('sha256', dokuConfig.secretKey)
    .update(stringToSign)
    .digest('base64');
  
  return `HMACSHA256=${signature}`;
}

/**
 * Generate signature for request headers
 */
export function generateRequestSignature(
  clientId: string,
  requestId: string,
  timestamp: string,
  requestBody: string
): string {
  // For checkout API, the signature is based on client ID, request ID, timestamp, and body
  const stringToSign = `${clientId}${requestId}${timestamp}${requestBody}`;
  
  const signature = crypto
    .createHmac('sha256', dokuConfig.secretKey)
    .update(stringToSign)
    .digest('base64');
  
  return `HMACSHA256=${signature}`;
}

/**
 * Validate signature from Doku response
 */
export function validateResponseSignature(
  clientId: string,
  requestId: string,
  timestamp: string,
  responseBody: string,
  receivedSignature: string
): boolean {
  try {
    const expectedSignature = generateRequestSignature(clientId, requestId, timestamp, responseBody);
    return expectedSignature === receivedSignature;
  } catch (error) {
    console.error('Error validating signature:', error);
    return false;
  }
}

/**
 * Generate signature for webhook notifications
 */
export function generateWebhookSignature(
  requestBody: string,
  timestamp: string
): string {
  const stringToSign = `${timestamp}${requestBody}`;
  
  const signature = crypto
    .createHmac('sha256', dokuConfig.secretKey)
    .update(stringToSign)
    .digest('hex');
  
  return signature;
}

/**
 * Validate webhook signature
 */
export function validateWebhookSignature(
  requestBody: string,
  timestamp: string,
  receivedSignature: string
): boolean {
  try {
    const expectedSignature = generateWebhookSignature(requestBody, timestamp);
    return expectedSignature === receivedSignature;
  } catch (error) {
    console.error('Error validating webhook signature:', error);
    return false;
  }
}

/**
 * Generate request ID (UUID v4)
 */
export function generateRequestId(): string {
  return crypto.randomUUID();
}

/**
 * Generate timestamp in ISO 8601 format (UTC)
 */
export function generateTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Create request headers for Doku API
 */
export function createRequestHeaders(requestBody: string): {
  'Client-Id': string;
  'Request-Id': string;
  'Request-Timestamp': string;
  'Signature': string;
  'Content-Type': string;
} {
  const clientId = dokuConfig.clientId;
  const requestId = generateRequestId();
  const timestamp = generateTimestamp();
  const signature = generateRequestSignature(clientId, requestId, timestamp, requestBody);

  return {
    'Client-Id': clientId,
    'Request-Id': requestId,
    'Request-Timestamp': timestamp,
    'Signature': signature,
    'Content-Type': 'application/json'
  };
}

/**
 * Validate request timestamp (should be within 5 minutes)
 */
export function validateTimestamp(timestamp: string): boolean {
  try {
    const requestTime = new Date(timestamp);
    const currentTime = new Date();
    const timeDifference = Math.abs(currentTime.getTime() - requestTime.getTime());
    const fiveMinutesInMs = 5 * 60 * 1000; // 5 minutes in milliseconds
    
    return timeDifference <= fiveMinutesInMs;
  } catch (error) {
    console.error('Error validating timestamp:', error);
    return false;
  }
}

/**
 * Generate signature for QRIS notifications (different format)
 */
export function generateQrisSignature(
  issuerId: string,
  txnDate: string,
  merchantPan: string,
  invoice: string,
  sharedKey: string
): string {
  const stringToSign = `${issuerId}${txnDate}${merchantPan}${invoice}${sharedKey}`;
  
  const signature = crypto
    .createHash('sha1')
    .update(stringToSign)
    .digest('hex');
  
  return signature.toUpperCase();
}

/**
 * Validate QRIS notification signature
 */
export function validateQrisSignature(
  issuerId: string,
  txnDate: string,
  merchantPan: string,
  invoice: string,
  receivedWords: string,
  sharedKey: string
): boolean {
  try {
    const expectedSignature = generateQrisSignature(issuerId, txnDate, merchantPan, invoice, sharedKey);
    return expectedSignature === receivedWords.toUpperCase();
  } catch (error) {
    console.error('Error validating QRIS signature:', error);
    return false;
  }
}

/**
 * Hash sensitive data for logging (one-way hash)
 */
export function hashForLogging(data: string): string {
  return crypto
    .createHash('sha256')
    .update(data)
    .digest('hex')
    .substring(0, 8); // Only first 8 characters for logging
}

/**
 * Verify signature components are present
 */
export function verifySignatureComponents(headers: any): {
  isValid: boolean;
  missing: string[];
} {
  const required = ['Client-Id', 'Request-Id', 'Request-Timestamp', 'Signature'];
  const missing: string[] = [];
  
  for (const field of required) {
    if (!headers[field] && !headers[field.toLowerCase()]) {
      missing.push(field);
    }
  }
  
  return {
    isValid: missing.length === 0,
    missing
  };
}
