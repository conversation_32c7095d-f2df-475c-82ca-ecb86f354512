/**
 * Doku Payment Gateway Types
 * Type definitions for Doku payment integration
 */

// Doku API request/response types
export interface DokuCheckoutRequest {
  order: {
    amount: number;
    invoice_number: string;
    currency?: string;
    callback_url?: string;
    callback_url_cancel?: string;
    language?: string;
    auto_redirect?: boolean;
    disable_retry_payment?: boolean;
    line_items?: DokuLineItem[];
  };
  payment: {
    payment_due_date: number;
    payment_method_types?: string[];
  };
  customer?: DokuCustomer;
  shipping_address?: DokuAddress;
  billing_address?: DokuAddress;
  additional_info?: DokuAdditionalInfo;
}

export interface DokuLineItem {
  id?: string;
  name: string;
  quantity: number;
  price: number;
  sku?: string;
  category?: string;
  url?: string;
  image_url?: string;
  type?: string;
}

export interface DokuCustomer {
  id?: string;
  name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  postcode?: string;
  state?: string;
  city?: string;
  country?: string;
}

export interface DokuAddress {
  first_name?: string;
  last_name?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  phone?: string;
  country_code?: string;
}

export interface DokuAdditionalInfo {
  allow_tenor?: number[];
  close_redirect?: string;
  doku_wallet_notify_url?: string;
}

export interface DokuCheckoutResponse {
  message: string[];
  response: {
    order: {
      amount: string;
      invoice_number: string;
      currency: string;
      session_id: string;
      callback_url?: string;
      callback_url_cancel?: string;
      line_items?: DokuLineItem[];
      language?: string;
      disable_retry_payment?: boolean;
      auto_redirect?: boolean;
    };
    payment: {
      payment_method_types: string[];
      payment_due_date: number;
      token_id: string;
      url: string;
      expired_date: string;
    };
    customer?: DokuCustomer;
    additional_info?: DokuAdditionalInfo;
    uuid: number;
    headers: {
      request_id: string;
      signature: string;
      date: string;
      client_id: string;
    };
    shipping_address?: DokuAddress;
    billing_address?: DokuAddress;
  };
}

// Doku notification/webhook types
export interface DokuNotification {
  order: {
    amount: string;
    invoice_number: string;
    currency: string;
  };
  transaction: {
    id: string;
    status: 'SUCCESS' | 'FAILED' | 'PENDING' | 'EXPIRED';
    date: string;
    reference_number?: string;
  };
  virtual_account_info?: {
    virtual_account_number: string;
    how_to_pay_page: string;
    how_to_pay_api: string;
    expired_date: string;
  };
  customer?: DokuCustomer;
  signature: string;
}

// Payment service interfaces
export interface CreatePaymentIntentParams {
  nodeId: string;
  userId: string;
  amount: number; // in IDR
  currency?: string;
  metadata?: Record<string, string>;
}

export interface CreateSubscriptionParams {
  userId: string;
  planId: string;
  paymentMethodId?: string;
  trialDays?: number;
}

export interface CreateCheckoutSessionParams {
  userId: string;
  planId: string;
  successUrl: string;
  cancelUrl: string;
  trialDays?: number;
}

// Payment status types
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'expired';

// Subscription status types
export type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'past_due' | 'trialing';

// Payment method types
export type PaymentMethodType = 
  | 'VIRTUAL_ACCOUNT_BCA'
  | 'VIRTUAL_ACCOUNT_BANK_MANDIRI'
  | 'VIRTUAL_ACCOUNT_BANK_SYARIAH_MANDIRI'
  | 'VIRTUAL_ACCOUNT_BRI'
  | 'VIRTUAL_ACCOUNT_BNI'
  | 'VIRTUAL_ACCOUNT_DOKU'
  | 'VIRTUAL_ACCOUNT_PERMATA'
  | 'VIRTUAL_ACCOUNT_CIMB'
  | 'VIRTUAL_ACCOUNT_DANAMON'
  | 'ONLINE_TO_OFFLINE_ALFA'
  | 'CREDIT_CARD'
  | 'QRIS'
  | 'EMONEY_OVO'
  | 'EMONEY_SHOPEE_PAY'
  | 'EMONEY_DOKU'
  | 'EMONEY_DANA'
  | 'DIRECT_DEBIT_BRI'
  | 'PEER_TO_PEER_AKULAKU'
  | 'PEER_TO_PEER_KREDIVO'
  | 'PEER_TO_PEER_INDODANA'
  | 'JENIUS_PAY';

// Error types
export interface DokuError {
  code: string;
  message: string;
  details?: any;
}

// Marketplace configuration
export interface MarketplaceConfig {
  apiUrl: string;
  cdnUrl: string;
  paymentProvider: 'doku';
  allowedFileTypes: string[];
  maxFileSize: number;
  sandboxEnabled: boolean;
  reviewModerationEnabled: boolean;
}

// Payment history item
export interface PaymentHistoryItem {
  id: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod?: PaymentMethodType;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

// Subscription plan
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  interval: 'month' | 'year';
  features: string[];
  limits: Record<string, any>;
}

// Node purchase record
export interface NodePurchase {
  id: string;
  nodeId: string;
  userId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  transactionId?: string;
  purchasedAt: Date;
  metadata?: Record<string, any>;
}

// Webhook event
export interface WebhookEvent {
  id: string;
  type: string;
  data: any;
  createdAt: Date;
  processed: boolean;
  attempts: number;
  lastAttemptAt?: Date;
  nextAttemptAt?: Date;
}

// API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: DokuError;
  message?: string;
}

// Request headers for Doku API
export interface DokuRequestHeaders {
  'Client-Id': string;
  'Request-Id': string;
  'Request-Timestamp': string;
  'Signature': string;
  'Content-Type': string;
}

// Response headers from Doku API
export interface DokuResponseHeaders {
  'Client-Id': string;
  'Request-Id': string;
  'Response-Timestamp': string;
  'Signature': string;
}
