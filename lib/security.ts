import { NextRequest, NextResponse } from 'next/server';
import { ratelimit } from './rate-limit';

// Security headers configuration
export const securityHeaders = {
  // Prevent XSS attacks
  'X-XSS-Protection': '1; mode=block',

  // Prevent clickjacking
  'X-Frame-Options': 'DENY',

  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',

  // Referrer policy
  'Referrer-Policy': 'strict-origin-when-cross-origin',

  // Content Security Policy
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://api.stripe.com",
    "frame-src https://js.stripe.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),

  // Strict Transport Security (HTTPS only)
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',

  // Permissions Policy
  'Permissions-Policy': [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()'
  ].join(', ')
};

// Rate limiting configuration
export const rateLimitConfig = {
  // API endpoints
  '/api/auth/login': { requests: 5, window: '15m' },
  '/api/auth/register': { requests: 3, window: '15m' },
  '/api/auth/forgot-password': { requests: 3, window: '15m' },
  '/api/auth/reset-password': { requests: 3, window: '15m' },
  '/api/auth/resend-verification': { requests: 2, window: '15m' },

  // General API rate limit
  '/api/': { requests: 100, window: '15m' },

  // Workflow execution (more generous for legitimate use)
  '/api/workflows/': { requests: 50, window: '15m' },
};

// Suspicious patterns to detect potential attacks (more specific patterns)
export const suspiciousPatterns = [
  // SQL injection patterns (more specific)
  /(\bunion\s+select\b)/i,
  /(\bselect\s+.*\s+from\b)/i,
  /(\binsert\s+into\b)/i,
  /(\bupdate\s+.*\s+set\b)/i,
  /(\bdelete\s+from\b)/i,
  /(\bdrop\s+table\b)/i,

  // XSS patterns (more specific)
  /<script[^>]*>.*?<\/script>/gi,
  /javascript:\s*[^;]/gi,
  /on(load|error|click|mouseover)\s*=/gi,

  // Path traversal (more specific)
  /\.\.\/.*\.\.\/.*\.\.\//g,
  /\.\.\\.*\.\.\\.*\.\.\\/g,

  // Command injection (more specific)
  /;\s*(rm|cat|ls|pwd|whoami|id)\s/g,
  /\|\s*(rm|cat|ls|pwd|whoami|id)\s/g,
];

// Check if request contains suspicious patterns
export function containsSuspiciousContent(request: NextRequest): boolean {
  const url = request.url;
  const userAgent = request.headers.get('user-agent') || '';
  const { pathname } = request.nextUrl;

  // Exclude legitimate pages from suspicious pattern checking
  const legitimatePages = [
    '/login', '/register', '/profile', '/dashboard',
    '/workflow-canvas', '/workflow-manager', '/marketplace',
    '/api/auth/', '/api/user/', '/api/workflow', '/api/workflows/',
    '/_next/', '/favicon.ico', '/public/'
  ];

  // Skip pattern checking for legitimate pages
  if (legitimatePages.some(page => pathname.startsWith(page))) {
    return false;
  }

  // Check URL for suspicious patterns (only for non-legitimate pages)
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(url) || pattern.test(userAgent)) {
      return true;
    }
  }

  return false;
}

// Apply security headers to response
export function applySecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

// Rate limiting middleware
export async function applyRateLimit(request: NextRequest): Promise<NextResponse | null> {
  const { pathname } = request.nextUrl;
  const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'anonymous';

  // Skip rate limiting for static assets and legitimate page requests
  const skipRateLimitPaths = [
    '/_next/', '/favicon.ico', '/public/', '/login', '/register',
    '/workflow-canvas', '/workflow-manager', '/marketplace', '/profile', '/dashboard'
  ];

  if (skipRateLimitPaths.some(path => pathname.startsWith(path))) {
    return null;
  }

  // Find matching rate limit configuration
  let rateLimitKey = '';
  let config = null;

  for (const [path, pathConfig] of Object.entries(rateLimitConfig)) {
    if (pathname.startsWith(path)) {
      rateLimitKey = path;
      config = pathConfig;
      break;
    }
  }

  if (!config) return null;

  // Create rate limiter for this endpoint
  const identifier = `${clientIP}:${rateLimitKey}`;

  try {
    const { success, limit, reset, remaining } = await ratelimit.limit(identifier);

    if (!success) {
      console.warn(`Rate limit exceeded for ${clientIP} on ${pathname}`);

      const response = NextResponse.json(
        {
          error: 'Too many requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: Math.round((reset - Date.now()) / 1000)
        },
        { status: 429 }
      );

      response.headers.set('X-RateLimit-Limit', limit.toString());
      response.headers.set('X-RateLimit-Remaining', remaining.toString());
      response.headers.set('X-RateLimit-Reset', reset.toString());
      response.headers.set('Retry-After', Math.round((reset - Date.now()) / 1000).toString());

      return applySecurityHeaders(response);
    }

    // Add rate limit headers to successful requests
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', limit.toString());
    response.headers.set('X-RateLimit-Remaining', remaining.toString());
    response.headers.set('X-RateLimit-Reset', reset.toString());

    return response;
  } catch (error) {
    console.error('Rate limiting error:', error);
    return null; // Continue without rate limiting on error
  }
}

// Block suspicious requests
export function blockSuspiciousRequests(request: NextRequest): NextResponse | null {
  if (containsSuspiciousContent(request)) {
    console.warn(`Suspicious request blocked: ${request.url}`);

    const response = NextResponse.json(
      { error: 'Request blocked for security reasons' },
      { status: 403 }
    );

    return applySecurityHeaders(response);
  }

  return null;
}

// Validate request origin for sensitive operations
export function validateOrigin(request: NextRequest): boolean {
  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');
  const host = request.headers.get('host');

  // For API requests, ensure they come from the same origin
  if (request.nextUrl.pathname.startsWith('/api/')) {
    if (origin && origin !== `https://${host}` && origin !== `http://${host}`) {
      return false;
    }

    if (referer && !referer.startsWith(`https://${host}`) && !referer.startsWith(`http://${host}`)) {
      return false;
    }
  }

  return true;
}

// Main security middleware function
export async function securityMiddleware(request: NextRequest): Promise<NextResponse | null> {
  // 1. Block suspicious requests
  const suspiciousBlock = blockSuspiciousRequests(request);
  if (suspiciousBlock) return suspiciousBlock;

  // 2. Validate origin for sensitive operations
  if (!validateOrigin(request)) {
    console.warn(`Invalid origin blocked: ${request.url}`);
    const response = NextResponse.json(
      { error: 'Invalid request origin' },
      { status: 403 }
    );
    return applySecurityHeaders(response);
  }

  // 3. Apply rate limiting
  const rateLimitResponse = await applyRateLimit(request);
  if (rateLimitResponse && rateLimitResponse.status === 429) {
    return rateLimitResponse;
  }

  // 4. Continue with security headers
  const response = rateLimitResponse || NextResponse.next();
  return applySecurityHeaders(response);
}
