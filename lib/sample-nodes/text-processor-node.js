// Sample Text Processor Node for Plug & Play System
// This is the JavaScript code that would be stored in the database

const React = require('react');
const { Handle, Position } = require('reactflow');

// Node Component
const TextProcessorComponent = ({ data, selected }) => {
  return React.createElement('div', {
    className: `bg-white border-2 rounded-lg p-4 shadow-sm min-w-[200px] ${
      selected ? 'border-blue-500' : 'border-gray-200'
    }`
  }, [
    // Input Handle
    React.createElement(Handle, {
      key: 'input',
      type: 'target',
      position: Position.Left,
      style: { background: '#555' }
    }),
    
    // Node Header
    React.createElement('div', {
      key: 'header',
      className: 'flex items-center gap-2 mb-3'
    }, [
      React.createElement('div', {
        key: 'icon',
        className: 'w-8 h-8 bg-blue-100 rounded flex items-center justify-center'
      }, '📝'),
      React.createElement('div', { key: 'title' }, [
        React.createElement('h3', {
          key: 'name',
          className: 'font-semibold text-sm'
        }, 'Text Processor'),
        React.createElement('p', {
          key: 'desc',
          className: 'text-xs text-gray-500'
        }, 'Process and transform text')
      ])
    ]),
    
    // Configuration
    React.createElement('div', {
      key: 'config',
      className: 'space-y-2'
    }, [
      React.createElement('div', { key: 'operation' }, [
        React.createElement('label', {
          key: 'label',
          className: 'block text-xs font-medium text-gray-700 mb-1'
        }, 'Operation'),
        React.createElement('select', {
          key: 'select',
          className: 'w-full text-xs border rounded px-2 py-1',
          value: data.operation || 'uppercase',
          onChange: (e) => data.updateConfig?.({ operation: e.target.value })
        }, [
          React.createElement('option', { key: 'upper', value: 'uppercase' }, 'Uppercase'),
          React.createElement('option', { key: 'lower', value: 'lowercase' }, 'Lowercase'),
          React.createElement('option', { key: 'title', value: 'titlecase' }, 'Title Case'),
          React.createElement('option', { key: 'reverse', value: 'reverse' }, 'Reverse'),
          React.createElement('option', { key: 'trim', value: 'trim' }, 'Trim Whitespace')
        ])
      ])
    ]),
    
    // Status
    React.createElement('div', {
      key: 'status',
      className: 'mt-3 text-xs text-gray-500'
    }, `Status: ${data.status || 'Ready'}`),
    
    // Output Handle
    React.createElement(Handle, {
      key: 'output',
      type: 'source',
      position: Position.Right,
      style: { background: '#555' }
    })
  ]);
};

// Node Execution Function
const executeTextProcessor = async (inputs, config) => {
  const { text } = inputs;
  const { operation = 'uppercase' } = config;
  
  if (!text || typeof text !== 'string') {
    throw new Error('Invalid input: text must be a string');
  }
  
  let result;
  
  switch (operation) {
    case 'uppercase':
      result = text.toUpperCase();
      break;
    case 'lowercase':
      result = text.toLowerCase();
      break;
    case 'titlecase':
      result = text.replace(/\w\S*/g, (txt) => 
        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
      );
      break;
    case 'reverse':
      result = text.split('').reverse().join('');
      break;
    case 'trim':
      result = text.trim();
      break;
    default:
      result = text;
  }
  
  return {
    processedText: result,
    originalLength: text.length,
    processedLength: result.length,
    operation: operation,
    timestamp: new Date().toISOString()
  };
};

// Node Definition
const nodeDefinition = {
  id: 'text-processor',
  name: 'Text Processor',
  type: 'text-processor',
  category: 'text',
  description: 'Process and transform text with various operations',
  icon: '📝',
  version: '1.0.0',
  inputs: [
    {
      id: 'text',
      name: 'Text Input',
      type: 'string',
      required: true,
      description: 'The text to process'
    }
  ],
  outputs: [
    {
      id: 'processedText',
      name: 'Processed Text',
      type: 'string',
      description: 'The processed text result'
    },
    {
      id: 'metadata',
      name: 'Metadata',
      type: 'object',
      description: 'Processing metadata and statistics'
    }
  ],
  config: [
    {
      id: 'operation',
      name: 'Operation',
      type: 'select',
      required: true,
      description: 'The text processing operation to perform',
      defaultValue: 'uppercase',
      options: [
        { label: 'Uppercase', value: 'uppercase' },
        { label: 'Lowercase', value: 'lowercase' },
        { label: 'Title Case', value: 'titlecase' },
        { label: 'Reverse', value: 'reverse' },
        { label: 'Trim Whitespace', value: 'trim' }
      ]
    }
  ],
  component: TextProcessorComponent,
  execute: executeTextProcessor
};
