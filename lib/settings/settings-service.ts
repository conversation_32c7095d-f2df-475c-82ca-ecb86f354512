import { PrismaClient } from '@prisma/client';
import { AppSettings, SettingsValidationResult } from './types';
import { defaultSettings, getInitialSettings } from './default-settings';

const prisma = new PrismaClient();

export class SettingsService {
  private static instance: SettingsService;
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): SettingsService {
    if (!SettingsService.instance) {
      SettingsService.instance = new SettingsService();
    }
    return SettingsService.instance;
  }

  /**
   * Get all settings as a structured object
   */
  async getAllSettings(): Promise<AppSettings> {
    const cacheKey = 'all_settings';
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      const dbSettings = await prisma.appSettings.findMany();
      const settings = this.buildSettingsObject(dbSettings);

      this.setCache(cacheKey, settings);
      return settings;
    } catch (error) {
      console.error('Error fetching settings:', error);
      return getInitialSettings();
    }
  }

  /**
   * Get settings for a specific category
   */
  async getCategorySettings<K extends keyof AppSettings>(
    category: K
  ): Promise<AppSettings[K]> {
    const cacheKey = `category_${category}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      const dbSettings = await prisma.appSettings.findMany({
        where: { category: category as string }
      });

      const categorySettings = this.buildCategoryObject(category, dbSettings);
      this.setCache(cacheKey, categorySettings);
      return categorySettings;
    } catch (error) {
      console.error(`Error fetching ${category} settings:`, error);
      return defaultSettings[category];
    }
  }

  /**
   * Get a specific setting value
   */
  async getSetting<K extends keyof AppSettings>(
    category: K,
    key: keyof AppSettings[K]
  ): Promise<any> {
    const cacheKey = `setting_${category}_${String(key)}`;
    const cached = this.getFromCache(cacheKey);
    if (cached !== undefined) return cached;

    try {
      const setting = await prisma.appSettings.findUnique({
        where: {
          category_key: {
            category: category as string,
            key: key as string
          }
        }
      });

      const value = setting ? this.parseValue(setting.value, setting.type) :
                    (defaultSettings[category] as any)[key];

      this.setCache(cacheKey, value);
      return value;
    } catch (error) {
      console.error(`Error fetching setting ${category}.${String(key)}:`, error);
      return (defaultSettings[category] as any)[key];
    }
  }

  /**
   * Update settings for a category
   */
  async updateCategorySettings<K extends keyof AppSettings>(
    category: K,
    settings: Partial<AppSettings[K]>,
    userId?: string
  ): Promise<void> {
    try {
      const updates = Object.entries(settings).map(([key, value]) => ({
        category: category as string,
        key,
        value: this.stringifyValue(value),
        type: this.getValueType(value),
        updatedBy: userId
      }));

      await prisma.$transaction(
        updates.map(update =>
          prisma.appSettings.upsert({
            where: {
              category_key: {
                category: update.category,
                key: update.key
              }
            },
            update: {
              value: update.value,
              type: update.type,
              updatedBy: update.updatedBy,
              updatedAt: new Date()
            },
            create: {
              id: `${update.category}_${update.key}`,
              category: update.category,
              key: update.key,
              value: update.value,
              type: update.type,
              createdBy: update.updatedBy,
              updatedBy: update.updatedBy
            }
          })
        )
      );

      // Clear cache
      this.clearCacheForCategory(category);
    } catch (error) {
      console.error(`Error updating ${category} settings:`, error);
      throw new Error(`Failed to update ${category} settings`);
    }
  }

  /**
   * Update a specific setting
   */
  async updateSetting<K extends keyof AppSettings>(
    category: K,
    key: keyof AppSettings[K],
    value: any,
    userId?: string
  ): Promise<void> {
    try {
      await prisma.appSettings.upsert({
        where: {
          category_key: {
            category: category as string,
            key: key as string
          }
        },
        update: {
          value: this.stringifyValue(value),
          type: this.getValueType(value),
          updatedBy: userId,
          updatedAt: new Date()
        },
        create: {
          id: `${category}_${String(key)}`,
          category: category as string,
          key: key as string,
          value: this.stringifyValue(value),
          type: this.getValueType(value),
          createdBy: userId,
          updatedBy: userId
        }
      });

      // Clear cache
      this.clearCacheForSetting(category, key);
    } catch (error) {
      console.error(`Error updating setting ${category}.${String(key)}:`, error);
      throw new Error(`Failed to update setting ${category}.${String(key)}`);
    }
  }

  /**
   * Validate settings before saving
   */
  validateSettings<K extends keyof AppSettings>(
    category: K,
    settings: Partial<AppSettings[K]>
  ): SettingsValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Category-specific validation
    if (category === 'payments') {
      const paymentSettings = settings as Partial<AppSettings['payments']>;

      if (paymentSettings.enabled && paymentSettings.provider === 'disabled') {
        errors.push('Payment provider must be selected when payments are enabled');
      }

      if (paymentSettings.commissionRate &&
          (paymentSettings.commissionRate < 0 || paymentSettings.commissionRate > 1)) {
        errors.push('Commission rate must be between 0 and 1');
      }
    }

    if (category === 'marketplace') {
      const marketSettings = settings as Partial<AppSettings['marketplace']>;

      if (marketSettings.paidNodesEnabled && !marketSettings.enabled) {
        errors.push('Marketplace must be enabled to allow paid nodes');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Reset settings to defaults
   */
  async resetToDefaults(categories?: (keyof AppSettings)[]): Promise<void> {
    try {
      if (categories) {
        await prisma.appSettings.deleteMany({
          where: {
            category: {
              in: categories as string[]
            }
          }
        });
      } else {
        await prisma.appSettings.deleteMany();
      }

      // Clear cache
      this.clearCache();
    } catch (error) {
      console.error('Error resetting settings:', error);
      throw new Error('Failed to reset settings');
    }
  }

  // Private helper methods
  private buildSettingsObject(dbSettings: any[]): AppSettings {
    const settings = { ...getInitialSettings() };

    dbSettings.forEach(setting => {
      const category = setting.category as keyof AppSettings;
      const key = setting.key;
      const value = this.parseValue(setting.value, setting.type);

      if (settings[category] && typeof settings[category] === 'object') {
        (settings[category] as any)[key] = value;
      }
    });

    return settings;
  }

  private buildCategoryObject<K extends keyof AppSettings>(
    category: K,
    dbSettings: any[]
  ): AppSettings[K] {
    const categoryDefaults = { ...defaultSettings[category] };

    dbSettings.forEach(setting => {
      const key = setting.key;
      const value = this.parseValue(setting.value, setting.type);
      (categoryDefaults as any)[key] = value;
    });

    return categoryDefaults;
  }

  private parseValue(value: string, type: string): any {
    switch (type) {
      case 'boolean':
        return value === 'true';
      case 'number':
        return parseFloat(value);
      case 'array':
      case 'object':
        try {
          return JSON.parse(value);
        } catch {
          return null;
        }
      default:
        return value;
    }
  }

  private stringifyValue(value: any): string {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  private getValueType(value: any): string {
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') return 'number';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    return 'string';
  }

  // Cache management
  private getFromCache(key: string): any {
    const expiry = this.cacheExpiry.get(key);
    if (expiry && Date.now() > expiry) {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
      return undefined;
    }
    return this.cache.get(key);
  }

  private setCache(key: string, value: any): void {
    this.cache.set(key, value);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL);
  }

  private clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }

  private clearCacheForCategory(category: keyof AppSettings): void {
    const keysToDelete: string[] = [];

    for (const key of this.cache.keys()) {
      if (key === 'all_settings' || key === `category_${category}` ||
          key.startsWith(`setting_${category}_`)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
    });
  }

  private clearCacheForSetting(category: keyof AppSettings, key: string | number | symbol): void {
    const cacheKey = `setting_${category}_${String(key)}`;
    this.cache.delete(cacheKey);
    this.cache.delete('all_settings');
    this.cache.delete(`category_${category}`);
    this.cacheExpiry.delete(cacheKey);
  }
}
