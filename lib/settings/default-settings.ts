import { AppSettings } from './types';

export const defaultSettings: AppSettings = {
  // Payment Gateway Settings
  payments: {
    enabled: false,
    provider: 'disabled',
    dokuEnabled: false,
    testMode: true,
    currency: 'idr',
    commissionRate: 0.25,
    minimumAmount: 10000, // IDR 10,000
    maximumAmount: 100000000, // IDR 100,000,000
    // Doku specific settings
    dokuClientId: '',
    dokuSecretKey: '',
    dokuEnvironment: 'sandbox',
    dokuNotificationUrl: '',
  },

  // Billing Settings
  billing: {
    enabled: false,
    invoiceGeneration: false,
    automaticBilling: false,
    billingCycle: 'monthly',
    gracePeriodDays: 7,
    reminderDays: [7, 3, 1],
  },

  // Subscription Settings
  subscriptions: {
    enabled: false,
    allowFreePlan: true,
    allowUpgrades: false,
    allowDowngrades: false,
    prorationEnabled: false,
    trialPeriodDays: 14,
    cancelationPolicy: 'end_of_period',
  },

  // Marketplace Settings
  marketplace: {
    enabled: true,
    paidNodesEnabled: false,
    freeNodesEnabled: true,
    nodeApprovalRequired: true,
    allowNodeUploads: true,
    maxNodeSize: 10, // 10MB
    allowedFileTypes: ['.js', '.ts', '.json'],
    featuredNodesEnabled: true,
    reviewSystemEnabled: true,
    ratingSystemEnabled: true,
  },

  // Developer Settings
  developer: {
    enabled: true,
    nodePublishingEnabled: true,
    analyticsEnabled: true,
    revenueShareEnabled: false,
    maxNodesPerDeveloper: 50,
    approvalProcessEnabled: true,
    sandboxTestingEnabled: true,
  },

  // Workflow Engine Settings
  workflows: {
    // Basic Settings
    enabled: true,
    maxWorkflowsPerUser: 100,
    maxNodesPerWorkflow: 50,
    sharingEnabled: true,
    exportEnabled: true,
    importEnabled: true,

    // Execution Engine Settings
    executionTimeoutMinutes: 30,
    maxConcurrentExecutions: 5,
    defaultExecutionMode: 'optimized',
    retryAttempts: 3,
    continueOnError: false,
    debugModeEnabled: false,

    // Scheduling Settings
    schedulingEnabled: true,
    maxScheduledWorkflows: 20,
    schedulingIntervalMinutes: 1,

    // Performance Settings
    maxConcurrentNodes: 10,
    nodeExecutionTimeoutSeconds: 300, // 5 minutes
    memoryLimitMB: 512,
    cpuLimitPercent: 80,

    // Storage & Logging
    executionHistoryRetentionDays: 30,
    logLevel: 'info',
    maxLogSizeMB: 100,
    enableExecutionMetrics: true,

    // Security Settings
    sandboxEnabled: true,
    allowExternalConnections: true,
    allowFileSystemAccess: false,
    allowNetworkAccess: true,

    // Advanced Features
    webhooksEnabled: true,
    apiIntegrationEnabled: true,
    customNodeUploadEnabled: true,
    workflowTemplatesEnabled: true,
  },

  // Email System Settings
  email: {
    // Basic Configuration
    enabled: false,
    provider: 'disabled',
    fromEmail: '<EMAIL>',
    fromName: 'Workflow App',
    replyToEmail: '<EMAIL>',

    // Email Features
    verificationEnabled: false,
    notificationsEnabled: false,
    marketingEmailsEnabled: false,
    transactionalEmailsEnabled: true,
    emailTemplatesEnabled: true,

    // SMTP Settings
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpSecure: false,
    smtpUsername: '',
    smtpPassword: '',
    smtpTLS: true,

    // SendGrid Settings
    sendgridApiKey: '',
    sendgridTemplateId: '',
    sendgridWebhookEnabled: false,

    // Mailgun Settings
    mailgunApiKey: '',
    mailgunDomain: '',
    mailgunRegion: 'us',
    mailgunWebhookEnabled: false,

    // Amazon SES Settings
    sesAccessKeyId: '',
    sesSecretAccessKey: '',
    sesRegion: 'us-east-1',
    sesConfigurationSet: '',

    // Postmark Settings
    postmarkApiKey: '',
    postmarkTemplateId: '',
    postmarkWebhookEnabled: false,

    // Resend Settings
    resendApiKey: '',
    resendWebhookEnabled: false,

    // Email Delivery Settings
    maxRetryAttempts: 3,
    retryDelayMinutes: 5,
    bounceHandlingEnabled: true,
    unsubscribeHandlingEnabled: true,

    // Rate Limiting
    rateLimitEnabled: true,
    maxEmailsPerHour: 100,
    maxEmailsPerDay: 1000,

    // Email Tracking
    trackOpens: true,
    trackClicks: true,
    trackUnsubscribes: true,

    // Email Queue
    queueEnabled: true,
    queueMaxSize: 1000,
    queueProcessingInterval: 5,

    // Email Templates
    welcomeEmailEnabled: true,
    passwordResetEmailEnabled: true,
    emailVerificationEnabled: true,
    notificationEmailEnabled: true,

    // Email Security
    dkimEnabled: false,
    spfEnabled: false,
    dmarcEnabled: false,

    // Email Analytics
    analyticsEnabled: true,
    retentionDays: 90,

    // Testing & Development
    testModeEnabled: false,
    testEmailAddress: '<EMAIL>',
    logEmailsEnabled: true,
  },

  // Security Settings
  security: {
    twoFactorEnabled: false,
    passwordRequirements: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
    },
    sessionTimeoutMinutes: 480, // 8 hours
    maxLoginAttempts: 5,
    lockoutDurationMinutes: 15,
  },

  // API Settings
  api: {
    rateLimitEnabled: true,
    requestsPerMinute: 100,
    requestsPerHour: 1000,
    apiKeysEnabled: false,
    webhooksEnabled: false,
    corsEnabled: true,
    allowedOrigins: ['http://localhost:3000'],
  },

  // File Storage Settings
  storage: {
    // Provider Configuration
    provider: 'local',
    enabled: true,

    // File Upload Settings
    maxFileSize: 50, // 50MB
    maxTotalStorage: 10, // 10GB
    allowedFileTypes: ['.js', '.ts', '.json', '.md', '.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.zip'],
    blockedFileTypes: ['.exe', '.bat', '.cmd', '.scr', '.com', '.pif'],
    allowExecutableFiles: false,

    // Storage Optimization
    compressionEnabled: true,
    compressionQuality: 85,
    autoOptimizeImages: true,
    generateThumbnails: true,
    thumbnailSizes: [150, 300, 600],

    // Backup & Retention
    backupEnabled: false,
    backupFrequency: 'daily',
    retentionDays: 90,
    autoCleanupEnabled: true,

    // Security
    virusScanEnabled: false,
    encryptionEnabled: false,
    accessLoggingEnabled: true,
    publicAccessEnabled: false,

    // AWS S3 Settings
    awsRegion: 'us-east-1',
    awsBucket: '',
    awsAccessKeyId: '',
    awsSecretAccessKey: '',
    awsCloudFrontEnabled: false,
    awsCloudFrontDomain: '',

    // Google Cloud Storage Settings
    gcpProjectId: '',
    gcpBucket: '',
    gcpKeyFile: '',
    gcpRegion: 'us-central1',

    // Azure Blob Storage Settings
    azureAccountName: '',
    azureAccountKey: '',
    azureContainer: '',
    azureRegion: 'East US',

    // DigitalOcean Spaces Settings
    doSpacesKey: '',
    doSpacesSecret: '',
    doSpacesEndpoint: '',
    doSpacesBucket: '',
    doSpacesRegion: 'nyc3',

    // Cloudinary Settings
    cloudinaryCloudName: '',
    cloudinaryApiKey: '',
    cloudinaryApiSecret: '',
    cloudinaryFolder: 'uploads',
  },

  // Database Settings
  database: {
    // Connection Settings
    provider: 'sqlite',
    host: 'localhost',
    port: 5432,
    database: 'nextjs_app',
    username: '',
    password: '',
    ssl: false,

    // Connection Pool Settings
    maxConnections: 10,
    minConnections: 2,
    connectionTimeout: 30,
    idleTimeout: 600,

    // Performance Settings
    queryTimeout: 30,
    slowQueryThreshold: 1000,
    enableQueryLogging: false,
    enableSlowQueryLogging: true,
    cacheEnabled: true,
    cacheTTL: 300,

    // Backup Settings
    backupEnabled: true,
    backupFrequency: 'daily',
    backupRetentionDays: 30,
    backupCompression: true,
    backupLocation: 'local',

    // Maintenance Settings
    autoVacuumEnabled: true,
    autoAnalyzeEnabled: true,
    maintenanceWindow: '02:00-04:00',
    indexOptimizationEnabled: true,

    // Security Settings
    encryptionAtRest: false,
    encryptionInTransit: true,
    auditLoggingEnabled: false,
    accessLoggingEnabled: true,

    // Monitoring Settings
    performanceMonitoringEnabled: true,
    alertsEnabled: false,
    diskSpaceThreshold: 85,
    connectionThreshold: 80,

    // Migration Settings
    autoMigrationsEnabled: false,
    migrationTimeout: 300,
    rollbackEnabled: true,

    // Replication Settings
    replicationEnabled: false,
    readReplicaEnabled: false,
    replicationLag: 5,
  },

  // Analytics Settings
  analytics: {
    enabled: true,
    trackingEnabled: true,
    dataRetentionDays: 365,
    anonymizeData: true,
    exportEnabled: true,
    realtimeEnabled: true,
  },

  // Maintenance Settings
  maintenance: {
    maintenanceMode: false,
    maintenanceMessage: 'We are currently performing scheduled maintenance. Please check back soon.',
    allowedIPs: [],
    scheduledMaintenanceEnabled: false,
    backupScheduleEnabled: false,
  },

  // Feature Flags
  features: {
    betaFeaturesEnabled: false,
    experimentalFeaturesEnabled: false,
    debugModeEnabled: false,
    performanceMonitoringEnabled: true,
    errorReportingEnabled: true,
  },

  // User Interface & Experience Settings
  ui: {
    // Theme & Appearance
    darkModeEnabled: true,
    themeMode: 'system',
    colorScheme: 'default',
    accentColor: '#3b82f6',
    borderRadius: 'medium',
    fontFamily: 'system',
    fontSize: 'medium',

    // Layout & Navigation
    compactModeEnabled: false,
    sidebarCollapsed: false,
    sidebarPosition: 'left',
    navigationStyle: 'sidebar',
    breadcrumbsEnabled: true,
    pageTransitions: true,

    // Animations & Effects
    animationsEnabled: true,
    animationSpeed: 'normal',
    reducedMotion: false,
    hoverEffects: true,
    loadingAnimations: true,

    // Notifications & Feedback
    notificationsEnabled: true,
    notificationPosition: 'top-right',
    notificationDuration: 5,
    soundEnabled: false,
    hapticFeedback: false,

    // Content & Display
    density: 'comfortable',
    showTooltips: true,
    showKeyboardShortcuts: true,
    autoSaveEnabled: true,
    confirmDialogs: true,

    // Accessibility
    highContrastMode: false,
    focusIndicators: true,
    screenReaderOptimized: false,
    keyboardNavigationEnabled: true,

    // Localization
    language: 'en',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '24h',
    timezone: 'UTC',
    currency: 'USD',
    numberFormat: 'US',

    // Performance & Data
    lazyLoadingEnabled: true,
    imageOptimization: true,
    cacheEnabled: true,
    offlineMode: false,

    // Developer & Debug
    debugMode: false,
    showPerformanceMetrics: false,
    enableDevTools: false,

    // Customization
    customCSS: '',
    logoUrl: '',
    faviconUrl: '',
    brandColors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#8b5cf6',
    },
  },
};

// Environment-based overrides
export function getEnvironmentSettings(): Partial<AppSettings> {
  const env = process.env.NODE_ENV;

  if (env === 'development') {
    return {
      features: {
        betaFeaturesEnabled: true,
        experimentalFeaturesEnabled: true,
        debugModeEnabled: true,
        performanceMonitoringEnabled: true,
        errorReportingEnabled: true,
      },
      maintenance: {
        maintenanceMode: false,
        maintenanceMessage: 'Development mode - some features may be unstable',
        allowedIPs: ['127.0.0.1', 'localhost'],
        scheduledMaintenanceEnabled: false,
        backupScheduleEnabled: false,
      },
    };
  }

  if (env === 'production') {
    return {
      features: {
        betaFeaturesEnabled: false,
        experimentalFeaturesEnabled: false,
        debugModeEnabled: false,
        performanceMonitoringEnabled: true,
        errorReportingEnabled: true,
      },
      security: {
        twoFactorEnabled: true,
        passwordRequirements: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true,
        },
        sessionTimeoutMinutes: 240, // 4 hours in production
        maxLoginAttempts: 3,
        lockoutDurationMinutes: 30,
      },
    };
  }

  return {};
}

// Merge default settings with environment overrides
export function getInitialSettings(): AppSettings {
  const envSettings = getEnvironmentSettings();
  return {
    ...defaultSettings,
    ...envSettings,
    // Deep merge for nested objects
    features: { ...defaultSettings.features, ...envSettings.features },
    security: { ...defaultSettings.security, ...envSettings.security },
    maintenance: { ...defaultSettings.maintenance, ...envSettings.maintenance },
  };
}
