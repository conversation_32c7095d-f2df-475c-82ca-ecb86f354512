"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { AppSettings } from './types';
import { getInitialSettings } from './default-settings';

interface SettingsContextType {
  settings: AppSettings;
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
  isFeatureEnabled: (feature: string) => boolean;
  getSetting: <K extends keyof AppSettings>(
    category: K,
    key: keyof AppSettings[K]
  ) => any;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

interface SettingsProviderProps {
  children: ReactNode;
}

export function SettingsProvider({ children }: SettingsProviderProps) {
  const [settings, setSettings] = useState<AppSettings>(getInitialSettings());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      } else if (response.status === 401) {
        // User not authenticated, use default settings silently
        console.warn('User not authenticated, using default settings');
        setSettings(getInitialSettings());
      } else {
        throw new Error(`Failed to load settings: ${response.status}`);
      }
    } catch (err) {
      console.error('Error loading settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load settings');
      // Keep using default settings on error
      setSettings(getInitialSettings());
    } finally {
      setLoading(false);
    }
  };

  // Load settings only when first accessed, not immediately
  const [hasLoaded, setHasLoaded] = useState(false);

  useEffect(() => {
    // Only load if not already loaded and if we have a user session
    if (!hasLoaded) {
      loadSettings().then(() => setHasLoaded(true));
    }
  }, [hasLoaded]);

  const refreshSettings = async () => {
    await loadSettings();
  };

  const isFeatureEnabled = (feature: string): boolean => {
    const [category, key] = feature.split('.');

    if (!category || !key) {
      console.warn(`Invalid feature format: ${feature}. Use 'category.key' format.`);
      return false;
    }

    const categorySettings = settings[category as keyof AppSettings];
    if (!categorySettings || typeof categorySettings !== 'object') {
      return false;
    }

    return (categorySettings as any)[key] === true;
  };

  const getSetting = <K extends keyof AppSettings>(
    category: K,
    key: keyof AppSettings[K]
  ): any => {
    const categorySettings = settings[category];
    if (!categorySettings || typeof categorySettings !== 'object') {
      return undefined;
    }
    return (categorySettings as any)[key];
  };

  const value: SettingsContextType = {
    settings,
    loading,
    error,
    refreshSettings,
    isFeatureEnabled,
    getSetting,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}

// Convenience hooks for common settings
export function useFeatureFlag(feature: string): boolean {
  const { isFeatureEnabled } = useSettings();
  return isFeatureEnabled(feature);
}

export function usePaymentSettings() {
  const { settings } = useSettings();
  return settings.payments;
}

export function useMarketplaceSettings() {
  const { settings } = useSettings();
  return settings.marketplace;
}

export function useSubscriptionSettings() {
  const { settings } = useSettings();
  return settings.subscriptions;
}

export function useDeveloperSettings() {
  const { settings } = useSettings();
  return settings.developer;
}

export function useWorkflowSettings() {
  const { settings } = useSettings();
  return settings.workflows;
}

// HOC for feature gating components
export function withFeatureFlag<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  feature: string,
  fallback?: React.ComponentType<P> | React.ReactNode
) {
  return function FeatureGatedComponent(props: P) {
    const isEnabled = useFeatureFlag(feature);

    if (!isEnabled) {
      if (React.isValidElement(fallback)) {
        return fallback;
      }
      if (fallback && typeof fallback === 'function') {
        const FallbackComponent = fallback as React.ComponentType<P>;
        return <FallbackComponent {...props} />;
      }
      return null;
    }

    return <WrappedComponent {...props} />;
  };
}

// Component for conditional rendering based on feature flags
interface FeatureGateProps {
  feature: string;
  children: ReactNode;
  fallback?: ReactNode;
}

export function FeatureGate({ feature, children, fallback = null }: FeatureGateProps) {
  const isEnabled = useFeatureFlag(feature);
  return isEnabled ? <>{children}</> : <>{fallback}</>;
}

// Component for conditional rendering based on multiple features (AND logic)
interface MultiFeatureGateProps {
  features: string[];
  children: ReactNode;
  fallback?: ReactNode;
  mode?: 'all' | 'any'; // 'all' = AND logic, 'any' = OR logic
}

export function MultiFeatureGate({
  features,
  children,
  fallback = null,
  mode = 'all'
}: MultiFeatureGateProps) {
  const { isFeatureEnabled } = useSettings();

  const isEnabled = mode === 'all'
    ? features.every(feature => isFeatureEnabled(feature))
    : features.some(feature => isFeatureEnabled(feature));

  return isEnabled ? <>{children}</> : <>{fallback}</>;
}

// Hook for checking if payments are properly configured
export function usePaymentStatus() {
  const paymentSettings = usePaymentSettings();

  const isEnabled = paymentSettings.enabled;
  const isConfigured = paymentSettings.provider !== 'disabled' &&
                      paymentSettings.dokuEnabled;
  const isReady = isEnabled && isConfigured;

  return {
    isEnabled,
    isConfigured,
    isReady,
    provider: paymentSettings.provider,
    testMode: paymentSettings.testMode,
  };
}

// Hook for checking if subscriptions are available
export function useSubscriptionStatus() {
  const subscriptionSettings = useSubscriptionSettings();
  const { isReady: paymentsReady } = usePaymentStatus();

  const isEnabled = subscriptionSettings.enabled;
  const isReady = isEnabled && (subscriptionSettings.allowFreePlan || paymentsReady);

  return {
    isEnabled,
    isReady,
    allowFreePlan: subscriptionSettings.allowFreePlan,
    allowUpgrades: subscriptionSettings.allowUpgrades,
    allowDowngrades: subscriptionSettings.allowDowngrades,
  };
}

// Hook for checking marketplace status
export function useMarketplaceStatus() {
  const marketplaceSettings = useMarketplaceSettings();
  const { isReady: paymentsReady } = usePaymentStatus();

  const isEnabled = marketplaceSettings.enabled;
  const paidNodesAvailable = marketplaceSettings.paidNodesEnabled && paymentsReady;
  const freeNodesAvailable = marketplaceSettings.freeNodesEnabled;

  return {
    isEnabled,
    paidNodesAvailable,
    freeNodesAvailable,
    uploadsAllowed: marketplaceSettings.allowNodeUploads,
    approvalRequired: marketplaceSettings.nodeApprovalRequired,
  };
}
