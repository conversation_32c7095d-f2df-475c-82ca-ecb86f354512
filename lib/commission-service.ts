/**
 * Commission Service for Developer Earnings
 * Handles commission calculations and revenue tracking
 */

import { prisma } from '@/lib/prisma';
import { dokuConfig } from '@/lib/doku/config';

export interface CommissionCalculation {
  grossAmount: number;
  commissionRate: number;
  commissionAmount: number;
  platformFee: number;
  developerEarnings: number;
}

export interface RevenueEntry {
  userId: string;
  nodeId?: string;
  grossAmount: number;
  commissionAmount: number;
  transactionId?: string;
  type: 'node_sale' | 'subscription' | 'other';
  metadata?: Record<string, any>;
}

export class CommissionService {
  /**
   * Calculate commission for a transaction
   */
  static calculateCommission(grossAmount: number): CommissionCalculation {
    const commissionRate = dokuConfig.commissionRate; // 0.25 (25%)
    const commissionAmount = Math.round(grossAmount * commissionRate);
    const platformFee = grossAmount - commissionAmount;

    return {
      grossAmount,
      commissionRate,
      commissionAmount,
      platformFee,
      developerEarnings: commissionAmount
    };
  }

  /**
   * Record revenue for a developer
   */
  static async recordRevenue(entry: RevenueEntry): Promise<void> {
    try {
      // Create revenue record
      await prisma.revenue.create({
        data: {
          userId: entry.userId,
          nodeId: entry.nodeId || null,
          amount: entry.grossAmount,
          commission: entry.commissionAmount,
          transactionId: entry.transactionId || null,
          type: entry.type,
        }
      });

      // Update user's total earnings and available balance
      await this.updateUserBalance(entry.userId);

    } catch (error) {
      console.error('Error recording revenue:', error);
      throw new Error('Failed to record revenue');
    }
  }

  /**
   * Update user's balance calculations
   */
  static async updateUserBalance(userId: string): Promise<void> {
    try {
      // Calculate total earnings from revenue
      const revenueStats = await prisma.revenue.aggregate({
        where: { userId },
        _sum: {
          commission: true
        }
      });

      // Calculate total withdrawn (completed withdrawals)
      const withdrawnStats = await prisma.withdrawalRequest.aggregate({
        where: {
          userId,
          status: 'completed'
        },
        _sum: {
          amount: true
        }
      });

      // Calculate pending withdrawals
      const pendingStats = await prisma.withdrawalRequest.aggregate({
        where: {
          userId,
          status: {
            in: ['pending', 'processing']
          }
        },
        _sum: {
          amount: true
        }
      });

      const totalEarnings = revenueStats._sum.commission || 0;
      const totalWithdrawn = withdrawnStats._sum.amount || 0;
      const pendingWithdrawals = pendingStats._sum.amount || 0;
      const availableBalance = Math.max(0, totalEarnings - totalWithdrawn - pendingWithdrawals);

      // Update user record
      await prisma.user.update({
        where: { id: userId },
        data: {
          availableBalance,
          totalEarnings,
          totalWithdrawn
        }
      });

    } catch (error) {
      console.error('Error updating user balance:', error);
      throw new Error('Failed to update user balance');
    }
  }

  /**
   * Get user's balance summary
   */
  static async getUserBalance(userId: string): Promise<{
    availableBalance: number;
    totalEarnings: number;
    totalWithdrawn: number;
    pendingWithdrawals: number;
  }> {
    try {
      // Get user's current balance
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          availableBalance: true,
          totalEarnings: true,
          totalWithdrawn: true
        }
      });

      // Calculate pending withdrawals
      const pendingStats = await prisma.withdrawalRequest.aggregate({
        where: {
          userId,
          status: {
            in: ['pending', 'processing']
          }
        },
        _sum: {
          amount: true
        }
      });

      return {
        availableBalance: user?.availableBalance || 0,
        totalEarnings: user?.totalEarnings || 0,
        totalWithdrawn: user?.totalWithdrawn || 0,
        pendingWithdrawals: pendingStats._sum.amount || 0
      };

    } catch (error) {
      console.error('Error getting user balance:', error);
      throw new Error('Failed to get user balance');
    }
  }

  /**
   * Process node sale commission
   */
  static async processNodeSaleCommission(
    nodeId: string,
    buyerId: string,
    saleAmount: number,
    transactionId?: string
  ): Promise<void> {
    try {
      // Get node details to find the author
      const node = await prisma.nodePlugin.findUnique({
        where: { id: nodeId },
        select: { authorId: true, name: true }
      });

      if (!node) {
        throw new Error('Node not found');
      }

      // Calculate commission
      const commission = this.calculateCommission(saleAmount);

      // Record revenue for the node author
      await this.recordRevenue({
        userId: node.authorId,
        nodeId,
        grossAmount: saleAmount,
        commissionAmount: commission.commissionAmount,
        transactionId,
        type: 'node_sale',
        metadata: {
          buyerId,
          nodeName: node.name,
          saleDate: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Error processing node sale commission:', error);
      throw new Error('Failed to process node sale commission');
    }
  }

  /**
   * Format currency for Indonesian Rupiah
   */
  static formatIDR(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  /**
   * Validate withdrawal amount
   */
  static validateWithdrawalAmount(amount: number, availableBalance: number): {
    isValid: boolean;
    error?: string;
  } {
    const minAmount = 50000; // IDR 50,000
    const maxAmount = 10000000; // IDR 10,000,000

    if (amount < minAmount) {
      return {
        isValid: false,
        error: `Minimum withdrawal amount is ${this.formatIDR(minAmount)}`
      };
    }

    if (amount > maxAmount) {
      return {
        isValid: false,
        error: `Maximum withdrawal amount is ${this.formatIDR(maxAmount)}`
      };
    }

    if (amount > availableBalance) {
      return {
        isValid: false,
        error: 'Withdrawal amount exceeds available balance'
      };
    }

    return { isValid: true };
  }
}
