/**
 * Package Processor for Node Packages
 * Handles extraction and processing of uploaded node packages
 */

import JSZip from 'jszip';

export interface PackageMetadata {
  name: string;
  version: string;
  description: string;
  longDescription?: string;
  author: {
    name: string;
    email: string;
    website?: string;
  };
  category: string;
  tier: 'free' | 'premium' | 'enterprise';
  price?: number;
  tags: string[];
  dependencies: Array<{
    name: string;
    version: string;
    required: boolean;
  }>;
  permissions: string[];
  compatibility: {
    minVersion: string;
  };
  screenshots: string[];
  repositoryUrl?: string;
  documentationUrl?: string;
}

export interface ProcessedPackage {
  metadata: PackageMetadata;
  nodeCode: string;
  componentCode: string;
  executionCode: string;
  icon?: string;
  screenshots: string[];
  readme?: string;
}

export class PackageProcessor {
  /**
   * Process an uploaded package file
   */
  static async processPackage(packageFile: File): Promise<ProcessedPackage> {
    try {
      const zip = new JSZip();
      const zipContent = await zip.loadAsync(packageFile);

      // Extract package.json
      const packageJsonFile = zipContent.file('package.json');
      if (!packageJsonFile) {
        throw new Error('package.json not found in package');
      }

      const packageJsonContent = await packageJsonFile.async('text');
      const packageJson = JSON.parse(packageJsonContent);

      // Extract metadata from package.json
      const metadata = this.extractMetadata(packageJson);

      // Extract main files
      const indexFile = zipContent.file('index.js');
      const componentFile = zipContent.file('component.jsx');
      const executionFile = zipContent.file('execution.js');
      const iconFile = zipContent.file('icon.svg');
      const readmeFile = zipContent.file('README.md');

      if (!indexFile) {
        throw new Error('index.js not found in package');
      }

      const indexCode = await indexFile.async('text');
      const componentCode = componentFile ? await componentFile.async('text') : '';
      const executionCode = executionFile ? await executionFile.async('text') : '';
      const icon = iconFile ? await iconFile.async('text') : undefined;
      const readme = readmeFile ? await readmeFile.async('text') : undefined;

      // Generate compatible node code
      const nodeCode = this.generateNodeCode(metadata, indexCode, componentCode, executionCode);

      // Extract screenshots (if any)
      const screenshots: string[] = [];
      const screenshotFolder = zipContent.folder('screenshots');
      if (screenshotFolder) {
        screenshotFolder.forEach((relativePath, file) => {
          if (file && !file.dir && relativePath.match(/\.(png|jpg|jpeg|gif)$/i)) {
            screenshots.push(relativePath);
          }
        });
      }

      return {
        metadata,
        nodeCode,
        componentCode,
        executionCode,
        icon,
        screenshots,
        readme
      };

    } catch (error) {
      console.error('Package processing error:', error);
      throw new Error(`Failed to process package: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract metadata from package.json
   */
  private static extractMetadata(packageJson: any): PackageMetadata {
    const nodePlugin = packageJson.nodePlugin || {};
    
    return {
      name: nodePlugin.name || packageJson.name,
      version: nodePlugin.version || packageJson.version,
      description: nodePlugin.description || packageJson.description,
      longDescription: nodePlugin.longDescription,
      author: {
        name: packageJson.author?.name || packageJson.author || 'Unknown',
        email: packageJson.author?.email || '',
        website: packageJson.author?.website || packageJson.homepage
      },
      category: nodePlugin.category || 'utility',
      tier: nodePlugin.tier || 'free',
      price: nodePlugin.price || 0,
      tags: nodePlugin.tags || packageJson.keywords || [],
      dependencies: nodePlugin.dependencies || [],
      permissions: nodePlugin.permissions || [],
      compatibility: nodePlugin.compatibility || { minVersion: '1.0.0' },
      screenshots: nodePlugin.screenshots || [],
      repositoryUrl: packageJson.repository?.url || packageJson.repository,
      documentationUrl: nodePlugin.documentationUrl || packageJson.homepage
    };
  }

  /**
   * Generate compatible node code for our system
   */
  private static generateNodeCode(
    metadata: PackageMetadata, 
    indexCode: string, 
    componentCode: string, 
    executionCode: string
  ): string {
    return `
// Node Package: ${metadata.name}
// Version: ${metadata.version}
// Generated from uploaded package

// Original package code (wrapped for compatibility)
${indexCode}

// Component code
${componentCode}

// Execution code  
${executionCode}

// Node definition for our system
const nodeDefinition = {
  id: '${metadata.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}',
  name: '${metadata.name}',
  description: '${metadata.description}',
  category: '${metadata.category}',
  version: '${metadata.version}',
  type: '${metadata.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}',
  inputs: [
    {
      id: 'input',
      name: 'Input',
      type: 'any',
      required: false,
      description: 'Input data'
    }
  ],
  outputs: [
    {
      id: 'result',
      name: 'Result',
      type: 'any',
      description: 'Processing result'
    },
    {
      id: 'data',
      name: 'Data',
      type: 'any',
      description: 'Output data'
    }
  ],
  execute: async (inputs, config, context) => {
    try {
      // Use the execution logic from the package
      if (typeof execution !== 'undefined' && execution.execute) {
        return await execution.execute(inputs, config, context);
      } else if (typeof mathCalculatorExecution !== 'undefined' && mathCalculatorExecution.execute) {
        return await mathCalculatorExecution.execute(inputs, config, context);
      } else {
        // Fallback execution
        context.log('${metadata.name}: Using fallback execution');
        return {
          success: true,
          data: { processed: true, inputs, config },
          outputs: { result: inputs, data: inputs },
          metadata: {
            executionTime: Date.now(),
            nodeType: '${metadata.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}',
            version: '${metadata.version}'
          }
        };
      }
    } catch (error) {
      context.log('${metadata.name}: Execution failed: ' + error.message);
      return {
        success: false,
        error: error.message,
        data: null,
        outputs: { result: null, data: null },
        metadata: {
          executionTime: Date.now(),
          nodeType: '${metadata.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}',
          version: '${metadata.version}',
          error: true
        }
      };
    }
  },
  metadata: {
    type: '${metadata.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}',
    label: '${metadata.name}',
    description: '${metadata.description}',
    category: '${metadata.category}',
    version: '${metadata.version}',
    icon: 'Package',
    needsOnChangeHandler: true,
    isDynamic: true,
    defaultWidth: 280,
    tags: ${JSON.stringify(metadata.tags)}
  }
};

// Export for Web Worker
if (typeof self !== 'undefined') {
  self.postMessage({ type: 'definition', data: nodeDefinition });
}
`;
  }

  /**
   * Validate package structure
   */
  static async validatePackage(packageFile: File): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      const zip = new JSZip();
      const zipContent = await zip.loadAsync(packageFile);

      // Check required files
      if (!zipContent.file('package.json')) {
        errors.push('package.json is required');
      }

      if (!zipContent.file('index.js')) {
        errors.push('index.js is required');
      }

      // Validate package.json if it exists
      const packageJsonFile = zipContent.file('package.json');
      if (packageJsonFile) {
        try {
          const packageJsonContent = await packageJsonFile.async('text');
          const packageJson = JSON.parse(packageJsonContent);

          if (!packageJson.name) {
            errors.push('package.json must have a name field');
          }

          if (!packageJson.version) {
            errors.push('package.json must have a version field');
          }

          if (!packageJson.description) {
            errors.push('package.json must have a description field');
          }
        } catch (e) {
          errors.push('package.json is not valid JSON');
        }
      }

    } catch (error) {
      errors.push('Invalid ZIP file or corrupted package');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
