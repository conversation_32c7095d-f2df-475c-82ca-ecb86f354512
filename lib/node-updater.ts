import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface NodeUpdateInfo {
  nodeId: string;
  currentVersion: string;
  latestVersion: string;
  hasUpdate: boolean;
  updateType: 'major' | 'minor' | 'patch';
  changelog?: string;
  breaking: boolean;
}

export interface UpdateResult {
  success: boolean;
  nodeId: string;
  oldVersion: string;
  newVersion: string;
  error?: string;
}

export class NodeUpdater {
  private static instance: NodeUpdater;

  static getInstance(): NodeUpdater {
    if (!NodeUpdater.instance) {
      NodeUpdater.instance = new NodeUpdater();
    }
    return NodeUpdater.instance;
  }

  /**
   * Check for updates for all installed nodes for a user
   */
  async checkForUpdates(userId: string): Promise<NodeUpdateInfo[]> {
    try {
      // Get all installed nodes for the user
      const installedNodes = await prisma.installedNode.findMany({
        where: { userId },
        include: {
          node: {
            include: {
              nodeCodes: {
                orderBy: { createdAt: 'desc' },
                take: 1
              }
            }
          }
        }
      });

      const updateInfos: NodeUpdateInfo[] = [];

      for (const installation of installedNodes) {
        const currentVersion = installation.version;
        const latestCode = installation.node.nodeCodes[0];
        const latestVersion = latestCode?.version || currentVersion;

        const hasUpdate = this.compareVersions(latestVersion, currentVersion) > 0;

        if (hasUpdate) {
          const updateType = this.getUpdateType(currentVersion, latestVersion);
          const breaking = updateType === 'major';

          updateInfos.push({
            nodeId: installation.nodeId,
            currentVersion,
            latestVersion,
            hasUpdate: true,
            updateType,
            changelog: undefined, // Changelog is stored in NodePlugin, not NodeCode
            breaking
          });
        }
      }

      return updateInfos;
    } catch (error) {
      console.error('Error checking for updates:', error);
      return [];
    }
  }

  /**
   * Update a specific node to the latest version
   */
  async updateNode(userId: string, nodeId: string): Promise<UpdateResult> {
    try {
      // Get the current installation
      const installation = await prisma.installedNode.findFirst({
        where: { userId, nodeId },
        include: {
          node: {
            include: {
              nodeCodes: {
                orderBy: { createdAt: 'desc' },
                take: 1
              }
            }
          }
        }
      });

      if (!installation) {
        return {
          success: false,
          nodeId,
          oldVersion: '',
          newVersion: '',
          error: 'Node not installed'
        };
      }

      const oldVersion = installation.version;
      const latestCode = installation.node.nodeCodes[0];
      const newVersion = latestCode?.version || oldVersion;

      if (this.compareVersions(newVersion, oldVersion) <= 0) {
        return {
          success: false,
          nodeId,
          oldVersion,
          newVersion,
          error: 'No update available'
        };
      }

      // Update the installation
      await prisma.installedNode.update({
        where: { id: installation.id },
        data: {
          version: newVersion,
          status: 'installed',
          updatedAt: new Date()
        }
      });

      return {
        success: true,
        nodeId,
        oldVersion,
        newVersion
      };

    } catch (error) {
      console.error('Error updating node:', error);
      return {
        success: false,
        nodeId,
        oldVersion: '',
        newVersion: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Update all nodes for a user
   */
  async updateAllNodes(userId: string): Promise<UpdateResult[]> {
    const updateInfos = await this.checkForUpdates(userId);
    const results: UpdateResult[] = [];

    for (const updateInfo of updateInfos) {
      if (updateInfo.hasUpdate && !updateInfo.breaking) {
        // Only auto-update non-breaking changes
        const result = await this.updateNode(userId, updateInfo.nodeId);
        results.push(result);
      }
    }

    return results;
  }

  /**
   * Compare two semantic versions
   * Returns: 1 if v1 > v2, -1 if v1 < v2, 0 if equal
   */
  private compareVersions(v1: string, v2: string): number {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);

    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const part1 = parts1[i] || 0;
      const part2 = parts2[i] || 0;

      if (part1 > part2) return 1;
      if (part1 < part2) return -1;
    }

    return 0;
  }

  /**
   * Determine the type of update (major, minor, patch)
   */
  private getUpdateType(oldVersion: string, newVersion: string): 'major' | 'minor' | 'patch' {
    const oldParts = oldVersion.split('.').map(Number);
    const newParts = newVersion.split('.').map(Number);

    if ((newParts[0] || 0) > (oldParts[0] || 0)) return 'major';
    if ((newParts[1] || 0) > (oldParts[1] || 0)) return 'minor';
    return 'patch';
  }

  /**
   * Get update statistics for a user
   */
  async getUpdateStats(userId: string): Promise<{
    totalInstalled: number;
    hasUpdates: number;
    breakingUpdates: number;
    safeUpdates: number;
  }> {
    const updateInfos = await this.checkForUpdates(userId);
    const installedNodes = await prisma.installedNode.count({
      where: { userId }
    });

    const hasUpdates = updateInfos.filter(info => info.hasUpdate).length;
    const breakingUpdates = updateInfos.filter(info => info.breaking).length;
    const safeUpdates = hasUpdates - breakingUpdates;

    return {
      totalInstalled: installedNodes,
      hasUpdates,
      breakingUpdates,
      safeUpdates
    };
  }
}

export const nodeUpdater = NodeUpdater.getInstance();
