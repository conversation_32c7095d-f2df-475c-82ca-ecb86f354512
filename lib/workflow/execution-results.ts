/**
 * Enhanced Execution Results System
 * Handles result preview, export, and formatting
 */

import { NodeExecutionResult, WorkflowExecutionStatus } from './execution-engine';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface ExecutionResultSummary {
  executionId: string;
  workflowId: string;
  workflowName: string;
  status: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // milliseconds
  totalNodes: number;
  successfulNodes: number;
  failedNodes: number;
  outputData: any[];
  logs: any[];
  error?: string;
}

export interface ResultExportOptions {
  format: 'json' | 'csv' | 'excel' | 'pdf';
  includeMetadata: boolean;
  includeLogs: boolean;
  includeErrors: boolean;
  dateFormat: string;
  timezone: string;
}

export interface TablePreviewData {
  columns: Array<{
    key: string;
    label: string;
    type: 'string' | 'number' | 'date' | 'boolean' | 'object';
  }>;
  rows: Record<string, any>[];
  totalRows: number;
  pagination: {
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

export class ExecutionResultsManager {
  private static instance: ExecutionResultsManager;

  static getInstance(): ExecutionResultsManager {
    if (!ExecutionResultsManager.instance) {
      ExecutionResultsManager.instance = new ExecutionResultsManager();
    }
    return ExecutionResultsManager.instance;
  }

  /**
   * Get execution results summary
   */
  async getExecutionSummary(executionId: string): Promise<ExecutionResultSummary | null> {
    try {
      const execution = await prisma.workflowExecution.findUnique({
        where: { id: executionId },
        include: {
          workflow: {
            select: { name: true }
          }
        }
      });

      if (!execution) {
        return null;
      }

      const results = execution.results ? JSON.parse(execution.results) : {};
      const logs = execution.logs ? JSON.parse(execution.logs) : [];
      
      // Extract output data from results
      const outputData = this.extractOutputData(results);
      
      // Calculate metrics
      const totalNodes = Object.keys(results).length;
      const successfulNodes = Object.values(results).filter((r: any) => r.success).length;
      const failedNodes = totalNodes - successfulNodes;
      
      const duration = execution.endTime 
        ? execution.endTime.getTime() - execution.startTime.getTime()
        : Date.now() - execution.startTime.getTime();

      return {
        executionId: execution.id,
        workflowId: execution.workflowId,
        workflowName: execution.workflow?.name || 'Unknown Workflow',
        status: execution.status,
        startTime: execution.startTime,
        endTime: execution.endTime || undefined,
        duration,
        totalNodes,
        successfulNodes,
        failedNodes,
        outputData,
        logs,
        error: execution.error || undefined
      };
    } catch (error) {
      console.error('Error getting execution summary:', error);
      return null;
    }
  }

  /**
   * Extract structured output data from execution results
   */
  private extractOutputData(results: Record<string, NodeExecutionResult>): any[] {
    const outputData: any[] = [];

    Object.entries(results).forEach(([nodeId, result]) => {
      if (result.success && result.outputs) {
        // Handle different output types
        Object.entries(result.outputs).forEach(([outputKey, outputValue]) => {
          if (Array.isArray(outputValue)) {
            // Array data - add each item
            outputValue.forEach((item, index) => {
              outputData.push({
                nodeId,
                outputKey,
                index,
                data: item,
                type: typeof item,
                timestamp: new Date()
              });
            });
          } else if (typeof outputValue === 'object' && outputValue !== null) {
            // Object data
            outputData.push({
              nodeId,
              outputKey,
              data: outputValue,
              type: 'object',
              timestamp: new Date()
            });
          } else {
            // Primitive data
            outputData.push({
              nodeId,
              outputKey,
              data: outputValue,
              type: typeof outputValue,
              timestamp: new Date()
            });
          }
        });
      }
    });

    return outputData;
  }

  /**
   * Get table preview of execution results
   */
  async getTablePreview(
    executionId: string, 
    page: number = 1, 
    pageSize: number = 50
  ): Promise<TablePreviewData | null> {
    try {
      const summary = await this.getExecutionSummary(executionId);
      if (!summary) {
        return null;
      }

      const { outputData } = summary;
      
      if (outputData.length === 0) {
        return {
          columns: [],
          rows: [],
          totalRows: 0,
          pagination: { page: 1, pageSize, totalPages: 0 }
        };
      }

      // Determine columns from data structure
      const columns = this.inferColumns(outputData);
      
      // Paginate data
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = outputData.slice(startIndex, endIndex);
      
      // Convert to table rows
      const rows = paginatedData.map((item, index) => ({
        _id: startIndex + index + 1,
        nodeId: item.nodeId,
        outputKey: item.outputKey,
        data: this.formatDataForTable(item.data),
        type: item.type,
        timestamp: item.timestamp
      }));

      return {
        columns,
        rows,
        totalRows: outputData.length,
        pagination: {
          page,
          pageSize,
          totalPages: Math.ceil(outputData.length / pageSize)
        }
      };
    } catch (error) {
      console.error('Error getting table preview:', error);
      return null;
    }
  }

  /**
   * Infer table columns from data
   */
  private inferColumns(data: any[]): Array<{ key: string; label: string; type: 'string' | 'number' | 'date' | 'boolean' | 'object' }> {
    const columns = [
      { key: '_id', label: '#', type: 'number' as const },
      { key: 'nodeId', label: 'Node ID', type: 'string' as const },
      { key: 'outputKey', label: 'Output', type: 'string' as const },
      { key: 'data', label: 'Data', type: 'string' as const },
      { key: 'type', label: 'Type', type: 'string' as const },
      { key: 'timestamp', label: 'Timestamp', type: 'date' as const }
    ];

    // If data has consistent structure, add specific columns
    if (data.length > 0 && data[0].data && typeof data[0].data === 'object') {
      const sampleData = data[0].data;
      if (!Array.isArray(sampleData)) {
        Object.keys(sampleData).forEach(key => {
          const dataType = typeof sampleData[key];
          let columnType: 'string' | 'number' | 'date' | 'boolean' | 'object' = 'string';

          if (dataType === 'number') {
            columnType = 'number';
          } else {
            columnType = 'string'; // Simplify to string for now
          }

          columns.push({
            key: `data.${key}`,
            label: key,
            type: columnType
          });
        });
      }
    }

    return columns;
  }

  /**
   * Format data for table display
   */
  private formatDataForTable(data: any): string {
    if (data === null || data === undefined) {
      return '';
    }
    
    if (typeof data === 'object') {
      return JSON.stringify(data, null, 2);
    }
    
    return String(data);
  }

  /**
   * Export execution results
   */
  async exportResults(
    executionId: string, 
    options: ResultExportOptions
  ): Promise<{ data: any; filename: string; mimeType: string }> {
    const summary = await this.getExecutionSummary(executionId);
    if (!summary) {
      throw new Error('Execution not found');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const baseFilename = `workflow-execution-${executionId}-${timestamp}`;

    switch (options.format) {
      case 'json':
        return this.exportAsJSON(summary, options, baseFilename);
      case 'csv':
        return this.exportAsCSV(summary, options, baseFilename);
      case 'excel':
        return this.exportAsExcel(summary, options, baseFilename);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Export as JSON
   */
  private exportAsJSON(
    summary: ExecutionResultSummary, 
    options: ResultExportOptions, 
    baseFilename: string
  ) {
    const exportData: any = {
      execution: {
        id: summary.executionId,
        workflowId: summary.workflowId,
        workflowName: summary.workflowName,
        status: summary.status,
        startTime: summary.startTime,
        endTime: summary.endTime,
        duration: summary.duration
      },
      results: summary.outputData
    };

    if (options.includeMetadata) {
      exportData.metadata = {
        totalNodes: summary.totalNodes,
        successfulNodes: summary.successfulNodes,
        failedNodes: summary.failedNodes,
        exportedAt: new Date(),
        exportOptions: options
      };
    }

    if (options.includeLogs) {
      exportData.logs = summary.logs;
    }

    if (options.includeErrors && summary.error) {
      exportData.error = summary.error;
    }

    return {
      data: JSON.stringify(exportData, null, 2),
      filename: `${baseFilename}.json`,
      mimeType: 'application/json'
    };
  }

  /**
   * Export as CSV
   */
  private exportAsCSV(
    summary: ExecutionResultSummary, 
    options: ResultExportOptions, 
    baseFilename: string
  ) {
    const { outputData } = summary;
    
    if (outputData.length === 0) {
      return {
        data: 'No data to export',
        filename: `${baseFilename}.csv`,
        mimeType: 'text/csv'
      };
    }

    // Create CSV headers
    const headers = ['Node ID', 'Output Key', 'Data', 'Type', 'Timestamp'];
    
    // Create CSV rows
    const rows = outputData.map(item => [
      item.nodeId,
      item.outputKey,
      this.formatDataForCSV(item.data),
      item.type,
      item.timestamp.toISOString()
    ]);

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
      .join('\n');

    return {
      data: csvContent,
      filename: `${baseFilename}.csv`,
      mimeType: 'text/csv'
    };
  }

  /**
   * Format data for CSV export
   */
  private formatDataForCSV(data: any): string {
    if (data === null || data === undefined) {
      return '';
    }
    
    if (typeof data === 'object') {
      return JSON.stringify(data);
    }
    
    return String(data);
  }

  /**
   * Export as Excel (placeholder - would need xlsx library)
   */
  private exportAsExcel(
    summary: ExecutionResultSummary, 
    options: ResultExportOptions, 
    baseFilename: string
  ) {
    // For now, return CSV format
    // In production, implement with xlsx library
    const csvExport = this.exportAsCSV(summary, options, baseFilename);
    return {
      ...csvExport,
      filename: `${baseFilename}.xlsx`,
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    };
  }

  /**
   * Get execution history for a workflow
   */
  async getExecutionHistory(
    workflowId: string, 
    limit: number = 50, 
    offset: number = 0
  ): Promise<ExecutionResultSummary[]> {
    try {
      const executions = await prisma.workflowExecution.findMany({
        where: { workflowId },
        include: {
          workflow: {
            select: { name: true }
          }
        },
        orderBy: { startTime: 'desc' },
        take: limit,
        skip: offset
      });

      const summaries = await Promise.all(
        executions.map(async (execution) => {
          const summary = await this.getExecutionSummary(execution.id);
          return summary!;
        })
      );

      return summaries.filter(Boolean);
    } catch (error) {
      console.error('Error getting execution history:', error);
      return [];
    }
  }
}
