/**
 * Execution Interface for Workflow Nodes
 * Bridges UI nodes with the execution engine
 */

import { NodeInput, NodeOutput, NodeConfig } from '@/lib/node-loader';
import { ExecutionContext } from './execution-engine';

/**
 * Node execution definition that defines how a node processes data
 */
export interface NodeExecutionDefinition {
  /**
   * Main execution function that processes inputs and returns outputs
   */
  execute: (
    inputs: Record<string, any>, 
    config: any, 
    context: ExecutionContext
  ) => Promise<Record<string, any>>;
  
  /**
   * Input schema definition
   */
  inputs: NodeInput[];
  
  /**
   * Output schema definition
   */
  outputs: NodeOutput[];
  
  /**
   * Configuration schema (optional)
   */
  config?: NodeConfig[];
  
  /**
   * Execution timeout in milliseconds (optional)
   */
  timeout?: number;
  
  /**
   * Whether this node can be retried on failure (optional)
   */
  retryable?: boolean;
  
  /**
   * Maximum retry attempts (optional)
   */
  maxRetries?: number;
}

/**
 * Extended node metadata that includes execution capabilities
 */
export interface ExecutableNodeMetadata {
  /** Standard node metadata */
  type: string;
  label: string;
  description: string;
  category: string;
  
  /** Execution definition */
  execution: NodeExecutionDefinition;
  
  /** Whether this node is executable */
  isExecutable: boolean;
  
  /** Execution priority (higher = executed first when possible) */
  executionPriority?: number;
}

/**
 * Execution context for individual nodes
 */
export interface NodeExecutionContext extends ExecutionContext {
  /** Current node ID */
  nodeId: string;

  /** Node type */
  nodeType: string;

  /** Node configuration data */
  nodeData: any;

  /** Progress reporting function */
  reportProgress?: (progress: number) => void;

  /** Check if execution should be cancelled */
  isCancelled?: () => boolean;
}

/**
 * Result of node execution
 */
export interface NodeExecutionOutput {
  /** Whether execution was successful */
  success: boolean;
  
  /** Output data */
  outputs: Record<string, any>;
  
  /** Error message if failed */
  error?: string;
  
  /** Execution time in milliseconds */
  executionTime: number;
  
  /** Execution logs */
  logs: string[];
  
  /** Additional metadata */
  metadata?: Record<string, any>;
  
  /** Progress percentage (0-100) */
  progress?: number;
}

/**
 * Standard execution helpers
 */
export class ExecutionHelpers {
  /**
   * Validate inputs against schema
   */
  static validateInputs(
    inputs: Record<string, any>, 
    schema: NodeInput[]
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    for (const input of schema) {
      if (input.required && (inputs[input.id] === undefined || inputs[input.id] === null)) {
        errors.push(`Required input '${input.name}' is missing`);
      }
      
      if (inputs[input.id] !== undefined) {
        const value = inputs[input.id];
        const isValidType = this.validateType(value, input.type);
        
        if (!isValidType) {
          errors.push(`Input '${input.name}' has invalid type. Expected ${input.type}`);
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Validate output against schema
   */
  static validateOutputs(
    outputs: Record<string, any>, 
    schema: NodeOutput[]
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    for (const output of schema) {
      if (outputs[output.id] !== undefined) {
        const value = outputs[output.id];
        const isValidType = this.validateType(value, output.type);
        
        if (!isValidType) {
          errors.push(`Output '${output.name}' has invalid type. Expected ${output.type}`);
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Validate value type
   */
  private static validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'array':
        return Array.isArray(value);
      case 'file':
        return typeof value === 'string' || (typeof value === 'object' && value.type === 'file');
      default:
        return true; // Unknown types are considered valid
    }
  }
  
  /**
   * Create safe execution environment
   */
  static createSafeExecutionEnvironment(context: NodeExecutionContext) {
    return {
      // Safe context properties
      nodeId: context.nodeId,
      nodeType: context.nodeType,
      workflowId: context.workflowId,
      userId: context.userId,
      
      // Safe utility functions
      log: context.log,
      reportProgress: context.reportProgress,
      isCancelled: context.isCancelled,
      
      // Safe data access
      variables: { ...context.variables },
      settings: { ...context.settings },
      
      // Utility functions
      JSON: {
        parse: JSON.parse,
        stringify: JSON.stringify
      },
      
      Math: Math,
      Date: Date,
      
      // String utilities
      String: String,
      Number: Number,
      Boolean: Boolean,
      Array: Array,
      Object: Object
    };
  }
  
  /**
   * Handle execution errors gracefully
   */
  static handleExecutionError(error: any, context: NodeExecutionContext): NodeExecutionOutput {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    context.log(`Execution failed: ${errorMessage}`, 'error');
    
    return {
      success: false,
      outputs: {},
      error: errorMessage,
      executionTime: 0,
      logs: [`Error: ${errorMessage}`],
      metadata: {
        errorType: error.constructor.name,
        stack: error.stack
      }
    };
  }
  
  /**
   * Create successful execution result
   */
  static createSuccessResult(
    outputs: Record<string, any>,
    executionTime: number,
    logs: string[] = [],
    metadata?: Record<string, any>
  ): NodeExecutionOutput {
    return {
      success: true,
      outputs,
      executionTime,
      logs,
      metadata,
      progress: 100
    };
  }
}

/**
 * Base class for executable nodes
 */
export abstract class ExecutableNode {
  abstract readonly metadata: ExecutableNodeMetadata;
  
  /**
   * Execute the node with proper error handling and validation
   */
  async execute(
    inputs: Record<string, any>,
    config: any,
    context: NodeExecutionContext
  ): Promise<NodeExecutionOutput> {
    const startTime = Date.now();
    
    try {
      // Validate inputs
      const inputValidation = ExecutionHelpers.validateInputs(inputs, this.metadata.execution.inputs);
      if (!inputValidation.valid) {
        throw new Error(`Input validation failed: ${inputValidation.errors.join(', ')}`);
      }
      
      // Execute the node
      const outputs = await this.metadata.execution.execute(inputs, config, context);
      
      // Validate outputs
      const outputValidation = ExecutionHelpers.validateOutputs(outputs, this.metadata.execution.outputs);
      if (!outputValidation.valid) {
        context.log(`Output validation warnings: ${outputValidation.errors.join(', ')}`, 'warn');
      }
      
      const executionTime = Date.now() - startTime;
      
      return ExecutionHelpers.createSuccessResult(
        outputs,
        executionTime,
        [`Node executed successfully in ${executionTime}ms`]
      );
      
    } catch (error) {
      return ExecutionHelpers.handleExecutionError(error, context);
    }
  }
}
