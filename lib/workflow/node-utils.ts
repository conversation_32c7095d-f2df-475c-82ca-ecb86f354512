import { Node } from 'reactflow';
import { NodeData, NodeMetadata } from './node-interface';
import { nodeRegistry } from './node-registry';

/**
 * Generate a unique node ID
 */
export function generateNodeId(type: string): string {
  return `${type}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Create default node data based on node type
 */
export function createDefaultNodeData(
  type: string,
  handlers?: {
    onChange?: (value: string) => void;
    onRawDataChange?: (value: string) => void;
    onJsonChange?: (value: string) => void;
  }
): NodeData {
  const metadata = nodeRegistry.getNodeMetadata(type);
  const label = metadata?.label || type;

  const nodeData: NodeData = {
    label,
    value: '',
    inputValue: '',
    fileContent: ''
  };

  // Add handlers if the node needs them
  if (metadata?.needsOnChangeHandler && handlers) {
    if (handlers.onChange) nodeData.onChange = handlers.onChange;
    if (handlers.onRawDataChange) nodeData.onRawDataChange = handlers.onRawDataChange;
    if (handlers.onJsonChange) nodeData.onJsonChange = handlers.onJsonChange;
  }

  return nodeData;
}

/**
 * Create a new node with proper positioning and data
 */
export function createNewNode(
  type: string,
  position: { x: number; y: number },
  handlers?: {
    onChange?: (value: string) => void;
    onRawDataChange?: (value: string) => void;
    onJsonChange?: (value: string) => void;
  },
  customId?: string
): Node {
  const id = customId || generateNodeId(type);
  const data = createDefaultNodeData(type, handlers);

  return {
    id,
    type,
    position,
    data
  };
}

/**
 * Generate random position for new nodes
 */
export function generateRandomPosition(): { x: number; y: number } {
  return {
    x: Math.random() * 300 + 50,
    y: Math.random() * 300 + 50
  };
}

/**
 * Validate node data structure
 */
export function validateNodeData(data: any): data is NodeData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof data.label === 'string'
  );
}

/**
 * Process and validate nodes from saved workflow data
 */
export function processWorkflowNodes(
  savedNodes: any[],
  handlers: {
    onChange: (value: string, nodeId: string) => void;
    onRawDataChange: (value: string, nodeId: string) => void;
    onJsonChange: (value: string, nodeId: string) => void;
  }
): Node[] {
  if (!Array.isArray(savedNodes)) {
    console.warn('Invalid nodes data: not an array');
    return [];
  }

  return savedNodes.map((node, index) => {
    try {
      // Validate basic node structure
      if (!node || typeof node !== 'object') {
        throw new Error('Invalid node object');
      }

      const { id, type, position, data } = node;

      // Validate required fields
      if (!id || !type || !position) {
        throw new Error('Missing required node fields');
      }

      // Check if node type is registered
      if (!nodeRegistry.hasNode(type)) {
        console.warn(`Unknown node type: ${type}. Using fallback.`);
      }

      // Process node data
      const processedData = validateNodeData(data)
        ? {
            ...data,
            // Add handlers if needed
            ...(nodeRegistry.getNodeMetadata(type)?.needsOnChangeHandler ? {
              onChange: (value: string) => handlers.onChange(value, String(id)),
              onRawDataChange: (value: string) => handlers.onRawDataChange(value, String(id)),
              onJsonChange: (value: string) => handlers.onJsonChange(value, String(id))
            } : {})
          }
        : {
            label: nodeRegistry.getNodeLabel(type),
            onChange: (value: string) => handlers.onChange(value, String(id)),
            onRawDataChange: (value: string) => handlers.onRawDataChange(value, String(id)),
            onJsonChange: (value: string) => handlers.onJsonChange(value, String(id))
          };

      return {
        id: String(id),
        type,
        position,
        data: processedData
      };
    } catch (error) {
      console.error(`Error processing node at index ${index}:`, error);

      // Return a fallback error node
      return {
        id: `error-node-${Date.now()}-${index}`,
        type: 'textOutput',
        position: generateRandomPosition(),
        data: { label: 'Error Node' }
      };
    }
  });
}

/**
 * Check if a node type needs onChange handlers
 */
export function nodeNeedsOnChangeHandler(type: string): boolean {
  return nodeRegistry.getNodeMetadata(type)?.needsOnChangeHandler || false;
}

/**
 * Get node display label
 */
export function getNodeDisplayLabel(type: string): string {
  return nodeRegistry.getNodeLabel(type);
}

/**
 * Sanitize node data for saving
 */
export function sanitizeNodeForSave(node: Node): any {
  const { data, ...nodeWithoutData } = node;

  // Remove function references from data
  const sanitizedData = {
    label: data.label,
    value: data.value,
    inputValue: data.inputValue,
    fileContent: data.fileContent
    // Exclude onChange, onRawDataChange, onJsonChange functions
  };

  return {
    ...nodeWithoutData,
    data: sanitizedData
  };
}

/**
 * Batch sanitize nodes for saving
 */
export function sanitizeNodesForSave(nodes: Node[]): any[] {
  return nodes.map(sanitizeNodeForSave);
}
