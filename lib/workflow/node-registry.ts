import { ComponentType } from 'react';
import { NodeProps } from 'reactflow';
import {
  NodeRegistry,
  NodeRegistration,
  NodeMetadata,
  NodeCategory,
  CategoryInfo
} from './node-interface';

/**
 * Category definitions with display information
 */
const CATEGORY_INFO: Record<NodeCategory, CategoryInfo> = {
  input: {
    title: 'Input Nodes',
    description: 'Nodes for data input and sources',
    order: 1
  },
  output: {
    title: 'Output Nodes',
    description: 'Nodes for displaying and exporting data',
    order: 2
  },
  transform: {
    title: 'Transform Nodes',
    description: 'Nodes for data transformation and processing',
    order: 3
  },
  control: {
    title: 'Control Flow',
    description: 'Nodes for loops, scheduling, and workflow control',
    order: 4
  },
  advanced: {
    title: 'Advanced Nodes',
    description: 'Complex processing and integration nodes',
    order: 5
  },
  ai: {
    title: 'AI Nodes',
    description: 'Artificial intelligence and machine learning nodes',
    order: 6
  },
  data: {
    title: 'Data Nodes',
    description: 'Database and data source nodes',
    order: 7
  }
};

/**
 * Implementation of the node registry
 */
class WorkflowNodeRegistry implements NodeRegistry {
  private nodes = new Map<string, NodeRegistration>();
  private loadedComponents = new Map<string, ComponentType<NodeProps>>();

  /**
   * Get all registered node metadata
   */
  getAllNodes(): NodeMetadata[] {
    return Array.from(this.nodes.values()).map(reg => reg.metadata);
  }

  /**
   * Get nodes by category, sorted by label
   */
  getNodesByCategory(category: NodeCategory): NodeMetadata[] {
    return this.getAllNodes()
      .filter(node => node.category === category)
      .sort((a, b) => a.label.localeCompare(b.label));
  }

  /**
   * Get node metadata by type
   */
  getNodeMetadata(type: string): NodeMetadata | undefined {
    return this.nodes.get(type)?.metadata;
  }

  /**
   * Get node component, loading dynamically if needed
   */
  async getNodeComponent(type: string): Promise<ComponentType<NodeProps> | undefined> {
    // Return cached component if available
    if (this.loadedComponents.has(type)) {
      return this.loadedComponents.get(type);
    }

    // Get registration
    const registration = this.nodes.get(type);
    if (!registration) {
      console.warn(`Node type "${type}" not found in registry`);
      return undefined;
    }

    try {
      // Load component dynamically
      const module = await registration.loader();
      const component = module.default;

      // Cache the loaded component
      this.loadedComponents.set(type, component);

      return component;
    } catch (error) {
      console.error(`Failed to load node component for type "${type}":`, error);
      return undefined;
    }
  }

  /**
   * Register a new node
   */
  registerNode(registration: NodeRegistration): void {
    const { type } = registration.metadata;

    if (this.nodes.has(type)) {
      console.warn(`Node type "${type}" is already registered. Overwriting.`);
    }

    this.nodes.set(type, registration);
    console.log(`Registered node: ${type} (${registration.metadata.label})`);

    // Dispatch event to notify components that registry has been updated
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('nodeRegistryUpdated', {
        detail: { nodeType: type, action: 'registered' }
      }));
    }
  }

  /**
   * Check if a node type exists
   */
  hasNode(type: string): boolean {
    return this.nodes.has(type);
  }

  /**
   * Get node registration (including execution definition)
   */
  getNodeRegistration(type: string): NodeRegistration | undefined {
    return this.nodes.get(type);
  }

  /**
   * Check if a node has execution capabilities
   */
  hasExecution(type: string): boolean {
    const registration = this.nodes.get(type);
    return registration?.execution !== undefined;
  }

  /**
   * Get node execution definition
   */
  getNodeExecution(type: string) {
    const registration = this.nodes.get(type);
    return registration?.execution;
  }

  /**
   * Get all categories with their info
   */
  getCategories(): Record<NodeCategory, CategoryInfo> {
    return CATEGORY_INFO;
  }

  /**
   * Get node types that need onChange handlers
   */
  getNodeTypesNeedingOnChange(): string[] {
    return this.getAllNodes()
      .filter(node => node.needsOnChangeHandler)
      .map(node => node.type);
  }

  /**
   * Get node label by type (fallback for compatibility)
   */
  getNodeLabel(type: string): string {
    const metadata = this.getNodeMetadata(type);
    return metadata?.label || type;
  }

  /**
   * Clear all registered nodes (useful for testing)
   */
  clear(): void {
    this.nodes.clear();
    this.loadedComponents.clear();
  }

  /**
   * Get registry statistics
   */
  getStats() {
    const nodesByCategory = Object.keys(CATEGORY_INFO).reduce((acc, category) => {
      acc[category as NodeCategory] = this.getNodesByCategory(category as NodeCategory).length;
      return acc;
    }, {} as Record<NodeCategory, number>);

    return {
      totalNodes: this.nodes.size,
      loadedComponents: this.loadedComponents.size,
      nodesByCategory
    };
  }
}

// Create and export the singleton registry instance
export const nodeRegistry = new WorkflowNodeRegistry();

// Export the class for testing purposes
export { WorkflowNodeRegistry };
