/**
 * Client-side Export Utilities for Workflow Execution Results
 */

export interface ExportOptions {
  format: 'json' | 'csv' | 'excel';
  includeMetadata?: boolean;
  includeLogs?: boolean;
  includeErrors?: boolean;
  filename?: string;
}

export interface ExportData {
  summary: any;
  tableData: any[];
  metadata?: any;
  logs?: any[];
  errors?: any[];
  exportedAt: string;
}

/**
 * Export execution results as JSON
 */
export function exportAsJSON(data: ExportData, options: ExportOptions = { format: 'json' }): void {
  const exportData = {
    ...data,
    exportedAt: new Date().toISOString()
  };

  // Filter data based on options
  if (!options.includeMetadata) {
    delete exportData.metadata;
  }
  if (!options.includeLogs) {
    delete exportData.logs;
  }
  if (!options.includeErrors) {
    delete exportData.errors;
  }

  const jsonString = JSON.stringify(exportData, null, 2);
  const blob = new Blob([jsonString], { type: 'application/json' });
  
  const filename = options.filename || `workflow-execution-${Date.now()}.json`;
  downloadBlob(blob, filename);
}

/**
 * Export execution results as CSV
 */
export function exportAsCSV(data: ExportData, options: ExportOptions = { format: 'csv' }): void {
  if (!data.tableData || data.tableData.length === 0) {
    throw new Error('No table data available for CSV export');
  }

  // Get all unique keys from the data
  const allKeys = new Set<string>();
  data.tableData.forEach(item => {
    Object.keys(item).forEach(key => allKeys.add(key));
  });

  const headers = Array.from(allKeys);
  const csvRows: string[] = [];

  // Add headers
  csvRows.push(headers.map(header => escapeCSVField(header)).join(','));

  // Add data rows
  data.tableData.forEach(row => {
    const csvRow = headers.map(header => {
      const value = row[header];
      return escapeCSVField(value);
    }).join(',');
    csvRows.push(csvRow);
  });

  // Add metadata as comments if requested
  if (options.includeMetadata && data.metadata) {
    const metadataRows = [
      '# Metadata',
      `# Exported at: ${data.exportedAt}`,
      `# Workflow ID: ${data.metadata.workflowId || 'N/A'}`,
      `# Execution ID: ${data.metadata.executionId || 'N/A'}`,
      `# Status: ${data.metadata.status || 'N/A'}`,
      '# ',
    ];
    csvRows.unshift(...metadataRows);
  }

  const csvContent = csvRows.join('\n');
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  
  const filename = options.filename || `workflow-execution-${Date.now()}.csv`;
  downloadBlob(blob, filename);
}

/**
 * Export execution results as Excel (XLSX)
 * Note: This is a simplified implementation. For full Excel support, consider using libraries like xlsx
 */
export function exportAsExcel(data: ExportData, options: ExportOptions = { format: 'excel' }): void {
  // For now, we'll export as CSV with .xlsx extension
  // In a real implementation, you'd use a library like xlsx to create proper Excel files
  console.warn('Excel export is using CSV format. Consider implementing proper XLSX support.');
  
  const csvOptions = { ...options, format: 'csv' as const };
  const filename = options.filename || `workflow-execution-${Date.now()}.xlsx`;
  
  exportAsCSV(data, { ...csvOptions, filename });
}

/**
 * Escape CSV field values
 */
function escapeCSVField(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }

  const stringValue = String(value);
  
  // If the value contains comma, newline, or quote, wrap it in quotes
  if (stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('"')) {
    // Escape quotes by doubling them
    const escapedValue = stringValue.replace(/"/g, '""');
    return `"${escapedValue}"`;
  }
  
  return stringValue;
}

/**
 * Download a blob as a file
 */
function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  // Append to body, click, and remove
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL
  URL.revokeObjectURL(url);
}

/**
 * Copy data to clipboard
 */
export async function copyToClipboard(data: any, format: 'json' | 'csv' = 'json'): Promise<boolean> {
  try {
    let textToCopy: string;
    
    if (format === 'json') {
      textToCopy = JSON.stringify(data, null, 2);
    } else {
      // Convert to CSV format
      if (Array.isArray(data) && data.length > 0) {
        const headers = Object.keys(data[0]);
        const csvRows = [
          headers.join(','),
          ...data.map(row => headers.map(header => escapeCSVField(row[header])).join(','))
        ];
        textToCopy = csvRows.join('\n');
      } else {
        textToCopy = JSON.stringify(data, null, 2);
      }
    }
    
    await navigator.clipboard.writeText(textToCopy);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Validate export data
 */
export function validateExportData(data: ExportData): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!data) {
    errors.push('Export data is required');
    return { valid: false, errors };
  }
  
  if (!data.summary) {
    errors.push('Summary data is required');
  }
  
  if (!data.exportedAt) {
    errors.push('Export timestamp is required');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Create export data from execution status
 */
export function createExportData(
  executionStatus: any,
  tableData?: any[],
  options: Partial<ExportOptions> = {}
): ExportData {
  const exportData: ExportData = {
    summary: executionStatus,
    tableData: tableData || [],
    exportedAt: new Date().toISOString()
  };
  
  if (options.includeMetadata) {
    exportData.metadata = {
      workflowId: executionStatus.workflowId,
      executionId: executionStatus.executionId || executionStatus.workflowId,
      status: executionStatus.status,
      startTime: executionStatus.startTime,
      endTime: executionStatus.endTime,
      duration: executionStatus.endTime && executionStatus.startTime
        ? new Date(executionStatus.endTime).getTime() - new Date(executionStatus.startTime).getTime()
        : null
    };
  }
  
  if (options.includeLogs && executionStatus.logs) {
    exportData.logs = executionStatus.logs;
  }
  
  if (options.includeErrors && executionStatus.error) {
    exportData.errors = [executionStatus.error];
  }
  
  return exportData;
}

/**
 * Get suggested filename for export
 */
export function getSuggestedFilename(
  executionId: string,
  format: string,
  prefix: string = 'workflow-execution'
): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
  const shortId = executionId.substring(0, 8);
  
  return `${prefix}-${shortId}-${timestamp}.${format}`;
}
