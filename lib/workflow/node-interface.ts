import { ComponentType } from 'react';
import { NodeProps } from 'reactflow';
import { LucideIcon } from 'lucide-react';
import { NodeExecutionDefinition } from './execution-interface';

/**
 * Standard interface for node data that flows between nodes
 */
export interface NodeData {
  label: string;
  value?: string;
  inputValue?: string;
  fileContent?: string;
  onChange?: (value: string) => void;
  onRawDataChange?: (value: string) => void;
  onJsonChange?: (value: string) => void;
  onDataUpdate?: (data: Record<string, any>) => void;
}

/**
 * Node category for organizing nodes in the selector
 */
export type NodeCategory =
  | 'input'
  | 'output'
  | 'transform'
  | 'control'
  | 'advanced'
  | 'ai'
  | 'data';

/**
 * Node metadata that describes the node's capabilities and appearance
 */
export interface NodeMetadata {
  /** Unique identifier for the node type */
  type: string;

  /** Display name for the node */
  label: string;

  /** Brief description of what the node does */
  description: string;

  /** Icon component to display in the selector */
  icon: LucideIcon;

  /** Category for organizing nodes */
  category: NodeCategory;

  /** Whether this node needs onChange handlers */
  needsOnChangeHandler: boolean;

  /** Whether this node should be loaded dynamically */
  isDynamic: boolean;

  /** Default width for the node (optional) */
  defaultWidth?: number;

  /** Tags for searching/filtering nodes */
  tags?: string[];
}

/**
 * Complete node definition including component and metadata
 */
export interface NodeDefinition {
  /** Node metadata */
  metadata: NodeMetadata;

  /** React component for the node */
  component: ComponentType<NodeProps>;
}

/**
 * Node registration entry for dynamic loading
 */
export interface NodeRegistration {
  /** Node metadata */
  metadata: NodeMetadata;

  /** Function to dynamically import the component */
  loader: () => Promise<{ default: ComponentType<NodeProps> }>;

  /** Execution definition for workflow engine (optional) */
  execution?: NodeExecutionDefinition;
}

/**
 * Category information for organizing nodes in the selector
 */
export interface CategoryInfo {
  title: string;
  description?: string;
  order: number;
}

/**
 * Node registry interface for managing all available nodes
 */
export interface NodeRegistry {
  /** Get all registered node metadata */
  getAllNodes(): NodeMetadata[];

  /** Get nodes by category */
  getNodesByCategory(category: NodeCategory): NodeMetadata[];

  /** Get node metadata by type */
  getNodeMetadata(type: string): NodeMetadata | undefined;

  /** Get node component (loads dynamically if needed) */
  getNodeComponent(type: string): Promise<ComponentType<NodeProps> | undefined>;

  /** Register a new node */
  registerNode(registration: NodeRegistration): void;

  /** Check if a node type exists */
  hasNode(type: string): boolean;

  /** Get all categories with their info */
  getCategories(): Record<NodeCategory, CategoryInfo>;
}

/**
 * Standard props that all nodes should accept
 */
export interface StandardNodeProps extends NodeProps {
  data: NodeData;
}

/**
 * Helper type for node component with proper typing
 */
export type NodeComponent = ComponentType<StandardNodeProps>;
