/**
 * Background Workflow Execution Queue System
 * Handles queuing and processing of workflow executions in the background
 */

import { Node, Edge } from 'reactflow';
import { ExecutionContext, ExecutionOptions, WorkflowExecutionStatus } from './execution-engine';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface QueuedExecution {
  id: string;
  workflowId: string;
  userId: string;
  nodes: Node[];
  edges: Edge[];
  context: ExecutionContext;
  options: ExecutionOptions;
  priority: number; // 1-10, higher = more priority
  scheduledAt?: Date; // For scheduled executions
  createdAt: Date;
  attempts: number;
  maxAttempts: number;
  lastError?: string;
}

export interface ExecutionQueueStats {
  totalQueued: number;
  totalRunning: number;
  totalCompleted: number;
  totalFailed: number;
  averageExecutionTime: number;
  queueProcessingRate: number;
}

export class WorkflowExecutionQueue {
  private static instance: WorkflowExecutionQueue;
  private queue: Map<string, QueuedExecution> = new Map();
  private running: Map<string, QueuedExecution> = new Map();
  private completed: Map<string, QueuedExecution> = new Map();
  private failed: Map<string, QueuedExecution> = new Map();
  private isProcessing = false;
  private maxConcurrentExecutions = 3;
  private processingInterval: NodeJS.Timeout | null = null;
  private statusCallbacks: Map<string, (status: WorkflowExecutionStatus) => void> = new Map();

  static getInstance(): WorkflowExecutionQueue {
    if (!WorkflowExecutionQueue.instance) {
      WorkflowExecutionQueue.instance = new WorkflowExecutionQueue();
    }
    return WorkflowExecutionQueue.instance;
  }

  private constructor() {
    this.startProcessing();
  }

  /**
   * Add a workflow execution to the queue
   */
  async enqueue(
    workflowId: string,
    userId: string,
    nodes: Node[],
    edges: Edge[],
    context: ExecutionContext,
    options: ExecutionOptions = { mode: 'sequential' },
    priority: number = 5,
    scheduledAt?: Date
  ): Promise<string> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const queuedExecution: QueuedExecution = {
      id: executionId,
      workflowId,
      userId,
      nodes,
      edges,
      context,
      options: {
        continueOnError: true,
        retryAttempts: 3,
        mode: 'sequential',
        ...options,
        backgroundExecution: true
      },
      priority,
      scheduledAt,
      createdAt: new Date(),
      attempts: 0,
      maxAttempts: options.retryAttempts || 3
    };

    // Save to database
    await prisma.workflowExecution.create({
      data: {
        id: executionId,
        workflowId,
        userId,
        status: scheduledAt ? 'queued' : 'queued',
        startTime: scheduledAt || new Date(),
        variables: JSON.stringify(context.variables || {}),
        options: JSON.stringify(options),
        logs: JSON.stringify([{
          timestamp: new Date(),
          level: 'info',
          message: scheduledAt ? `Scheduled for ${scheduledAt.toISOString()}` : 'Added to execution queue'
        }])
      }
    });

    this.queue.set(executionId, queuedExecution);
    
    console.log(`[ExecutionQueue] Added execution ${executionId} to queue (priority: ${priority})`);
    
    // Notify status change
    this.notifyStatusChange(executionId, {
      workflowId,
      status: 'queued',
      startTime: new Date(),
      completedNodes: [],
      failedNodes: [],
      progress: 0,
      results: {},
      logs: [],
      executionId,
      isBackground: true,
      queuePosition: this.getQueuePosition(executionId)
    });

    return executionId;
  }

  /**
   * Get position of execution in queue
   */
  private getQueuePosition(executionId: string): number {
    const sortedQueue = Array.from(this.queue.values())
      .sort((a, b) => {
        // Sort by priority (higher first), then by creation time
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return a.createdAt.getTime() - b.createdAt.getTime();
      });
    
    return sortedQueue.findIndex(exec => exec.id === executionId) + 1;
  }

  /**
   * Start the queue processing loop
   */
  private startProcessing() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 1000); // Check every second
    
    console.log('[ExecutionQueue] Started queue processing');
  }

  /**
   * Stop the queue processing loop
   */
  stopProcessing() {
    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    console.log('[ExecutionQueue] Stopped queue processing');
  }

  /**
   * Process the execution queue
   */
  private async processQueue() {
    if (this.running.size >= this.maxConcurrentExecutions) {
      return; // Already at max capacity
    }

    // Get next execution to process
    const nextExecution = this.getNextExecution();
    if (!nextExecution) {
      return; // No executions ready
    }

    // Move from queue to running
    this.queue.delete(nextExecution.id);
    this.running.set(nextExecution.id, nextExecution);

    console.log(`[ExecutionQueue] Starting execution ${nextExecution.id}`);

    try {
      await this.executeWorkflow(nextExecution);
    } catch (error) {
      console.error(`[ExecutionQueue] Execution ${nextExecution.id} failed:`, error);
      await this.handleExecutionFailure(nextExecution, error);
    }
  }

  /**
   * Get the next execution to process
   */
  private getNextExecution(): QueuedExecution | null {
    const now = new Date();
    
    // Filter executions that are ready to run
    const readyExecutions = Array.from(this.queue.values())
      .filter(exec => !exec.scheduledAt || exec.scheduledAt <= now)
      .sort((a, b) => {
        // Sort by priority (higher first), then by creation time
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return a.createdAt.getTime() - b.createdAt.getTime();
      });

    return readyExecutions[0] || null;
  }

  /**
   * Execute a workflow from the queue
   */
  private async executeWorkflow(queuedExecution: QueuedExecution) {
    const { WorkflowExecutionEngine } = await import('./execution-engine');
    const executionEngine = WorkflowExecutionEngine.getInstance();

    queuedExecution.attempts++;

    // Update database status
    await prisma.workflowExecution.update({
      where: { id: queuedExecution.id },
      data: {
        status: 'running',
        startTime: new Date(),
        logs: JSON.stringify([{
          timestamp: new Date(),
          level: 'info',
          message: `Background execution started (attempt ${queuedExecution.attempts})`
        }])
      }
    });

    try {
      // Execute the workflow
      const result = await executionEngine.executeWorkflow(
        queuedExecution.nodes,
        queuedExecution.edges,
        queuedExecution.context,
        queuedExecution.options
      );

      // Move to completed
      this.running.delete(queuedExecution.id);
      this.completed.set(queuedExecution.id, queuedExecution);

      // Update database with results
      await prisma.workflowExecution.update({
        where: { id: queuedExecution.id },
        data: {
          status: result.status,
          endTime: result.endTime || new Date(),
          results: JSON.stringify(result.results),
          logs: JSON.stringify(result.logs),
          progress: result.progress
        }
      });

      // Notify completion
      this.notifyStatusChange(queuedExecution.id, {
        ...result,
        executionId: queuedExecution.id,
        isBackground: true
      });

      console.log(`[ExecutionQueue] Execution ${queuedExecution.id} completed successfully`);

    } catch (error) {
      await this.handleExecutionFailure(queuedExecution, error);
    }
  }

  /**
   * Handle execution failure
   */
  private async handleExecutionFailure(queuedExecution: QueuedExecution, error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    queuedExecution.lastError = errorMessage;

    this.running.delete(queuedExecution.id);

    if (queuedExecution.attempts < queuedExecution.maxAttempts) {
      // Retry: put back in queue with lower priority
      queuedExecution.priority = Math.max(1, queuedExecution.priority - 1);
      this.queue.set(queuedExecution.id, queuedExecution);
      
      console.log(`[ExecutionQueue] Execution ${queuedExecution.id} failed, retrying (attempt ${queuedExecution.attempts}/${queuedExecution.maxAttempts})`);
    } else {
      // Max attempts reached, mark as failed
      this.failed.set(queuedExecution.id, queuedExecution);
      
      // Update database
      await prisma.workflowExecution.update({
        where: { id: queuedExecution.id },
        data: {
          status: 'failed',
          endTime: new Date(),
          error: errorMessage,
          logs: JSON.stringify([{
            timestamp: new Date(),
            level: 'error',
            message: `Execution failed after ${queuedExecution.attempts} attempts: ${errorMessage}`
          }])
        }
      });

      // Notify failure
      this.notifyStatusChange(queuedExecution.id, {
        workflowId: queuedExecution.workflowId,
        status: 'failed',
        startTime: queuedExecution.createdAt,
        endTime: new Date(),
        completedNodes: [],
        failedNodes: [],
        progress: 0,
        results: {},
        logs: [{
          timestamp: new Date(),
          level: 'error',
          message: errorMessage,
        }],
        executionId: queuedExecution.id,
        isBackground: true
      });

      console.log(`[ExecutionQueue] Execution ${queuedExecution.id} failed permanently: ${errorMessage}`);
    }
  }

  /**
   * Subscribe to execution status changes
   */
  onStatusChange(executionId: string, callback: (status: WorkflowExecutionStatus) => void): () => void {
    this.statusCallbacks.set(executionId, callback);
    return () => this.statusCallbacks.delete(executionId);
  }

  /**
   * Notify status change
   */
  private notifyStatusChange(executionId: string, status: WorkflowExecutionStatus) {
    const callback = this.statusCallbacks.get(executionId);
    if (callback) {
      callback(status);
    }
  }

  /**
   * Get queue statistics
   */
  getStats(): ExecutionQueueStats {
    return {
      totalQueued: this.queue.size,
      totalRunning: this.running.size,
      totalCompleted: this.completed.size,
      totalFailed: this.failed.size,
      averageExecutionTime: 0, // TODO: Calculate from completed executions
      queueProcessingRate: 0 // TODO: Calculate processing rate
    };
  }

  /**
   * Get execution status
   */
  getExecutionStatus(executionId: string): QueuedExecution | null {
    return this.queue.get(executionId) || 
           this.running.get(executionId) || 
           this.completed.get(executionId) || 
           this.failed.get(executionId) || 
           null;
  }

  /**
   * Cancel a queued execution
   */
  async cancelExecution(executionId: string): Promise<boolean> {
    const execution = this.queue.get(executionId);
    if (execution) {
      this.queue.delete(executionId);
      
      // Update database
      await prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'cancelled',
          endTime: new Date(),
          logs: JSON.stringify([{
            timestamp: new Date(),
            level: 'info',
            message: 'Execution cancelled by user'
          }])
        }
      });

      console.log(`[ExecutionQueue] Cancelled execution ${executionId}`);
      return true;
    }
    return false;
  }
}
