// API Cache implementation for workflow nodes

interface CacheEntry {
  data: any;
  timestamp: number;
  expiresAt: number;
}

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of entries in the cache
}

class ApiCache {
  private cache: Map<string, CacheEntry>;
  private ttl: number; // Default time to live: 5 minutes
  private maxSize: number; // Default max size: 100 entries
  
  constructor(options?: CacheOptions) {
    this.cache = new Map<string, CacheEntry>();
    this.ttl = options?.ttl || 5 * 60 * 1000; // 5 minutes default
    this.maxSize = options?.maxSize || 100; // 100 entries default
  }
  
  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached value or null if not found or expired
   */
  get(key: string): any {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    // Check if the entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  /**
   * Set a value in the cache
   * @param key The cache key
   * @param data The data to cache
   * @param ttl Optional custom TTL for this entry
   */
  set(key: string, data: any, ttl?: number): void {
    // Enforce max size by removing oldest entries if needed
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.getOldestKey();
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }
    
    const timestamp = Date.now();
    const expiresAt = timestamp + (ttl || this.ttl);
    
    this.cache.set(key, {
      data,
      timestamp,
      expiresAt
    });
  }
  
  /**
   * Check if a key exists in the cache and is not expired
   * @param key The cache key
   * @returns True if the key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }
    
    // Check if the entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }
  
  /**
   * Remove a key from the cache
   * @param key The cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }
  
  /**
   * Clear all entries from the cache
   */
  clear(): void {
    this.cache.clear();
  }
  
  /**
   * Get the number of entries in the cache
   */
  size(): number {
    return this.cache.size;
  }
  
  /**
   * Get the oldest key in the cache based on timestamp
   * @returns The oldest key or null if cache is empty
   */
  private getOldestKey(): string | null {
    if (this.cache.size === 0) {
      return null;
    }
    
    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }
    
    return oldestKey;
  }
  
  /**
   * Remove all expired entries from the cache
   * @returns The number of entries removed
   */
  cleanup(): number {
    const now = Date.now();
    let removedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    return removedCount;
  }
}

// Create a singleton instance
const apiCache = new ApiCache();

export default apiCache;
