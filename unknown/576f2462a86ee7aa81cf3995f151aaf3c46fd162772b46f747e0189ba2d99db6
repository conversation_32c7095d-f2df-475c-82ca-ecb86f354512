"use client";

import { useState, useEffect, useCallback, memo } from "react";
import { <PERSON><PERSON>, Posi<PERSON> } from "reactflow";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  RefreshCw,
  Copy,
  Check,
  Settings,
  FileText,
  Code,
  Table,
  List,
  Braces
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

/**
 * Converter Node - Standardized Structure
 * Cleans and extracts data from AI responses
 */
const ConverterNode = memo(({ data, id }: StandardNodeProps) => {
  const [inputText, setInputText] = useState<string>("");
  const [cleanedText, setCleanedText] = useState<string>("");
  const [jsonOutput, setJsonOutput] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);
  const [autoProcess, setAutoProcess] = useState<boolean>(true);
  const [extractionMode, setExtractionMode] = useState<string>("smart");
  const [extractionTarget, setExtractionTarget] = useState<string>("auto");
  const [customPath, setCustomPath] = useState<string>("");

  // Handle input changes from connected nodes
  useEffect(() => {
    if (data.inputValue && data.inputValue !== inputText) {
      setInputText(data.inputValue);
      if (autoProcess) {
        processText(data.inputValue);
      }
    }
  }, [data.inputValue, autoProcess]);

  // Advanced text cleaning and extraction
  const extractRawData = useCallback((text: string): { cleaned: string; structured: any } => {
    if (!text) return { cleaned: "", structured: {} };

    let cleaned = text;
    let structured: any = {
      type: "text",
      data: [],
      metadata: {
        originalLength: text.length,
        extractedAt: new Date().toISOString()
      }
    };

    // Skip JSON extraction if text_only mode is selected
    if (extractionTarget === "text_only") {
      // Just remove narration and return text
      const narrationPatterns = [
        /^(here\s+(are|is)|below\s+(are|is)|above\s+(are|is))[^.!?]*[.!?]/gmi,
        /^(these\s+|this\s+|the\s+following)[^.!?]*[.!?]/gmi,
        /^(sample|example|data)[^.!?]*[.!?]/gmi,
        /^(i\s+have|i've)[^.!?]*[.!?]/gmi,
        /^(let\s+me|i\s+will)[^.!?]*[.!?]/gmi,
        /(hope\s+this\s+helps|let\s+me\s+know)[^.!?]*[.!?]/gmi,
      ];

      narrationPatterns.forEach(pattern => {
        cleaned = cleaned.replace(pattern, '');
      });

      cleaned = cleaned.trim();
      structured.type = "text";
      structured.data = cleaned.split('\n').filter(line => line.trim());
      return { cleaned, structured };
    }

    // First, try to extract JSON from nested structures (like AI API responses)
    if (extractionTarget === "auto" || extractionTarget === "nested_json") {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(text);

        // Handle common AI response structures
        if (Array.isArray(parsed) && parsed.length > 0) {
          // Handle array responses like OpenAI format
          const firstItem = parsed[0];
          if (firstItem.message && firstItem.message.content) {
            // OpenAI format: [{ message: { content: "..." } }]
            cleaned = firstItem.message.content;
          } else if (firstItem.content) {
            // Simple format: [{ content: "..." }]
            cleaned = firstItem.content;
          }
        } else if (parsed.content) {
          // Direct content field
          cleaned = parsed.content;
        } else if (parsed.message && parsed.message.content) {
          // Message wrapper
          cleaned = parsed.message.content;
        } else if (parsed.choices && Array.isArray(parsed.choices) && parsed.choices[0]) {
          // OpenAI choices format
          const choice = parsed.choices[0];
          if (choice.message && choice.message.content) {
            cleaned = choice.message.content;
          } else if (choice.text) {
            cleaned = choice.text;
          }
        }
      } catch (e) {
        // Not JSON, continue with text processing
      }
    }

    // Handle custom path extraction
    if (extractionTarget === "custom" && customPath) {
      try {
        const parsed = JSON.parse(text);
        const pathParts = customPath.split('.');
        let current = parsed;

        for (const part of pathParts) {
          if (current && typeof current === 'object') {
            // Handle array indices like [0]
            if (part.includes('[') && part.includes(']')) {
              const [key, indexStr] = part.split('[');
              const index = parseInt(indexStr.replace(']', ''));
              current = current[key] ? current[key][index] : undefined;
            } else {
              current = current[part];
            }
          }
        }

        if (current !== undefined) {
          cleaned = typeof current === 'string' ? current : JSON.stringify(current);
        }
      } catch (e) {
        console.error('Custom path extraction failed:', e);
      }
    }

    // Now try to extract JSON content from the cleaned text
    if (extractionTarget === "auto" || extractionTarget === "json_content") {
      try {
        // Look for JSON content within the text
        const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonStr = jsonMatch[0];
          const parsedJson = JSON.parse(jsonStr);
          structured.type = "json";
          structured.data = parsedJson;
          cleaned = JSON.stringify(parsedJson, null, 2);
          return { cleaned, structured };
        }
      } catch (e) {
        // Continue with other extraction methods
      }
    }

    // Remove common AI narration phrases
    const narrationPatterns = [
      /^(here\s+(are|is)|below\s+(are|is)|above\s+(are|is))[^.!?]*[.!?]/gmi,
      /^(these\s+|this\s+|the\s+following)[^.!?]*[.!?]/gmi,
      /^(sample|example|data)[^.!?]*[.!?]/gmi,
      /^(i\s+have|i've)[^.!?]*[.!?]/gmi,
      /^(let\s+me|i\s+will)[^.!?]*[.!?]/gmi,
      /(hope\s+this\s+helps|let\s+me\s+know)[^.!?]*[.!?]/gmi,
    ];

    narrationPatterns.forEach(pattern => {
      cleaned = cleaned.replace(pattern, '');
    });

    // Extract different data types

    // 1. Tables (markdown format)
    const tableMatches = cleaned.match(/\|.*\|[\s\S]*?\|.*\|/g);
    if (tableMatches) {
      structured.type = "table";
      structured.data = tableMatches.map(table => {
        const rows = table.split('\n').filter(row => row.trim() && !row.includes('---'));
        return rows.map(row =>
          row.split('|').map(cell => cell.trim()).filter(cell => cell)
        );
      });
    }

    // 2. Numbered lists
    const numberedListMatches = cleaned.match(/^\d+\.\s*(.+)$/gm);
    if (numberedListMatches && !tableMatches) {
      structured.type = "numbered_list";
      structured.data = numberedListMatches.map(item =>
        item.replace(/^\d+\.\s*/, '').trim()
      );
    }

    // 3. Bullet lists
    const bulletListMatches = cleaned.match(/^[-*•]\s*(.+)$/gm);
    if (bulletListMatches && !tableMatches && !numberedListMatches) {
      structured.type = "bullet_list";
      structured.data = bulletListMatches.map(item =>
        item.replace(/^[-*•]\s*/, '').trim()
      );
    }

    // 4. JSON objects
    const jsonMatches = cleaned.match(/\{[\s\S]*?\}/g);
    if (jsonMatches) {
      structured.type = "json";
      structured.data = jsonMatches.map(json => {
        try {
          return JSON.parse(json);
        } catch {
          return json;
        }
      });
    }

    // 5. Code blocks
    const codeMatches = cleaned.match(/```[\s\S]*?```/g);
    if (codeMatches) {
      structured.type = "code";
      structured.data = codeMatches.map(code =>
        code.replace(/```\w*\n?/, '').replace(/```$/, '').trim()
      );
    }

    // 6. Key-value pairs
    const kvMatches = cleaned.match(/^[^:\n]+:\s*[^:\n]+$/gm);
    if (kvMatches && !tableMatches && !numberedListMatches && !bulletListMatches) {
      structured.type = "key_value";
      structured.data = kvMatches.map(kv => {
        const [key, ...valueParts] = kv.split(':');
        return {
          key: key.trim(),
          value: valueParts.join(':').trim()
        };
      });
    }

    // Clean up the text further
    cleaned = cleaned
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\|.*\|[\s\S]*?\|.*\|/g, '') // Remove tables
      .replace(/^\s*[-*•]\s*/gm, '') // Remove bullet points
      .replace(/^\s*\d+\.\s*/gm, '') // Remove numbers
      .replace(/^[^:\n]+:\s*/gm, '') // Remove keys from key-value pairs
      .replace(/\n\s*\n/g, '\n') // Remove extra newlines
      .trim();

    // If no specific structure found, treat as plain text
    if (structured.data.length === 0) {
      structured.type = "text";
      structured.data = cleaned.split('\n').filter(line => line.trim());
    }

    return { cleaned, structured };
  }, [extractionTarget, customPath]);

  // Process the input text
  const processText = useCallback(async (text?: string) => {
    const textToProcess = text || inputText;
    if (!textToProcess.trim()) return;

    setIsProcessing(true);

    try {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 300));

      const { cleaned, structured } = extractRawData(textToProcess);

      setCleanedText(cleaned);
      setJsonOutput(JSON.stringify(structured, null, 2));

      // Update connected nodes
      if (data.onChange) {
        data.onChange(cleaned);
      }

      if (data.onJsonChange) {
        data.onJsonChange(JSON.stringify(structured, null, 2));
      }

    } catch (error) {
      console.error("Error processing text:", error);
    } finally {
      setIsProcessing(false);
    }
  }, [inputText, extractRawData, data]);

  // Copy to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  // Get data type icon
  const getDataTypeIcon = (type: string) => {
    switch (type) {
      case "table": return <Table className="h-3 w-3" />;
      case "numbered_list":
      case "bullet_list": return <List className="h-3 w-3" />;
      case "json": return <Braces className="h-3 w-3" />;
      case "code": return <Code className="h-3 w-3" />;
      default: return <FileText className="h-3 w-3" />;
    }
  };

  const parsedJson = jsonOutput ? JSON.parse(jsonOutput) : null;

  return (
    <div className="bg-background border-2 border-border rounded-lg shadow-sm min-w-[300px] max-w-[400px]">
      <div className="flex items-center justify-between p-3 border-b border-border">
        <div className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4 text-primary" />
          <span className="font-medium text-sm">Converter</span>
          {parsedJson && (
            <Badge variant="secondary" className="text-xs flex items-center gap-1">
              {getDataTypeIcon(parsedJson.type)}
              {parsedJson.type.replace('_', ' ')}
            </Badge>
          )}
        </div>

        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <Settings className="h-3 w-3" />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Converter Settings</DialogTitle>
              <DialogDescription>
                Configure how the converter processes AI responses
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="auto-process">Auto Process</Label>
                <Switch
                  id="auto-process"
                  checked={autoProcess}
                  onCheckedChange={setAutoProcess}
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Automatically process input when it changes
              </p>

              <div className="space-y-2">
                <Label htmlFor="extraction-target">Extraction Target</Label>
                <Select value={extractionTarget} onValueChange={setExtractionTarget}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select extraction method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto Detect</SelectItem>
                    <SelectItem value="nested_json">Nested JSON (AI APIs)</SelectItem>
                    <SelectItem value="json_content">JSON Content</SelectItem>
                    <SelectItem value="text_only">Text Only</SelectItem>
                    <SelectItem value="custom">Custom Path</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Choose how to extract data from the input
                </p>
              </div>

              {extractionTarget === "custom" && (
                <div className="space-y-2">
                  <Label htmlFor="custom-path">Custom JSON Path</Label>
                  <Input
                    id="custom-path"
                    placeholder="e.g., message.content or choices[0].text"
                    value={customPath}
                    onChange={(e) => setCustomPath(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Use dot notation for nested objects and [index] for arrays
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <Label className="text-sm font-medium">Examples:</Label>
                <div className="text-xs text-muted-foreground space-y-1">
                  <div><strong>OpenAI:</strong> message.content</div>
                  <div><strong>Array:</strong> [0].message.content</div>
                  <div><strong>Choices:</strong> choices[0].text</div>
                  <div><strong>Direct:</strong> content</div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="p-3 space-y-3">
        {/* Input Preview */}
        <div>
          <Label className="text-xs text-muted-foreground">Input</Label>
          <ScrollArea className="h-16 w-full">
            <div className="p-2 bg-muted rounded-md text-xs break-words">
              {inputText || "Waiting for input..."}
            </div>
          </ScrollArea>
        </div>

        {/* Process Button */}
        <Button
          onClick={() => processText()}
          disabled={!inputText.trim() || isProcessing}
          size="sm"
          className="w-full"
        >
          {isProcessing ? (
            <>
              <RefreshCw className="h-3 w-3 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <RefreshCw className="h-3 w-3 mr-2" />
              Convert
            </>
          )}
        </Button>

        {/* Cleaned Output */}
        {cleanedText && (
          <div>
            <div className="flex items-center justify-between">
              <Label className="text-xs text-muted-foreground">Cleaned Text</Label>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => copyToClipboard(cleanedText)}
              >
                {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
              </Button>
            </div>
            <ScrollArea className="h-20 w-full">
              <div className="p-2 bg-muted rounded-md text-xs break-words">
                {cleanedText}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* JSON Preview */}
        {parsedJson && (
          <div>
            <Label className="text-xs text-muted-foreground">
              Extracted Data ({parsedJson.data.length} items)
            </Label>
            <ScrollArea className="h-16 w-full">
              <div className="p-2 bg-muted rounded-md text-xs break-words font-mono">
                {JSON.stringify(parsedJson.data, null, 1)}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output Handles */}
      <div className="absolute right-0 top-[40%] transform -translate-y-1/2 flex items-center">
        <span className="text-xs text-muted-foreground mr-2 bg-background px-1 rounded">Text</span>
        <Handle
          type="source"
          position={Position.Right}
          id="output"
          className="w-3 h-3 bg-primary border-2 border-background"
        />
      </div>

      <div className="absolute right-0 top-[60%] transform -translate-y-1/2 flex items-center">
        <span className="text-xs text-muted-foreground mr-2 bg-background px-1 rounded">JSON</span>
        <Handle
          type="source"
          position={Position.Right}
          id="jsonOutput"
          className="w-3 h-3 bg-green-500 border-2 border-background"
        />
      </div>
    </div>
  );
});

ConverterNode.displayName = "ConverterNode";

// Export execution definition for the workflow engine
export const converterExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    // Get input text from connected nodes
    const inputText = inputs.text || inputs.data || inputs.value || inputs.result || "";

    // Get configuration
    const extractionTarget = config.extractionTarget || "auto";
    const customPath = config.customPath || "";

    // Log execution
    context.log(`Converter Node processing text with extraction target: ${extractionTarget}`);

    if (!inputText.trim()) {
      throw new Error('No input text provided for conversion');
    }

    // Advanced text cleaning and extraction function
    const extractRawData = (text: string): { cleaned: string; structured: any } => {
      if (!text) return { cleaned: "", structured: {} };

      let cleaned = text;
      let structured: any = {
        type: "text",
        data: [],
        metadata: {
          originalLength: text.length,
          extractedAt: new Date().toISOString()
        }
      };

      // Skip JSON extraction if text_only mode is selected
      if (extractionTarget === "text_only") {
        // Just remove narration and return text
        const narrationPatterns = [
          /^(here\s+(are|is)|below\s+(are|is)|above\s+(are|is))[^.!?]*[.!?]/gmi,
          /^(these\s+|this\s+|the\s+following)[^.!?]*[.!?]/gmi,
          /^(sample|example|data)[^.!?]*[.!?]/gmi,
          /^(i\s+have|i've)[^.!?]*[.!?]/gmi,
          /^(let\s+me|i\s+will)[^.!?]*[.!?]/gmi,
          /(hope\s+this\s+helps|let\s+me\s+know)[^.!?]*[.!?]/gmi,
        ];

        narrationPatterns.forEach(pattern => {
          cleaned = cleaned.replace(pattern, '');
        });

        cleaned = cleaned.trim();
        structured.type = "text";
        structured.data = cleaned.split('\n').filter(line => line.trim());
        return { cleaned, structured };
      }

      // First, try to extract JSON from nested structures (like AI API responses)
      if (extractionTarget === "auto" || extractionTarget === "nested_json") {
        try {
          // Try to parse as JSON first
          const parsed = JSON.parse(text);

          // Handle common AI response structures
          if (Array.isArray(parsed) && parsed.length > 0) {
            const firstItem = parsed[0];
            if (firstItem.message && firstItem.message.content) {
              cleaned = firstItem.message.content;
            } else if (firstItem.content) {
              cleaned = firstItem.content;
            }
          } else if (parsed.content) {
            cleaned = parsed.content;
          } else if (parsed.message && parsed.message.content) {
            cleaned = parsed.message.content;
          } else if (parsed.choices && Array.isArray(parsed.choices) && parsed.choices[0]) {
            const choice = parsed.choices[0];
            if (choice.message && choice.message.content) {
              cleaned = choice.message.content;
            } else if (choice.text) {
              cleaned = choice.text;
            }
          }
        } catch (e) {
          // Not JSON, continue with text processing
        }
      }

      // Handle custom path extraction
      if (extractionTarget === "custom" && customPath) {
        try {
          const parsed = JSON.parse(text);
          const pathParts = customPath.split('.');
          let current = parsed;

          for (const part of pathParts) {
            if (current && typeof current === 'object') {
              // Handle array indices like [0]
              if (part.includes('[') && part.includes(']')) {
                const [key, indexStr] = part.split('[');
                const index = parseInt(indexStr.replace(']', ''));
                current = current[key] ? current[key][index] : undefined;
              } else {
                current = current[part];
              }
            }
          }

          if (current !== undefined) {
            cleaned = typeof current === 'string' ? current : JSON.stringify(current);
          }
        } catch (e) {
          context.log(`Custom path extraction failed: ${e}`, 'warn');
        }
      }

      // Now try to extract JSON content from the cleaned text
      if (extractionTarget === "auto" || extractionTarget === "json_content") {
        try {
          const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const jsonStr = jsonMatch[0];
            const parsedJson = JSON.parse(jsonStr);
            structured.type = "json";
            structured.data = parsedJson;
            cleaned = JSON.stringify(parsedJson, null, 2);
            return { cleaned, structured };
          }
        } catch (e) {
          // Continue with other extraction methods
        }
      }

      // Remove common AI narration phrases
      const narrationPatterns = [
        /^(here\s+(are|is)|below\s+(are|is)|above\s+(are|is))[^.!?]*[.!?]/gmi,
        /^(these\s+|this\s+|the\s+following)[^.!?]*[.!?]/gmi,
        /^(sample|example|data)[^.!?]*[.!?]/gmi,
        /^(i\s+have|i've)[^.!?]*[.!?]/gmi,
        /^(let\s+me|i\s+will)[^.!?]*[.!?]/gmi,
        /(hope\s+this\s+helps|let\s+me\s+know)[^.!?]*[.!?]/gmi,
      ];

      narrationPatterns.forEach(pattern => {
        cleaned = cleaned.replace(pattern, '');
      });

      // Extract different data types

      // 1. Tables (markdown format)
      const tableMatches = cleaned.match(/\|.*\|[\s\S]*?\|.*\|/g);
      if (tableMatches) {
        structured.type = "table";
        structured.data = tableMatches.map(table => {
          const rows = table.split('\n').filter(row => row.trim() && !row.includes('---'));
          return rows.map(row =>
            row.split('|').map(cell => cell.trim()).filter(cell => cell)
          );
        });
      }

      // 2. Numbered lists
      const numberedListMatches = cleaned.match(/^\d+\.\s*(.+)$/gm);
      if (numberedListMatches && !tableMatches) {
        structured.type = "numbered_list";
        structured.data = numberedListMatches.map(item =>
          item.replace(/^\d+\.\s*/, '').trim()
        );
      }

      // 3. Bullet lists
      const bulletListMatches = cleaned.match(/^[-*•]\s*(.+)$/gm);
      if (bulletListMatches && !tableMatches && !numberedListMatches) {
        structured.type = "bullet_list";
        structured.data = bulletListMatches.map(item =>
          item.replace(/^[-*•]\s*/, '').trim()
        );
      }

      // 4. JSON objects
      const jsonMatches = cleaned.match(/\{[\s\S]*?\}/g);
      if (jsonMatches) {
        structured.type = "json";
        structured.data = jsonMatches.map(json => {
          try {
            return JSON.parse(json);
          } catch {
            return json;
          }
        });
      }

      // 5. Code blocks
      const codeMatches = cleaned.match(/```[\s\S]*?```/g);
      if (codeMatches) {
        structured.type = "code";
        structured.data = codeMatches.map(code =>
          code.replace(/```\w*\n?/, '').replace(/```$/, '').trim()
        );
      }

      // 6. Key-value pairs
      const kvMatches = cleaned.match(/^[^:\n]+:\s*[^:\n]+$/gm);
      if (kvMatches && !tableMatches && !numberedListMatches && !bulletListMatches) {
        structured.type = "key_value";
        structured.data = kvMatches.map(kv => {
          const [key, ...valueParts] = kv.split(':');
          return {
            key: key.trim(),
            value: valueParts.join(':').trim()
          };
        });
      }

      // Clean up the text further
      cleaned = cleaned
        .replace(/```[\s\S]*?```/g, '') // Remove code blocks
        .replace(/\|.*\|[\s\S]*?\|.*\|/g, '') // Remove tables
        .replace(/^\s*[-*•]\s*/gm, '') // Remove bullet points
        .replace(/^\s*\d+\.\s*/gm, '') // Remove numbers
        .replace(/^[^:\n]+:\s*/gm, '') // Remove keys from key-value pairs
        .replace(/\n\s*\n/g, '\n') // Remove extra newlines
        .trim();

      // If no specific structure found, treat as plain text
      if (structured.data.length === 0) {
        structured.type = "text";
        structured.data = cleaned.split('\n').filter(line => line.trim());
      }

      return { cleaned, structured };
    };

    // Process the text
    const { cleaned, structured } = extractRawData(inputText);

    // Calculate processing statistics
    const originalLength = inputText.length;
    const cleanedLength = cleaned.length;
    const compressionRatio = originalLength > 0 ? (cleanedLength / originalLength) * 100 : 0;

    context.log(`Converter processed ${originalLength} chars to ${cleanedLength} chars (${compressionRatio.toFixed(1)}% retained)`);

    // Return comprehensive conversion results
    return {
      cleaned: cleaned,
      text: cleaned,
      data: cleaned,
      value: cleaned,
      structured: structured,
      json: JSON.stringify(structured, null, 2),
      extractionType: structured.type,
      dataCount: Array.isArray(structured.data) ? structured.data.length : 1,
      originalText: inputText,
      originalLength: originalLength,
      cleanedLength: cleanedLength,
      compressionRatio: compressionRatio,
      extractionTarget: extractionTarget,
      hasStructuredData: structured.data.length > 0,
      isJson: structured.type === "json",
      isTable: structured.type === "table",
      isList: structured.type.includes("list"),
      isCode: structured.type === "code",
      isKeyValue: structured.type === "key_value",
      processedAt: new Date().toISOString(),
      metadata: structured.metadata
    };
  },
  inputs: [
    {
      id: 'text',
      name: 'Text Input',
      type: 'string' as const,
      required: true,
      description: 'Text to convert and clean'
    },
    {
      id: 'data',
      name: 'Data Input',
      type: 'string' as const,
      required: false,
      description: 'Data to convert (alternative input)'
    },
    {
      id: 'value',
      name: 'Value Input',
      type: 'string' as const,
      required: false,
      description: 'Value to convert (alternative input)'
    },
    {
      id: 'result',
      name: 'Result Input',
      type: 'string' as const,
      required: false,
      description: 'Result to convert (alternative input)'
    }
  ],
  outputs: [
    {
      id: 'cleaned',
      name: 'Cleaned Text',
      type: 'string' as const,
      description: 'The cleaned and processed text'
    },
    {
      id: 'text',
      name: 'Text Output',
      type: 'string' as const,
      description: 'Same as cleaned text, for compatibility'
    },
    {
      id: 'data',
      name: 'Data Output',
      type: 'string' as const,
      description: 'Same as cleaned text, for compatibility'
    },
    {
      id: 'value',
      name: 'Value Output',
      type: 'string' as const,
      description: 'Same as cleaned text, for compatibility'
    },
    {
      id: 'structured',
      name: 'Structured Data',
      type: 'object' as const,
      description: 'Extracted structured data object'
    },
    {
      id: 'json',
      name: 'JSON Output',
      type: 'string' as const,
      description: 'JSON representation of structured data'
    },
    {
      id: 'extractionType',
      name: 'Extraction Type',
      type: 'string' as const,
      description: 'Type of data extracted (json, table, list, etc.)'
    },
    {
      id: 'dataCount',
      name: 'Data Count',
      type: 'number' as const,
      description: 'Number of data items extracted'
    },
    {
      id: 'compressionRatio',
      name: 'Compression Ratio',
      type: 'number' as const,
      description: 'Percentage of original text retained after cleaning'
    },
    {
      id: 'hasStructuredData',
      name: 'Has Structured Data',
      type: 'boolean' as const,
      description: 'Whether structured data was found'
    },
    {
      id: 'isJson',
      name: 'Is JSON',
      type: 'boolean' as const,
      description: 'Whether the extracted data is JSON'
    },
    {
      id: 'isTable',
      name: 'Is Table',
      type: 'boolean' as const,
      description: 'Whether the extracted data is a table'
    },
    {
      id: 'isList',
      name: 'Is List',
      type: 'boolean' as const,
      description: 'Whether the extracted data is a list'
    },
    {
      id: 'metadata',
      name: 'Processing Metadata',
      type: 'object' as const,
      description: 'Additional metadata about the conversion process'
    }
  ],
  timeout: 15000, // 15 second timeout for complex text processing
  retryable: true, // Can be retried on failure
  maxRetries: 2
};

export default ConverterNode;
