"use client";

import { memo, useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useReactFlow } from "reactflow";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { Clock, Play, Square, Calendar } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type ScheduleType = "interval" | "cron" | "once";

const SchedulerNode = memo(({ data, id }: StandardNodeProps) => {
  const { setNodes } = useReactFlow();
  const [scheduleType, setScheduleType] = useState<ScheduleType>((data as any).scheduleType || "once");
  const [interval, setInterval] = useState<number>((data as any).interval || 5);
  const [intervalUnit, setIntervalUnit] = useState<string>((data as any).intervalUnit || "seconds");
  const [cronExpression, setCronExpression] = useState<string>((data as any).cronExpression || "*/5 * * * *");
  const [scheduleDateTime, setScheduleDateTime] = useState<Date | undefined>(
    (data as any).scheduleDate && (data as any).scheduleTime
      ? new Date(`${(data as any).scheduleDate}T${(data as any).scheduleTime}`)
      : undefined
  );
  const [isScheduled, setIsScheduled] = useState<boolean>(false);
  const [executions, setExecutions] = useState<any[]>([]);
  const [nextExecution, setNextExecution] = useState<Date | null>(null);
  const [activeTab, setActiveTab] = useState<string>("config");
  const [countdown, setCountdown] = useState<string>("");
  const [countdownInterval, setCountdownInterval] = useState<number | null>(null);

  // Update node data when settings change
  useEffect(() => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? {
              ...node,
              data: {
                ...node.data,
                scheduleType,
                interval,
                intervalUnit,
                cronExpression,
                scheduleDate: scheduleDateTime ? scheduleDateTime.toISOString().split('T')[0] : "",
                scheduleTime: scheduleDateTime ? scheduleDateTime.toTimeString().split(' ')[0] : "",
                scheduleDateTime,
                isScheduled,
                executions,
                nextExecution
              }
            }
          : node
      )
    );
  }, [scheduleType, interval, intervalUnit, cronExpression, scheduleDateTime, isScheduled, executions, nextExecution, countdown, id, setNodes]);

  // Handle input changes from connected nodes
  useEffect(() => {
    if (data.inputValue) {
      console.log('[Scheduler Node] Received input:', data.inputValue);
    }
  }, [data.inputValue]);

  // Set default date and time when schedule type changes to "once"
  useEffect(() => {
    if (scheduleType === "once" && !scheduleDateTime) {
      // Set default to current time + 1 minute
      const now = new Date();
      now.setMinutes(now.getMinutes() + 1);
      now.setSeconds(0);
      setScheduleDateTime(now);
    }
  }, [scheduleType, scheduleDateTime]);

  // Countdown timer function
  const startCountdown = (targetDate: Date) => {
    // Clear any existing countdown
    if (countdownInterval) {
      window.clearInterval(countdownInterval);
    }

    const updateCountdown = () => {
      const now = new Date().getTime();
      const target = targetDate.getTime();
      const difference = target - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        let countdownText = "";
        if (days > 0) {
          countdownText = `${days}d ${hours}h ${minutes}m ${seconds}s`;
        } else if (hours > 0) {
          countdownText = `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
          countdownText = `${minutes}m ${seconds}s`;
        } else {
          countdownText = `${seconds}s`;
        }

        setCountdown(countdownText);
      } else {
        setCountdown("Executing...");
        if (countdownInterval) {
          window.clearInterval(countdownInterval);
          setCountdownInterval(null);
        }
      }
    };

    // Update immediately
    updateCountdown();

    // Set up interval to update every second
    const intervalId = window.setInterval(updateCountdown, 1000);
    setCountdownInterval(intervalId);
  };

  const startSchedule = async () => {
    // Validate inputs based on schedule type
    if (scheduleType === "once" && !scheduleDateTime) {
      console.error('[Scheduler Node] Date and time are required for one-time schedule');
      return;
    }

    setIsScheduled(true);
    setExecutions([]);
    setActiveTab("output");

    try {
      switch (scheduleType) {
        case "interval":
          await startIntervalSchedule();
          break;
        case "cron":
          await startCronSchedule();
          break;
        case "once":
          await scheduleOnce();
          break;
      }
    } catch (error) {
      console.error('[Scheduler Node] Execution error:', error);
      setIsScheduled(false);
    }
  };

  const startIntervalSchedule = async () => {
    const intervalMs = getIntervalInMs();
    const nextExecTime = new Date(Date.now() + intervalMs);
    setNextExecution(nextExecTime);

    // Start countdown for interval schedule
    startCountdown(nextExecTime);

    const intervalId = window.setInterval(() => {
      if (!isScheduled) {
        window.clearInterval(intervalId);
        return;
      }

      executeScheduledTask();
      const nextTime = new Date(Date.now() + intervalMs);
      setNextExecution(nextTime);
      startCountdown(nextTime);
    }, intervalMs);

    // Store interval ID for cleanup
    (window as any)[`scheduler_${id}`] = intervalId;
  };

  const startCronSchedule = async () => {
    // Simplified cron implementation - in production, use a proper cron library
    const cronMs = parseCronExpression(cronExpression);
    if (cronMs > 0) {
      const nextExecTime = new Date(Date.now() + cronMs);
      setNextExecution(nextExecTime);

      // Start countdown for cron schedule
      startCountdown(nextExecTime);

      const timeoutId = setTimeout(() => {
        if (isScheduled) {
          executeScheduledTask();
          startCronSchedule(); // Reschedule
        }
      }, cronMs);

      (window as any)[`scheduler_${id}`] = timeoutId;
    }
  };

  const scheduleOnce = async () => {
    if (scheduleDateTime) {
      const now = new Date();

      if (scheduleDateTime > now) {
        setNextExecution(scheduleDateTime);

        // Start countdown for one-time schedule
        startCountdown(scheduleDateTime);

        const delay = scheduleDateTime.getTime() - now.getTime();

        const timeoutId = setTimeout(() => {
          if (isScheduled) {
            executeScheduledTask();
            setIsScheduled(false);
            setCountdown("");
          }
        }, delay);

        (window as any)[`scheduler_${id}`] = timeoutId;
      } else {
        console.error('[Scheduler Node] Scheduled time is in the past');
      }
    }
  };

  const executeScheduledTask = () => {
    const execution = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      input: data.inputValue || "Scheduled execution",
      output: `Scheduled task executed at ${new Date().toLocaleString('en-US', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })}`,
      type: scheduleType
    };

    setExecutions(prev => [execution, ...prev.slice(0, 9)]); // Keep last 10 executions
    updateConnectedNodes(execution);
  };

  const getIntervalInMs = (): number => {
    const multipliers = {
      seconds: 1000,
      minutes: 60 * 1000,
      hours: 60 * 60 * 1000,
      days: 24 * 60 * 60 * 1000
    };
    return interval * (multipliers[intervalUnit as keyof typeof multipliers] || 1000);
  };

  const parseCronExpression = (cron: string): number => {
    // Simplified cron parser - returns milliseconds until next execution
    // In production, use a proper cron library like node-cron
    const parts = cron.split(' ');
    if (parts.length >= 5) {
      const minutes = parts[0];
      if (minutes.startsWith('*/')) {
        const intervalMinutes = parseInt(minutes.substring(2));
        return intervalMinutes * 60 * 1000;
      }
    }
    return 5 * 60 * 1000; // Default to 5 minutes
  };

  const updateConnectedNodes = (execution: any) => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? {
              ...node,
              data: {
                ...node.data,
                outputValue: execution,
                lastExecution: execution
              }
            }
          : node
      )
    );
  };

  const stopSchedule = () => {
    setIsScheduled(false);
    setNextExecution(null);
    setCountdown("");

    // Clear countdown interval
    if (countdownInterval) {
      window.clearInterval(countdownInterval);
      setCountdownInterval(null);
    }

    // Clear any active timers
    const timerId = (window as any)[`scheduler_${id}`];
    if (timerId) {
      window.clearInterval(timerId);
      window.clearTimeout(timerId);
      delete (window as any)[`scheduler_${id}`];
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopSchedule();
      if (countdownInterval) {
        window.clearInterval(countdownInterval);
      }
    };
  }, [countdownInterval]);

  return (
    <div className="bg-background border-2 border-border rounded-lg shadow-sm min-w-[300px]">
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      <div className="p-3">
        <div className="flex items-center gap-2 mb-3">
          <div className="p-1.5 bg-blue-100 dark:bg-blue-900/20 rounded">
            <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="font-medium text-sm">Scheduler</h3>
            <p className="text-xs text-muted-foreground">Automated execution</p>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="config">Config</TabsTrigger>
            <TabsTrigger value="output" className="relative">
              Output
              {isScheduled && (
                <div className="absolute -top-1 -right-1 h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="config" className="space-y-3">
            <div>
              <Label className="text-xs">Schedule Type</Label>
              <Select value={scheduleType} onValueChange={(value: ScheduleType) => setScheduleType(value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="interval">Interval</SelectItem>
                  <SelectItem value="cron">Cron Expression</SelectItem>
                  <SelectItem value="once">One Time</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {scheduleType === "interval" && (
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Every</Label>
                  <Input
                    type="number"
                    value={interval}
                    onChange={(e) => setInterval(parseInt(e.target.value) || 1)}
                    min={1}
                    className="h-8"
                  />
                </div>
                <div>
                  <Label className="text-xs">Unit</Label>
                  <Select value={intervalUnit} onValueChange={setIntervalUnit}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="seconds">Seconds</SelectItem>
                      <SelectItem value="minutes">Minutes</SelectItem>
                      <SelectItem value="hours">Hours</SelectItem>
                      <SelectItem value="days">Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {scheduleType === "cron" && (
              <div>
                <Label className="text-xs">Cron Expression</Label>
                <Input
                  value={cronExpression}
                  onChange={(e) => setCronExpression(e.target.value)}
                  placeholder="*/5 * * * *"
                  className="h-8"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Format: minute hour day month weekday
                </p>
              </div>
            )}

            {scheduleType === "once" && (
              <div>
                <Label className="text-xs">Schedule Date & Time</Label>
                <DateTimePicker
                  date={scheduleDateTime}
                  onDateChange={setScheduleDateTime}
                  placeholder="Pick a date and time"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Select date and time for one-time execution (24-hour format)
                </p>
              </div>
            )}

            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={startSchedule}
                disabled={isScheduled || (scheduleType === "once" && !scheduleDateTime)}
                className="flex-1"
              >
                {isScheduled ? (
                  <>
                    <Clock className="mr-2 h-3 w-3 animate-pulse" />
                    Scheduled
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-3 w-3" />
                    Start Schedule
                  </>
                )}
              </Button>

              {isScheduled && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={stopSchedule}
                >
                  <Square className="h-3 w-3" />
                </Button>
              )}
            </div>

            {nextExecution && (
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground text-center">
                  Next: {nextExecution.toLocaleString('en-US', {
                    hour12: false,
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                  })}
                </div>
                {countdown && (
                  <div className="text-xs font-medium text-center text-primary">
                    ⏱️ {countdown}
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="output" className="min-h-[150px]">
            <div className="space-y-3">
              {/* Countdown Display */}
              {isScheduled && countdown && (
                <div className="p-3 bg-primary/10 border border-primary/20 rounded-lg text-center">
                  <div className="text-sm font-medium text-primary mb-1">Next Execution</div>
                  <div className="text-lg font-mono text-primary">⏱️ {countdown}</div>
                  {nextExecution && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {nextExecution.toLocaleString('en-US', {
                        hour12: false,
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                      })}
                    </div>
                  )}
                </div>
              )}

              {/* Execution History */}
              {executions.length > 0 ? (
                <div>
                  <div className="text-xs font-medium text-muted-foreground mb-2">Execution History</div>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {executions.map((execution) => (
                      <div key={execution.id} className="p-2 bg-muted rounded text-xs">
                        <div className="font-medium">
                          {new Date(execution.timestamp).toLocaleString('en-US', {
                            hour12: false,
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                          })}
                        </div>
                        <div className="text-muted-foreground truncate">{execution.output}</div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : !isScheduled ? (
                <div className="flex items-center justify-center h-full text-muted-foreground text-sm">
                  Configure and start schedule
                </div>
              ) : null}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

SchedulerNode.displayName = "SchedulerNode";

export default SchedulerNode;

// Export execution definition for the workflow engine
export const schedulerExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    const { scheduleType, interval, intervalUnit, cronExpression, scheduleDate, scheduleTime } = config;
    const inputData = inputs.data || inputs.text || inputs.value || "";

    context.log(`Scheduler Node: Setting up ${scheduleType} schedule`);

    // For one-time schedules, wait for the scheduled time
    if (scheduleType === "once" && scheduleDate && scheduleTime) {
      const dateTimeString = `${scheduleDate}T${scheduleTime}`;
      const scheduledDate = new Date(dateTimeString);
      const now = new Date();

      const scheduleInfo = ` scheduled for ${scheduledDate.toLocaleString('en-US', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })}`;

      if (scheduledDate > now) {
        const delay = scheduledDate.getTime() - now.getTime();
        context.log(`Scheduler Node: Waiting ${Math.round(delay / 1000)} seconds until execution${scheduleInfo}`);

        // Wait for the scheduled time
        await new Promise(resolve => setTimeout(resolve, delay));

        context.log(`Scheduler Node: Executing scheduled task${scheduleInfo}`);
      } else {
        context.log(`Scheduler Node: Scheduled time is in the past, executing immediately${scheduleInfo}`);
      }

      const execution = {
        timestamp: new Date().toISOString(),
        input: inputData,
        output: `Scheduled execution completed at ${new Date().toLocaleString('en-US', {
          hour12: false,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })}${scheduleInfo}`,
        type: scheduleType,
        config: { scheduleType, interval, intervalUnit, cronExpression, scheduleDate, scheduleTime }
      };

      return {
        execution,
        timestamp: execution.timestamp,
        data: execution,
        text: execution.output,
        result: execution.output
      };
    }

    // For interval and cron schedules, execute immediately (they should be handled by the UI)
    context.log(`Scheduler Node: ${scheduleType} schedule configured, executing immediately`);

    const execution = {
      timestamp: new Date().toISOString(),
      input: inputData,
      output: `${scheduleType} schedule configured at ${new Date().toLocaleString('en-US', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })}`,
      type: scheduleType,
      config: { scheduleType, interval, intervalUnit, cronExpression, scheduleDate, scheduleTime }
    };

    return {
      execution,
      timestamp: execution.timestamp,
      data: execution,
      text: execution.output,
      result: execution.output
    };
  },
  inputs: [
    { id: 'data', name: 'Input Data', type: 'object' as const, required: false }
  ],
  outputs: [
    { id: 'execution', name: 'Execution Info', type: 'object' as const },
    { id: 'timestamp', name: 'Execution Time', type: 'string' as const },
    { id: 'data', name: 'Execution Data', type: 'object' as const },
    { id: 'text', name: 'Output Text', type: 'string' as const },
    { id: 'result', name: 'Result', type: 'string' as const }
  ],
  timeout: 30000,
  retryable: true
};
