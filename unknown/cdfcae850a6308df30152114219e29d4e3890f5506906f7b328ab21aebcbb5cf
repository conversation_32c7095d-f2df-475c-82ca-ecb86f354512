"use client";

import { useState, memo, useEffect } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Zap } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

/**
 * Sample new node demonstrating the modular architecture
 * This node shows how easy it is to create new nodes with the new system
 */
const SampleNewNode = memo(({ data, id }: StandardNodeProps) => {
  const [inputValue, setInputValue] = useState("");
  const [result, setResult] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // Process the input when it changes
  const processInput = () => {
    if (!inputValue.trim()) return;

    setIsProcessing(true);

    // Simulate some processing
    setTimeout(() => {
      const processed = `Processed: ${inputValue.toUpperCase()}`;
      setResult(processed);

      // Send result to connected nodes
      if (data.onChange) {
        data.onChange(processed);
      }

      setIsProcessing(false);
    }, 1000);
  };

  // Update when receiving input from connected nodes
  useEffect(() => {
    if (data.inputValue) {
      setInputValue(data.inputValue);
    }
  }, [data.inputValue]);

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px]">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <Zap className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Sample Node"}
          </Label>
        </div>

        {/* Input */}
        <div className="space-y-2">
          <Label htmlFor={`input-${id}`} className="text-xs text-muted-foreground">
            Input Text
          </Label>
          <Input
            id={`input-${id}`}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Enter text to process..."
            className="text-sm"
          />
        </div>

        {/* Process Button */}
        <Button
          onClick={processInput}
          disabled={!inputValue.trim() || isProcessing}
          size="sm"
          className="w-full"
        >
          {isProcessing ? "Processing..." : "Process"}
        </Button>

        {/* Result */}
        {result && (
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">Result</Label>
            <div className="p-2 bg-muted rounded text-sm">
              {result}
            </div>
          </div>
        )}

        {/* Connection info */}
        <div className="text-xs text-muted-foreground">
          {data.inputValue ? "📥 Receiving data" : "⏳ Waiting for input"}
        </div>
      </div>

      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

SampleNewNode.displayName = "SampleNewNode";

// Export execution definition for the workflow engine
export const sampleNewExecution = {
  execute: async (inputs: Record<string, any>, config: any, context: any) => {
    // Get input text from either inputs (from connected nodes) or config (node data)
    const inputText = inputs.text || inputs.data || inputs.value || config.inputValue || "";

    // Log execution
    context.log(`Sample New Node processing: "${inputText}"`);

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 100));

    // Process the input (convert to uppercase as example)
    const processed = `Processed: ${inputText.toUpperCase()}`;

    // Log result
    context.log(`Sample New Node result: "${processed}"`);

    // Return processed data
    return {
      result: processed,
      processed: true,
      originalInput: inputText,
      processedLength: processed.length,
      timestamp: new Date().toISOString(),
      processingInfo: {
        method: 'uppercase',
        inputLength: inputText.length,
        outputLength: processed.length
      }
    };
  },
  inputs: [
    {
      id: 'text',
      name: 'Input Text',
      type: 'string' as const,
      required: false,
      description: 'Text to process'
    },
    {
      id: 'data',
      name: 'Input Data',
      type: 'string' as const,
      required: false,
      description: 'Data to process (alternative input)'
    },
    {
      id: 'value',
      name: 'Input Value',
      type: 'string' as const,
      required: false,
      description: 'Value to process (alternative input)'
    }
  ],
  outputs: [
    {
      id: 'result',
      name: 'Processed Result',
      type: 'string' as const,
      description: 'The processed text result'
    },
    {
      id: 'processed',
      name: 'Processing Status',
      type: 'boolean' as const,
      description: 'Whether processing was completed'
    },
    {
      id: 'originalInput',
      name: 'Original Input',
      type: 'string' as const,
      description: 'The original input text'
    },
    {
      id: 'processedLength',
      name: 'Processed Length',
      type: 'number' as const,
      description: 'Length of the processed result'
    },
    {
      id: 'timestamp',
      name: 'Processing Timestamp',
      type: 'string' as const,
      description: 'When the processing occurred'
    },
    {
      id: 'processingInfo',
      name: 'Processing Information',
      type: 'object' as const,
      description: 'Detailed information about the processing'
    }
  ],
  timeout: 5000, // 5 second timeout
  retryable: true, // Can be retried on failure
  maxRetries: 3
};

export default SampleNewNode;
