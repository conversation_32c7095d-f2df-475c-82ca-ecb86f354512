"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Posi<PERSON> } from "reactflow";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { CalculatorIcon, ArrowLeftIcon, ArrowRightIcon, AlertCircleIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type MathOperation = "add" | "subtract" | "multiply" | "divide" | "power" | "sqrt" | "round" | "floor" | "ceil";

/**
 * Math Operation Node - Standardized Structure
 * Perform mathematical operations on numeric input
 */
const MathOperationNode = memo(({ data, id }: StandardNodeProps) => {
  const [operation, setOperation] = useState<MathOperation>("add");
  const [operand, setOperand] = useState("0");
  const [output, setOutput] = useState("");
  const [error, setError] = useState("");

  // Apply the selected math operation to the input
  useEffect(() => {
    // Always process the input, even if it's empty or zero
    let result = "";
    let errorMsg = "";

    try {
      // Parse the input as a number, default to 0 if not provided
      const inputValue = data.inputValue || "0";

      // Try to clean the input if it's not a valid number
      let cleanedInput = inputValue;
      if (typeof inputValue === 'string') {
        // Remove any non-numeric characters except decimal point and minus sign
        cleanedInput = inputValue.replace(/[^\d.-]/g, '');
      }

      const inputNum = parseFloat(cleanedInput);
      const operandNum = parseFloat(operand);

      if (isNaN(inputNum)) {
        throw new Error("Input is not a number");
      }

        let calculatedResult: number;

        switch (operation) {
          case "add":
            calculatedResult = inputNum + operandNum;
            break;
          case "subtract":
            calculatedResult = inputNum - operandNum;
            break;
          case "multiply":
            calculatedResult = inputNum * operandNum;
            break;
          case "divide":
            if (operandNum === 0) {
              throw new Error("Division by zero");
            }
            calculatedResult = inputNum / operandNum;
            break;
          case "power":
            calculatedResult = Math.pow(inputNum, operandNum);
            break;
          case "sqrt":
            if (inputNum < 0) {
              throw new Error("Cannot take square root of negative number");
            }
            calculatedResult = Math.sqrt(inputNum);
            break;
          case "round":
            calculatedResult = Math.round(inputNum);
            break;
          case "floor":
            calculatedResult = Math.floor(inputNum);
            break;
          case "ceil":
            calculatedResult = Math.ceil(inputNum);
            break;
          default:
            calculatedResult = inputNum;
        }

        result = calculatedResult.toString();
        errorMsg = "";
      } catch (e) {
        result = "";
        errorMsg = e instanceof Error ? e.message : "Unknown error";
      }

      setOutput(result);
      setError(errorMsg);

      // Update connected nodes if no error
      if (!errorMsg && data.onChange) {
        data.onChange(result);
      }
  }, [data.inputValue, operation, operand, data.onChange, id]);

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <CalculatorIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Math Operation"}
          </Label>
          {error && (
            <div className="h-2 w-2 rounded-full bg-red-500 ml-auto" title="Error in calculation" />
          )}
          {output && !error && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Calculation complete" />
          )}
        </div>

        {/* Input Indicator */}
        {data.inputValue && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Processing calculation</span>
            <ArrowRightIcon className="h-3 w-3" />
          </div>
        )}

        {/* Math Operation */}
        <div className="space-y-2">
          <Label htmlFor={`math-select-${id}`} className="text-xs text-muted-foreground">
            Math Operation
          </Label>
          <Select
            value={operation}
            onValueChange={(value) => setOperation(value as MathOperation)}
          >
            <SelectTrigger id={`math-select-${id}`} className="text-sm">
              <SelectValue placeholder="Select operation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="add">Add (+)</SelectItem>
              <SelectItem value="subtract">Subtract (-)</SelectItem>
              <SelectItem value="multiply">Multiply (×)</SelectItem>
              <SelectItem value="divide">Divide (÷)</SelectItem>
              <SelectItem value="power">Power (^)</SelectItem>
              <SelectItem value="sqrt">Square Root (√)</SelectItem>
              <SelectItem value="round">Round</SelectItem>
              <SelectItem value="floor">Floor</SelectItem>
              <SelectItem value="ceil">Ceiling</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Operand Input (for operations that need a second value) */}
        {operation !== "sqrt" && operation !== "round" && operation !== "floor" && operation !== "ceil" && (
          <div className="space-y-2">
            <Label htmlFor={`operand-value-${id}`} className="text-xs text-muted-foreground">
              Operand Value
            </Label>
            <Input
              id={`operand-value-${id}`}
              value={operand}
              onChange={(e) => setOperand(e.target.value)}
              placeholder="Enter operand value..."
              className="text-sm"
              type="number"
            />
          </div>
        )}

        {/* Input Display */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Input</Label>
          <Textarea
            value={data.inputValue || ""}
            readOnly
            className="min-h-[60px] max-h-[100px] resize-none text-sm bg-muted/50"
            placeholder="Waiting for numeric input..."
          />
        </div>

        {/* Output Display */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Result</Label>
          {error ? (
            <div className="flex items-center gap-2 p-2 rounded-md bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800">
              <AlertCircleIcon className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-700 dark:text-red-400">{error}</span>
            </div>
          ) : (
            <Textarea
              value={output || ""}
              readOnly
              className="min-h-[60px] max-h-[100px] resize-none text-sm bg-muted/50"
              placeholder="Calculation result will appear here..."
            />
          )}
        </div>
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

MathOperationNode.displayName = "MathOperationNode";

export default MathOperationNode;
