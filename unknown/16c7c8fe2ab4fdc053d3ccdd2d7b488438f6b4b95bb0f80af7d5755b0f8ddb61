
"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Database, Table, ArrowLeftIcon, ArrowRightIcon, AlertCircleIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type QueryType = "SELECT" | "INSERT" | "UPDATE" | "DELETE" | "CUSTOM";
type DatabaseType = "MySQL" | "PostgreSQL" | "SQLite" | "MongoDB";

/**
 * Database Query Node - Standardized Structure
 * Execute SQL queries against various database types
 */
const DatabaseQueryNode = memo(({ data, id }: StandardNodeProps) => {
  const [dbType, setDbType] = useState<DatabaseType>("SQLite");
  const [queryType, setQueryType] = useState<QueryType>("SELECT");
  const [connectionString, setConnectionString] = useState("sqlite://memory");
  const [table, setTable] = useState("users");
  const [columns, setColumns] = useState("*");
  const [whereClause, setWhereClause] = useState("");
  const [customQuery, setCustomQuery] = useState("");
  const [response, setResponse] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState("query");

  // Update connected nodes
  const updateConnectedNodes = (content: string) => {
    if (data.onChange) {
      data.onChange(content);
    }
  };

  // Handle input changes
  useEffect(() => {
    if (data.inputValue && activeTab === "params") {
      try {
        // Try to parse input as JSON parameters
        const params = JSON.parse(data.inputValue);
        if (params.table) setTable(params.table);
        if (params.columns) setColumns(params.columns);
        if (params.where) setWhereClause(params.where);
        if (params.query) setCustomQuery(params.query);
      } catch (err) {
        // If not valid JSON, use as table name
        setTable(data.inputValue);
      }
    }
  }, [data.inputValue, activeTab]);

  // Generate query based on selected type
  const generateQuery = (): string => {
    switch (queryType) {
      case "SELECT":
        return `SELECT ${columns} FROM ${table}${whereClause ? ` WHERE ${whereClause}` : ''}`;
      case "INSERT":
        return `INSERT INTO ${table} VALUES (...)`;
      case "UPDATE":
        return `UPDATE ${table} SET ...${whereClause ? ` WHERE ${whereClause}` : ''}`;
      case "DELETE":
        return `DELETE FROM ${table}${whereClause ? ` WHERE ${whereClause}` : ''}`;
      case "CUSTOM":
        return customQuery;
      default:
        return "";
    }
  };

  // Execute query (simulated)
  const executeQuery = async () => {
    setIsLoading(true);
    setError("");

    try {
      const query = generateQuery();

      // In a real implementation, this would connect to an actual database
      // For demo purposes, we'll simulate a response
      await new Promise(resolve => setTimeout(resolve, 1000));

      let simulatedResponse;

      if (queryType === "SELECT") {
        simulatedResponse = JSON.stringify([
          { id: 1, name: "John Doe", email: "<EMAIL>" },
          { id: 2, name: "Jane Smith", email: "<EMAIL>" },
          { id: 3, name: "Bob Johnson", email: "<EMAIL>" }
        ], null, 2);
      } else if (queryType === "INSERT") {
        simulatedResponse = JSON.stringify({
          success: true,
          message: "1 row inserted successfully",
          lastInsertId: 4
        }, null, 2);
      } else if (queryType === "UPDATE") {
        simulatedResponse = JSON.stringify({
          success: true,
          message: "2 rows updated successfully"
        }, null, 2);
      } else if (queryType === "DELETE") {
        simulatedResponse = JSON.stringify({
          success: true,
          message: "1 row deleted successfully"
        }, null, 2);
      } else {
        simulatedResponse = JSON.stringify({
          success: true,
          message: "Query executed successfully",
          results: [{ result: "Custom query result" }]
        }, null, 2);
      }

      setResponse(simulatedResponse);
      updateConnectedNodes(simulatedResponse);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      setResponse("");
      updateConnectedNodes("");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[320px]">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium flex items-center gap-1">
            <Database className="h-4 w-4" />
            {data.label || "Database Query"}
          </Label>

          <Select value={dbType} onValueChange={(value) => setDbType(value as DatabaseType)}>
            <SelectTrigger className="h-7 w-[120px]">
              <SelectValue placeholder="Database" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="SQLite">SQLite</SelectItem>
              <SelectItem value="MySQL">MySQL</SelectItem>
              <SelectItem value="PostgreSQL">PostgreSQL</SelectItem>
              <SelectItem value="MongoDB">MongoDB</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="query">Query</TabsTrigger>
            <TabsTrigger value="params">Parameters</TabsTrigger>
          </TabsList>

          <TabsContent value="query" className="space-y-2">
            <div className="flex items-center gap-2 mt-2">
              <Select value={queryType} onValueChange={(value) => setQueryType(value as QueryType)}>
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder="Query Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SELECT">SELECT</SelectItem>
                  <SelectItem value="INSERT">INSERT</SelectItem>
                  <SelectItem value="UPDATE">UPDATE</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                  <SelectItem value="CUSTOM">CUSTOM</SelectItem>
                </SelectContent>
              </Select>

              <Input
                value={table}
                onChange={(e) => setTable(e.target.value)}
                placeholder="Table name"
                className="flex-1"
              />
            </div>

            {queryType === "CUSTOM" ? (
              <Textarea
                value={customQuery}
                onChange={(e) => setCustomQuery(e.target.value)}
                placeholder="Enter custom SQL query..."
                className="min-h-[80px]"
              />
            ) : (
              <>
                {queryType === "SELECT" && (
                  <Input
                    value={columns}
                    onChange={(e) => setColumns(e.target.value)}
                    placeholder="Columns (e.g. id, name, email)"
                  />
                )}

                {(queryType === "SELECT" || queryType === "UPDATE" || queryType === "DELETE") && (
                  <Input
                    value={whereClause}
                    onChange={(e) => setWhereClause(e.target.value)}
                    placeholder="WHERE clause (e.g. id > 10)"
                  />
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value="params" className="space-y-2">
            <div className="text-xs text-muted-foreground mb-2">
              Connection parameters or input from connected nodes
            </div>
            <Input
              value={connectionString}
              onChange={(e) => setConnectionString(e.target.value)}
              placeholder="Connection string"
            />
            <div className="text-xs text-muted-foreground mt-1">
              Input data: {data.inputValue ? `${data.inputValue.substring(0, 50)}${data.inputValue.length > 50 ? '...' : ''}` : 'None'}
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex items-center gap-2 mt-2">
          <Button
            size="sm"
            onClick={executeQuery}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isLoading ? "Executing..." : "Execute Query"}
          </Button>
        </div>

        {error && (
          <div className="p-2 bg-destructive/10 text-destructive rounded-md text-xs mt-2">
            {error}
          </div>
        )}

        {response && (
          <div className="mt-2">
            <Label className="text-xs text-muted-foreground flex items-center gap-1">
              <Table className="h-3 w-3" />
              Results
            </Label>
            <pre className="p-2 bg-muted rounded-md text-xs mt-1 max-h-[120px] overflow-y-auto whitespace-pre-wrap break-words">
              {response}
            </pre>
          </div>
        )}
      </div>

      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

DatabaseQueryNode.displayName = "DatabaseQueryNode";

export default DatabaseQueryNode;
