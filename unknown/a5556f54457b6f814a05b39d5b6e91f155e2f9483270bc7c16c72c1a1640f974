import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

async function fixChecksums() {
  console.log('🔧 Fixing node code checksums...\n');

  try {
    // Get all node codes
    const nodeCodes = await prisma.nodeCode.findMany();

    console.log(`Found ${nodeCodes.length} node codes to check:`);

    for (const nodeCode of nodeCodes) {
      // Calculate the correct checksum
      const correctChecksum = crypto.createHash('sha256').update(nodeCode.code).digest('hex');
      
      console.log(`\n📦 Node: ${nodeCode.nodeId} v${nodeCode.version}`);
      console.log(`   Current checksum: ${nodeCode.checksum}`);
      console.log(`   Correct checksum: ${correctChecksum}`);
      
      if (nodeCode.checksum !== correctChecksum) {
        console.log(`   ❌ Checksum mismatch! Updating...`);
        
        await prisma.nodeCode.update({
          where: { id: nodeCode.id },
          data: { checksum: correctChecksum }
        });
        
        console.log(`   ✅ Updated checksum`);
      } else {
        console.log(`   ✅ Checksum is correct`);
      }
    }

    console.log('\n🎉 Checksum fix completed!');

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixChecksums();
