'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Home, ArrowLeft, Lock } from 'lucide-react';

export default function Forbidden() {
  const router = useRouter();

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-orange-100 dark:bg-orange-900/20 p-4">
              <Shield className="h-12 w-12 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
          <CardTitle className="text-3xl font-bold">403 - Access Forbidden</CardTitle>
          <CardDescription className="text-base">
            You don't have permission to access this resource
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <div className="flex justify-center mb-4">
            <Lock className="h-16 w-16 text-muted-foreground/50" />
          </div>
          <p className="text-muted-foreground mb-6">
            This page is restricted and requires special permissions to access.
          </p>
          <div className="bg-muted/50 p-4 rounded-lg">
            <p className="text-sm text-muted-foreground">
              If you believe you should have access to this page, please:
            </p>
            <ul className="text-sm text-muted-foreground text-left mt-2 space-y-1">
              <li>• Check that you're logged in with the correct account</li>
              <li>• Verify your subscription or membership status</li>
              <li>• Contact support if you need additional permissions</li>
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center gap-3">
          <Button 
            variant="outline" 
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Go Back
          </Button>
          <Button asChild className="flex items-center gap-2">
            <Link href="/">
              <Home className="h-4 w-4" />
              Go Home
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
