"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ArrowDownIcon, MonitorIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

/**
 * Output Node - Standardized Structure
 * Display data received from connected nodes
 */
const OutputNode = memo(({ data, id }: StandardNodeProps) => {
  // Track if we have content to display
  const [hasContent, setHasContent] = useState(false);
  // Track if we just received new content for animation
  const [isNewContent, setIsNewContent] = useState(false);

  // Update content state when input value changes
  useEffect(() => {
    if (data.inputValue) {
      setHasContent(true);
      // Trigger animation when new content arrives
      setIsNewContent(true);
      const timer = setTimeout(() => setIsNewContent(false), 500);
      return () => clearTimeout(timer);
    } else {
      setHasContent(false);
    }
  }, [data.inputValue]);

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <MonitorIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Output"}
          </Label>
          {hasContent && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Displaying data" />
          )}
        </div>

        {/* Input Indicator */}
        {hasContent && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowDownIcon className={`h-3 w-3 ${isNewContent ? 'animate-bounce' : ''}`} />
            <span>Receiving data</span>
          </div>
        )}

        {/* Content Display */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">
            Output Display
          </Label>
          {hasContent ? (
            <Textarea
              id={`output-${id}`}
              value={data.inputValue || ""}
              readOnly
              className="min-h-[120px] max-h-[200px] resize-none text-sm font-mono bg-muted/50"
            />
          ) : (
            <div className="flex items-center justify-center h-[120px] border border-dashed rounded-md bg-muted/50 text-muted-foreground text-sm">
              <div className="text-center">
                <MonitorIcon className="h-6 w-6 mx-auto mb-2 opacity-50" />
                <p>Waiting for input data...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

OutputNode.displayName = "OutputNode";

export default OutputNode;
