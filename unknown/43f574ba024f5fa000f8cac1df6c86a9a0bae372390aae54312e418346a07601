"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FilterIcon, ArrowLeftIcon, ArrowRightIcon, CheckCircleIcon, XCircleIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type FilterOperation = "contains" | "startsWith" | "endsWith" | "equals" | "notEquals" | "regex";

/**
 * Filter Node - Standardized Structure
 * Filter text data based on various conditions
 */
const FilterNode = memo(({ data, id }: StandardNodeProps) => {
  const [operation, setOperation] = useState<FilterOperation>("contains");
  const [filterValue, setFilterValue] = useState("");
  const [output, setOutput] = useState("");
  const [passed, setPassed] = useState(false);

  // Apply the selected filter to the input
  useEffect(() => {
    if (data.inputValue) {
      let result = "";
      let filterPassed = false;

      switch (operation) {
        case "contains":
          filterPassed = data.inputValue.includes(filterValue);
          break;
        case "startsWith":
          filterPassed = data.inputValue.startsWith(filterValue);
          break;
        case "endsWith":
          filterPassed = data.inputValue.endsWith(filterValue);
          break;
        case "equals":
          filterPassed = data.inputValue === filterValue;
          break;
        case "notEquals":
          filterPassed = data.inputValue !== filterValue;
          break;
        case "regex":
          try {
            const regex = new RegExp(filterValue);
            filterPassed = regex.test(data.inputValue);
          } catch (e) {
            filterPassed = false;
          }
          break;
        default:
          filterPassed = false;
      }

      // Only pass the input value if the filter passes
      result = filterPassed ? data.inputValue : "";
      setOutput(result);
      setPassed(filterPassed);

      // Update connected nodes
      if (data.onChange) {
        data.onChange(result);
      }
    }
  }, [data.inputValue, operation, filterValue, data.onChange]);

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <FilterIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Filter"}
          </Label>
          {data.inputValue && (
            <div className={`h-2 w-2 rounded-full ml-auto ${passed ? 'bg-green-500' : 'bg-red-500'}`}
                 title={passed ? 'Filter passed' : 'Filter failed'} />
          )}
        </div>

        {/* Input Indicator */}
        {data.inputValue && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Filtering input</span>
            {passed ? (
              <CheckCircleIcon className="h-3 w-3 text-green-500" />
            ) : (
              <XCircleIcon className="h-3 w-3 text-red-500" />
            )}
            <ArrowRightIcon className="h-3 w-3" />
          </div>
        )}

        {/* Filter Configuration */}
        <div className="space-y-2">
          <Label htmlFor={`filter-select-${id}`} className="text-xs text-muted-foreground">
            Filter Operation
          </Label>
          <Select
            value={operation}
            onValueChange={(value) => setOperation(value as FilterOperation)}
          >
            <SelectTrigger id={`filter-select-${id}`} className="text-sm">
              <SelectValue placeholder="Select operation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="contains">Contains</SelectItem>
              <SelectItem value="startsWith">Starts With</SelectItem>
              <SelectItem value="endsWith">Ends With</SelectItem>
              <SelectItem value="equals">Equals</SelectItem>
              <SelectItem value="notEquals">Not Equals</SelectItem>
              <SelectItem value="regex">Regex Pattern</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Filter Value */}
        <div className="space-y-2">
          <Label htmlFor={`filter-value-${id}`} className="text-xs text-muted-foreground">
            Filter Value
          </Label>
          <Input
            id={`filter-value-${id}`}
            value={filterValue}
            onChange={(e) => setFilterValue(e.target.value)}
            placeholder={operation === "regex" ? "Enter regex pattern..." : "Enter filter value..."}
            className="text-sm"
          />
        </div>

        {/* Input Display */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Input</Label>
          <Textarea
            value={data.inputValue || ""}
            readOnly
            className="min-h-[60px] max-h-[100px] resize-none text-sm bg-muted/50"
            placeholder="Waiting for input..."
          />
        </div>

        {/* Filter Status */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Filter Result</Label>
          <div className="flex items-center gap-2 p-2 rounded-md bg-muted/50">
            {passed ? (
              <>
                <CheckCircleIcon className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-700 dark:text-green-400">Filter Passed</span>
              </>
            ) : (
              <>
                <XCircleIcon className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-700 dark:text-red-400">Filter Failed</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

FilterNode.displayName = "FilterNode";

export default FilterNode;
