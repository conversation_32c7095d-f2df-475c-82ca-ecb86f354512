"use client";


import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import WorkflowProvider from "@/components/workflow/workflow-provider";
import { Loader2, Maximize, Minimize } from "lucide-react";
import { use } from "react";
import { AppSidebar } from "@/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"

interface Workflow {
  id: string;
  name: string;
  description: string | null;
  nodes: any[];
  edges: any[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

export default function WorkflowCanvasPage({ params }: { params: Promise<{ id: string }> }) {
  // Use React.use to unwrap the params Promise in a client component
  const { id } = use(params);
  console.log("Workflow Canvas Page - Received ID:", id);

  const router = useRouter();
  const [workflow, setWorkflow] = useState<Workflow | null>(null);
  const [isLoadingWorkflow, setIsLoadingWorkflow] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Fetch workflow when component mounts
  useEffect(() => {
    if (id) {
      fetchWorkflow();
    }
  }, [id]);

  const fetchWorkflow = async () => {
    console.log("Fetching workflow with ID:", id);
    setIsLoadingWorkflow(true);
    setError(null);

    try {
      const apiUrl = `/api/workflow/${id}`;
      console.log("Fetching from API URL:", apiUrl);

      const response = await fetch(apiUrl);
      console.log("API response status:", response.status);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Workflow not found");
        }
        const data = await response.json();
        throw new Error(data.error || "Failed to fetch workflow");
      }

      const data = await response.json();
      console.log("Received workflow data:", data);

      // Parse nodes and edges from JSON strings
      const parsedWorkflow = {
        ...data,
        nodes: JSON.parse(data.nodes || '[]'),
        edges: JSON.parse(data.edges || '[]')
      };

      console.log("Parsed workflow:", parsedWorkflow);
      setWorkflow(parsedWorkflow);
    } catch (error) {
      console.error("Error fetching workflow:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch workflow");
    } finally {
      setIsLoadingWorkflow(false);
    }
  };



  // Show loading state
  if (isLoadingWorkflow) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/workflow-manager">Workflows</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Loading...</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Loading workflow...</p>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  // Show error state
  if (error) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/workflow-manager">Workflows</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Error</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <div className="bg-destructive/10 border border-destructive text-destructive px-6 py-4 rounded-md max-w-md text-center">
              <h2 className="font-semibold mb-2">Error</h2>
              <p>{error}</p>
              <Button
                className="mt-4"
                onClick={() => router.push("/workflow-manager")}
              >
                Back to Workflow Manager
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  // Show workflow not found state
  if (!workflow) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/workflow-manager">Workflows</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Not Found</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <div className="bg-muted/50 border border-border px-6 py-4 rounded-md max-w-md text-center">
              <h2 className="font-semibold mb-2">Workflow Not Found</h2>
              <p className="text-muted-foreground">The workflow you're looking for doesn't exist or you don't have permission to view it.</p>
              <Button
                className="mt-4"
                onClick={() => router.push("/workflow-manager")}
              >
                Back to Workflow Manager
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  // Add a try-catch block to handle any rendering errors
  try {
    console.log("Rendering workflow canvas with workflow:", workflow);

    // Fullscreen mode - just the canvas
    if (isFullscreen) {
      return (
        <div className="h-screen w-screen overflow-hidden relative">
          <Button
            variant="outline"
            size="sm"
            className="absolute top-4 right-4 z-50 bg-background/80 backdrop-blur-sm"
            onClick={() => setIsFullscreen(false)}
          >
            <Minimize className="h-4 w-4 mr-2" />
            Exit Fullscreen
          </Button>
          <WorkflowProvider initialWorkflow={workflow} />
        </div>
      );
    }

    // Sidebar mode - with navigation
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/workflow-manager">Workflows</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>{workflow.name}</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
            <div className="ml-auto px-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(true)}
              >
                <Maximize className="h-4 w-4 mr-2" />
                Fullscreen
              </Button>
            </div>
          </header>
          <div className="flex-1 overflow-hidden">
            <WorkflowProvider initialWorkflow={workflow} />
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  } catch (error) {
    console.error("Error rendering WorkflowProvider:", error);

    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/workflow-manager">Workflows</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Error</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <div className="bg-destructive/10 border border-destructive text-destructive px-6 py-4 rounded-md max-w-md text-center">
              <h2 className="font-semibold mb-2">Error Loading Workflow</h2>
              <p>{error instanceof Error ? error.message : "An unexpected error occurred"}</p>
              <Button
                className="mt-4"
                onClick={() => router.push("/workflow-manager")}
              >
                Back to Workflow Manager
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }
}
