"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Workflow, 
  Zap, 
  Shield, 
  Users, 
  BarChart3, 
  Puzzle,
  Clock,
  Globe,
  Code
} from "lucide-react";
import { motion } from "framer-motion";

const features = [
  {
    icon: Workflow,
    title: "Visual Workflow Builder",
    description: "Drag and drop interface to create complex automation workflows without coding."
  },
  {
    icon: Zap,
    title: "Lightning Fast Execution",
    description: "Optimized runtime engine ensures your workflows execute quickly and reliably."
  },
  {
    icon: Puzzle,
    title: "500+ Integrations",
    description: "Connect with your favorite tools and services through our extensive integration library."
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-grade security with SOC 2 compliance and end-to-end encryption."
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Share workflows, collaborate in real-time, and manage permissions across your team."
  },
  {
    icon: Bar<PERSON>hart3,
    title: "Advanced Analytics",
    description: "Monitor performance, track usage, and optimize your workflows with detailed insights."
  },
  {
    icon: Clock,
    title: "Smart Scheduling",
    description: "Schedule workflows to run at specific times or trigger them based on events."
  },
  {
    icon: Globe,
    title: "Global Infrastructure",
    description: "Distributed across multiple regions for maximum reliability and performance."
  },
  {
    icon: Code,
    title: "Custom Code Support",
    description: "Extend functionality with custom JavaScript, Python, or API integrations."
  }
];

export function FeaturesSection() {
  return (
    <section className="py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge variant="secondary" className="mb-4">
            Features
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            Everything you need to automate
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Powerful features designed to help you build, deploy, and scale your automation workflows.
          </p>
        </motion.div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-0 shadow-none bg-transparent">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">
              Ready to transform your workflow?
            </h3>
            <p className="text-muted-foreground mb-6">
              Join thousands of teams already automating their processes with our platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Badge variant="secondary" className="px-4 py-2">
                <Users className="w-4 h-4 mr-2" />
                10,000+ active users
              </Badge>
              <Badge variant="secondary" className="px-4 py-2">
                <Zap className="w-4 h-4 mr-2" />
                1M+ workflows executed
              </Badge>
              <Badge variant="secondary" className="px-4 py-2">
                <Shield className="w-4 h-4 mr-2" />
                99.9% uptime
              </Badge>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
