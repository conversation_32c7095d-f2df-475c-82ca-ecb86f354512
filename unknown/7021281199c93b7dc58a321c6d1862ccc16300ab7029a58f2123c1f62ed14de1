"use client";

import * as React from "react";
import {
  BookOpen,
  Code,
  Database,
  FileText,
  Activity,
  FunctionSquare,
  GitBranch,
  LayoutDashboard,
  Network,
  Settings2,
  Text as TextIcon,
  Wand2,
  Upload,
  Bar<PERSON>hart,
  Globe,
  Hash,
} from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar";

// Workflow node types data
const nodeTypes = [
  {
    title: "Input Nodes",
    icon: TextIcon,
    isActive: true,
    items: [
      {
        title: "Text Input",
        type: "textInput",
        description: "Enter text manually",
      },
      {
        title: "Number Input",
        type: "numberInput",
        description: "Enter numeric values",
        icon: Hash,
      },
      {
        title: "File Input",
        type: "fileInput",
        description: "Upload and process files",
        icon: Upload,
      },
    ],
  },
  {
    title: "Transform Nodes",
    icon: Wand2,
    items: [
      {
        title: "Text Transform",
        type: "transform",
        description: "Transform text (uppercase, lowercase, etc.)",
      },
      {
        title: "Filter",
        type: "filter",
        description: "Filter data based on conditions",
      },
      {
        title: "Math Operation",
        type: "mathOperation",
        description: "Perform mathematical operations",
        icon: Hash,
      },
    ],
  },
  {
    title: "Output Nodes",
    icon: FileText,
    items: [
      {
        title: "Text Output",
        type: "output",
        description: "Display text output",
      },
      {
        title: "Chart Output",
        type: "chartOutput",
        description: "Visualize data with charts",
        icon: BarChart,
      },
    ],
  },
  {
    title: "Advanced",
    icon: FunctionSquare,
    items: [
      {
        title: "API Request",
        type: "apiRequest",
        description: "Make HTTP requests to APIs",
        icon: Globe,
      },
      {
        title: "Code Execution",
        type: "codeExecution",
        description: "Execute custom JavaScript code",
        icon: Code,
      },
    ],
  },
];

// Saved workflows data
const savedWorkflows = [
  {
    name: "Text Processing",
    icon: Activity,
  },
  {
    name: "Data Analysis",
    icon: Database,
  },
  {
    name: "API Integration",
    icon: Network,
  },
];

// Documentation links
const documentationLinks = [
  {
    title: "Getting Started",
    url: "#",
  },
  {
    title: "Node Types",
    url: "#",
  },
  {
    title: "Examples",
    url: "#",
  },
  {
    title: "API Reference",
    url: "#",
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center gap-2 px-4 py-2">
          <Activity className="h-6 w-6" />
          <span className="text-lg font-semibold">Workflow Builder</span>
        </div>
      </SidebarHeader>
      <SidebarContent>
        {/* Saved Workflows */}
        <SidebarGroup>
          <SidebarGroupLabel>Saved Workflows</SidebarGroupLabel>
          <SidebarMenu>
            {savedWorkflows.length > 0 ? (
              savedWorkflows.map((workflow) => (
                <SidebarMenuItem key={workflow.name}>
                  <SidebarMenuButton asChild>
                    <button className="w-full text-left">
                      <workflow.icon className="h-4 w-4" />
                      <span>{workflow.name}</span>
                    </button>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))
            ) : (
              <SidebarMenuItem>
                <div className="px-4 py-2 text-sm text-muted-foreground">
                  No saved workflows yet. Create and save a workflow to see it here.
                </div>
              </SidebarMenuItem>
            )}
          </SidebarMenu>
        </SidebarGroup>

        {/* Templates */}
        <SidebarGroup>
          <SidebarGroupLabel>Templates</SidebarGroupLabel>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <button className="w-full text-left">
                  <FileText className="h-4 w-4" />
                  <span>Text Processing</span>
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <button className="w-full text-left">
                  <BarChart className="h-4 w-4" />
                  <span>Data Visualization</span>
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <button className="w-full text-left">
                  <Globe className="h-4 w-4" />
                  <span>API Integration</span>
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>

        {/* Documentation */}
        <SidebarGroup>
          <SidebarGroupLabel>Documentation</SidebarGroupLabel>
          <SidebarMenu>
            <Collapsible asChild className="group/collapsible">
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton tooltip="Documentation">
                    <BookOpen className="h-4 w-4" />
                    <span>Documentation</span>
                    <GitBranch className="ml-auto h-4 w-4 rotate-90 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-180" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {documentationLinks.map((link) => (
                      <SidebarMenuSubItem key={link.title}>
                        <SidebarMenuSubButton asChild>
                          <a href={link.url}>
                            <span>{link.title}</span>
                          </a>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t border-sidebar-border p-4">
        <SidebarMenuButton asChild>
          <button className="w-full">
            <Settings2 className="h-4 w-4" />
            <span>Settings</span>
          </button>
        </SidebarMenuButton>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
