"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { WandIcon, ArrowLeftIcon, ArrowRightIcon } from "lucide-react";
import { StandardNodeProps } from "@/lib/workflow/node-interface";

type TransformOperation = "uppercase" | "lowercase" | "capitalize" | "reverse" | "trim";

/**
 * Transform Node - Standardized Structure
 * Transform text data with various operations
 */
const TransformNode = memo(({ data, id }: StandardNodeProps) => {
  const [operation, setOperation] = useState<TransformOperation>("uppercase");
  const [output, setOutput] = useState("");

  // Apply the selected transformation to the input
  useEffect(() => {
    if (data.inputValue) {
      let result = data.inputValue;

      switch (operation) {
        case "uppercase":
          result = data.inputValue.toUpperCase();
          break;
        case "lowercase":
          result = data.inputValue.toLowerCase();
          break;
        case "capitalize":
          result = data.inputValue
            .split(" ")
            .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(" ");
          break;
        case "reverse":
          result = data.inputValue.split("").reverse().join("");
          break;
        case "trim":
          result = data.inputValue.trim();
          break;
        default:
          result = data.inputValue;
      }

      setOutput(result);

      // Update connected nodes
      if (data.onChange) {
        data.onChange(result);
      }
    }
  }, [data.inputValue, operation, data.onChange]);

  return (
    <div className="p-4 border rounded-md shadow-sm bg-background text-foreground w-[280px] transition-shadow hover:shadow-md">
      <div className="flex flex-col gap-3">
        {/* Header */}
        <div className="flex items-center gap-2">
          <WandIcon className="h-4 w-4 text-primary" />
          <Label className="text-sm font-medium">
            {data.label || "Transform"}
          </Label>
          {output && (
            <div className="h-2 w-2 rounded-full bg-green-500 ml-auto" title="Transformed" />
          )}
        </div>

        {/* Input Indicator */}
        {data.inputValue && (
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <ArrowLeftIcon className="h-3 w-3" />
            <span>Processing input</span>
            <ArrowRightIcon className="h-3 w-3" />
          </div>
        )}

        {/* Transform Operation */}
        <div className="space-y-2">
          <Label htmlFor={`transform-select-${id}`} className="text-xs text-muted-foreground">
            Transform Operation
          </Label>
          <Select
            value={operation}
            onValueChange={(value) => setOperation(value as TransformOperation)}
          >
            <SelectTrigger id={`transform-select-${id}`} className="text-sm">
              <SelectValue placeholder="Select operation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="uppercase">UPPERCASE</SelectItem>
              <SelectItem value="lowercase">lowercase</SelectItem>
              <SelectItem value="capitalize">Capitalize</SelectItem>
              <SelectItem value="reverse">Reverse</SelectItem>
              <SelectItem value="trim">Trim</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Input Display */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Input</Label>
          <Textarea
            value={data.inputValue || ""}
            readOnly
            className="min-h-[60px] max-h-[100px] resize-none text-sm bg-muted/50"
            placeholder="Waiting for input..."
          />
        </div>

        {/* Output Display */}
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Output</Label>
          <Textarea
            value={output || ""}
            readOnly
            className="min-h-[60px] max-h-[100px] resize-none text-sm bg-muted/50"
            placeholder="Transformed output will appear here..."
          />
        </div>
      </div>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary border-2 border-background"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary border-2 border-background"
      />
    </div>
  );
});

TransformNode.displayName = "TransformNode";

export default TransformNode;
