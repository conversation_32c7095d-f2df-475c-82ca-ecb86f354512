"use client";

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, Gift, Zap } from 'lucide-react';

interface FreePlanActivationProps {
  isOpen: boolean;
  onClose: () => void;
  onActivate: () => Promise<void>;
}

export function FreePlanActivation({ isOpen, onClose, onActivate }: FreePlanActivationProps) {
  const [isActivating, setIsActivating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleActivate = async () => {
    setIsActivating(true);
    setError(null);

    try {
      await onActivate();
      setSuccess(true);
      setTimeout(() => {
        onClose();
        setSuccess(false);
      }, 2000);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsActivating(false);
    }
  };

  if (success) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center py-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Welcome to the Free Plan!</h3>
            <p className="text-muted-foreground">
              Your free plan has been activated successfully
            </p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Gift className="h-5 w-5 mr-2 text-green-500" />
            Activate Free Plan
          </DialogTitle>
          <DialogDescription>
            Get started with our free plan and explore the marketplace
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Free Plan Benefits */}
          <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 dark:text-green-200 mb-3">
              What's included in the Free Plan:
            </h4>
            <ul className="space-y-2 text-sm text-green-700 dark:text-green-300">
              <li className="flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Access to free community nodes
              </li>
              <li className="flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Create up to 3 workflows
              </li>
              <li className="flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Basic workflow execution (100/month)
              </li>
              <li className="flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Community support
              </li>
              <li className="flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Standard node library
              </li>
            </ul>
          </div>

          {/* Upgrade Notice */}
          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
            <div className="flex items-center mb-2">
              <Zap className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
              <span className="font-medium text-blue-800 dark:text-blue-200">
                Ready to upgrade?
              </span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              You can upgrade to a premium plan anytime to unlock unlimited workflows, 
              premium nodes, and priority support.
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isActivating}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleActivate}
              disabled={isActivating}
              className="flex-1"
            >
              {isActivating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Activating...
                </>
              ) : (
                <>
                  <Gift className="h-4 w-4 mr-2" />
                  Activate Free Plan
                </>
              )}
            </Button>
          </div>

          {/* Terms Notice */}
          <p className="text-xs text-muted-foreground text-center">
            By activating the free plan, you agree to our{' '}
            <a href="/terms" className="underline hover:text-foreground">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" className="underline hover:text-foreground">
              Privacy Policy
            </a>
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
